{"version": 3, "file": "CatchBoundary.cjs", "sources": ["../../src/CatchBoundary.tsx"], "sourcesContent": ["import * as React from 'react'\nimport type { ErrorRouteComponent } from './route'\nimport type { ErrorInfo } from 'react'\n\nexport function CatchBoundary(props: {\n  getResetKey: () => number | string\n  children: React.ReactNode\n  errorComponent?: ErrorRouteComponent\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n}) {\n  const errorComponent = props.errorComponent ?? ErrorComponent\n\n  return (\n    <CatchBoundaryImpl\n      getResetKey={props.getResetKey}\n      onCatch={props.onCatch}\n      children={({ error, reset }) => {\n        if (error) {\n          return React.createElement(errorComponent, {\n            error,\n            reset,\n          })\n        }\n\n        return props.children\n      }}\n    />\n  )\n}\n\nclass CatchBoundaryImpl extends React.Component<{\n  getResetKey: () => number | string\n  children: (props: {\n    error: Error | null\n    reset: () => void\n  }) => React.ReactNode\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n}> {\n  state = { error: null } as { error: Error | null; resetKey: string }\n  static getDerivedStateFromProps(props: any) {\n    return { resetKey: props.getResetKey() }\n  }\n  static getDerivedStateFromError(error: Error) {\n    return { error }\n  }\n  reset() {\n    this.setState({ error: null })\n  }\n  componentDidUpdate(\n    prevProps: Readonly<{\n      getResetKey: () => string\n      children: (props: { error: any; reset: () => void }) => any\n      onCatch?: ((error: any, info: any) => void) | undefined\n    }>,\n    prevState: any,\n  ): void {\n    if (prevState.error && prevState.resetKey !== this.state.resetKey) {\n      this.reset()\n    }\n  }\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    if (this.props.onCatch) {\n      this.props.onCatch(error, errorInfo)\n    }\n  }\n  render() {\n    // If the resetKey has changed, don't render the error\n    return this.props.children({\n      error:\n        this.state.resetKey !== this.props.getResetKey()\n          ? null\n          : this.state.error,\n      reset: () => {\n        this.reset()\n      },\n    })\n  }\n}\n\nexport function ErrorComponent({ error }: { error: any }) {\n  const [show, setShow] = React.useState(process.env.NODE_ENV !== 'production')\n\n  return (\n    <div style={{ padding: '.5rem', maxWidth: '100%' }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '.5rem' }}>\n        <strong style={{ fontSize: '1rem' }}>Something went wrong!</strong>\n        <button\n          style={{\n            appearance: 'none',\n            fontSize: '.6em',\n            border: '1px solid currentColor',\n            padding: '.1rem .2rem',\n            fontWeight: 'bold',\n            borderRadius: '.25rem',\n          }}\n          onClick={() => setShow((d) => !d)}\n        >\n          {show ? 'Hide Error' : 'Show Error'}\n        </button>\n      </div>\n      <div style={{ height: '.25rem' }} />\n      {show ? (\n        <div>\n          <pre\n            style={{\n              fontSize: '.7em',\n              border: '1px solid red',\n              borderRadius: '.25rem',\n              padding: '.3rem',\n              color: 'red',\n              overflow: 'auto',\n            }}\n          >\n            {error.message ? <code>{error.message}</code> : null}\n          </pre>\n        </div>\n      ) : null}\n    </div>\n  )\n}\n"], "names": ["jsx", "React", "jsxs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIO,SAAS,cAAc,OAK3B;AACK,QAAA,iBAAiB,MAAM,kBAAkB;AAG7C,SAAAA,2BAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,aAAa,MAAM;AAAA,MACnB,SAAS,MAAM;AAAA,MACf,UAAU,CAAC,EAAE,OAAO,YAAY;AAC9B,YAAI,OAAO;AACF,iBAAAC,iBAAM,cAAc,gBAAgB;AAAA,YACzC;AAAA,YACA;AAAA,UAAA,CACD;AAAA,QAAA;AAGH,eAAO,MAAM;AAAA,MAAA;AAAA,IACf;AAAA,EACF;AAEJ;AAEA,MAAM,0BAA0BA,iBAAM,UAOnC;AAAA,EAPH,cAAA;AAAA,UAAA,GAAA,SAAA;AAQU,SAAA,QAAA,EAAE,OAAO,KAAK;AAAA,EAAA;AAAA,EACtB,OAAO,yBAAyB,OAAY;AAC1C,WAAO,EAAE,UAAU,MAAM,cAAc;AAAA,EAAA;AAAA,EAEzC,OAAO,yBAAyB,OAAc;AAC5C,WAAO,EAAE,MAAM;AAAA,EAAA;AAAA,EAEjB,QAAQ;AACN,SAAK,SAAS,EAAE,OAAO,KAAA,CAAM;AAAA,EAAA;AAAA,EAE/B,mBACE,WAKA,WACM;AACN,QAAI,UAAU,SAAS,UAAU,aAAa,KAAK,MAAM,UAAU;AACjE,WAAK,MAAM;AAAA,IAAA;AAAA,EACb;AAAA,EAEF,kBAAkB,OAAc,WAAsB;AAChD,QAAA,KAAK,MAAM,SAAS;AACjB,WAAA,MAAM,QAAQ,OAAO,SAAS;AAAA,IAAA;AAAA,EACrC;AAAA,EAEF,SAAS;AAEA,WAAA,KAAK,MAAM,SAAS;AAAA,MACzB,OACE,KAAK,MAAM,aAAa,KAAK,MAAM,gBAC/B,OACA,KAAK,MAAM;AAAA,MACjB,OAAO,MAAM;AACX,aAAK,MAAM;AAAA,MAAA;AAAA,IACb,CACD;AAAA,EAAA;AAEL;AAEgB,SAAA,eAAe,EAAE,SAAyB;AAClD,QAAA,CAAC,MAAM,OAAO,IAAIA,iBAAM,SAAS,QAAQ,IAAI,aAAa,YAAY;AAG1E,SAAAC,2BAAA,KAAC,SAAI,OAAO,EAAE,SAAS,SAAS,UAAU,OACxC,GAAA,UAAA;AAAA,IAACA,2BAAAA,KAAA,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,QAAA,GACxD,UAAA;AAAA,MAAAF,+BAAC,YAAO,OAAO,EAAE,UAAU,UAAU,UAAqB,yBAAA;AAAA,MAC1DA,2BAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,OAAO;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,cAAc;AAAA,UAChB;AAAA,UACA,SAAS,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC;AAAA,UAE/B,iBAAO,eAAe;AAAA,QAAA;AAAA,MAAA;AAAA,IACzB,GACF;AAAA,mCACC,OAAI,EAAA,OAAO,EAAE,QAAQ,YAAY;AAAA,IACjC,sCACE,OACC,EAAA,UAAAA,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OAAO;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,QAEC,gBAAM,UAAUA,+BAAC,QAAM,EAAA,UAAA,MAAM,SAAQ,IAAU;AAAA,MAAA;AAAA,OAEpD,IACE;AAAA,EAAA,GACN;AAEJ;;;"}