import { ErrorInfo } from 'react';
import { NotFoundError } from '@tanstack/router-core';
import * as React from 'react';
export declare function CatchNotFound(props: {
    fallback?: (error: NotFoundError) => React.ReactElement;
    onCatch?: (error: Error, errorInfo: ErrorInfo) => void;
    children: React.ReactNode;
}): import("react/jsx-runtime").JSX.Element;
export declare function DefaultGlobalNotFound(): import("react/jsx-runtime").JSX.Element;
