{"version": 3, "file": "renderRouteNotFound.cjs", "sources": ["../../src/renderRouteNotFound.tsx"], "sourcesContent": ["import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { DefaultGlobalNotFound } from './not-found'\nimport type { AnyRoute, AnyRouter } from '@tanstack/router-core'\n\nexport function renderRouteNotFound(\n  router: AnyRouter,\n  route: AnyRoute,\n  data: any,\n) {\n  if (!route.options.notFoundComponent) {\n    if (router.options.defaultNotFoundComponent) {\n      return <router.options.defaultNotFoundComponent data={data} />\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      warning(\n        route.options.notFoundComponent,\n        `A notFoundError was encountered on the route with ID \"${route.id}\", but a notFoundComponent option was not configured, nor was a router level defaultNotFoundComponent configured. Consider configuring at least one of these to avoid TanStack Router's overly generic defaultNotFoundComponent (<div>Not Found<div>)`,\n      )\n    }\n\n    return <DefaultGlobalNotFound />\n  }\n\n  return <route.options.notFoundComponent data={data} />\n}\n"], "names": ["jsx", "DefaultGlobalNotFound"], "mappings": ";;;;;AAKgB,SAAA,oBACd,QACA,OACA,MACA;AACI,MAAA,CAAC,MAAM,QAAQ,mBAAmB;AAChC,QAAA,OAAO,QAAQ,0BAA0B;AAC3C,aAAQA,2BAAAA,IAAA,OAAO,QAAQ,0BAAf,EAAwC,KAAY,CAAA;AAAA,IAAA;AAG1D,QAAA,QAAQ,IAAI,aAAa,eAAe;AAC1C;AAAA,QACE,MAAM,QAAQ;AAAA,QACd,yDAAyD,MAAM,EAAE;AAAA,MACnE;AAAA,IAAA;AAGF,0CAAQC,SAAsB,uBAAA,EAAA;AAAA,EAAA;AAGhC,SAAQD,2BAAAA,IAAA,MAAM,QAAQ,mBAAd,EAAgC,KAAY,CAAA;AACtD;;"}