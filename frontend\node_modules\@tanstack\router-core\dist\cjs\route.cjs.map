{"version": 3, "file": "route.cjs", "sources": ["../../src/route.ts"], "sourcesContent": ["import { joinPaths, trimPathLeft } from './path'\nimport { notFound } from './not-found'\nimport { rootRouteId } from './root'\nimport type { LazyRoute } from './fileRoute'\nimport type { NotFoundError } from './not-found'\nimport type { NavigateOptions, ParsePathParams } from './link'\nimport type { ParsedLocation } from './location'\nimport type {\n  AnyRouteMatch,\n  MakeRouteMatchFromRoute,\n  MakeRouteMatchUnion,\n  RouteMatch,\n} from './Matches'\nimport type { RootRouteId } from './root'\nimport type { ParseRoute, RouteById, RoutePaths } from './routeInfo'\nimport type { AnyRouter, RegisteredRouter } from './router'\nimport type { BuildLocationFn, NavigateFn } from './RouterProvider'\nimport type {\n  Assign,\n  Constrain,\n  Expand,\n  IntersectAssign,\n  NoInfer,\n} from './utils'\nimport type {\n  AnySchema,\n  AnyStandardSchemaValidator,\n  AnyValidator,\n  AnyValidatorAdapter,\n  AnyValidatorObj,\n  DefaultValidator,\n  ResolveSearchValidatorInput,\n  ResolveValidatorOutput,\n  StandardSchemaValidator,\n  ValidatorAdapter,\n  ValidatorFn,\n  ValidatorObj,\n} from './validators'\n\nexport type AnyPathParams = {}\n\nexport type SearchSchemaInput = {\n  __TSearchSchemaInput__: 'TSearchSchemaInput'\n}\n\nexport type AnyContext = {}\n\nexport interface RouteContext {}\n\nexport type PreloadableObj = { preload?: () => Promise<void> }\n\nexport type RoutePathOptions<TCustomId, TPath> =\n  | {\n      path: TPath\n    }\n  | {\n      id: TCustomId\n    }\n\nexport interface StaticDataRouteOption {}\n\nexport type RoutePathOptionsIntersection<TCustomId, TPath> = {\n  path: TPath\n  id: TCustomId\n}\n\nexport type SearchFilter<TInput, TResult = TInput> = (prev: TInput) => TResult\n\nexport type SearchMiddlewareContext<TSearchSchema> = {\n  search: TSearchSchema\n  next: (newSearch: TSearchSchema) => TSearchSchema\n}\n\nexport type SearchMiddleware<TSearchSchema> = (\n  ctx: SearchMiddlewareContext<TSearchSchema>,\n) => TSearchSchema\n\nexport type ResolveId<\n  TParentRoute,\n  TCustomId extends string,\n  TPath extends string,\n> = TParentRoute extends { id: infer TParentId extends string }\n  ? RoutePrefix<TParentId, string extends TCustomId ? TPath : TCustomId>\n  : RootRouteId\n\nexport type InferFullSearchSchema<TRoute> = TRoute extends {\n  types: {\n    fullSearchSchema: infer TFullSearchSchema\n  }\n}\n  ? TFullSearchSchema\n  : {}\n\nexport type InferFullSearchSchemaInput<TRoute> = TRoute extends {\n  types: {\n    fullSearchSchemaInput: infer TFullSearchSchemaInput\n  }\n}\n  ? TFullSearchSchemaInput\n  : {}\n\nexport type InferAllParams<TRoute> = TRoute extends {\n  types: {\n    allParams: infer TAllParams\n  }\n}\n  ? TAllParams\n  : {}\n\nexport type InferAllContext<TRoute> = unknown extends TRoute\n  ? TRoute\n  : TRoute extends {\n        types: {\n          allContext: infer TAllContext\n        }\n      }\n    ? TAllContext\n    : {}\n\nexport type ResolveSearchSchemaFnInput<TSearchValidator> =\n  TSearchValidator extends (input: infer TSearchSchemaInput) => any\n    ? TSearchSchemaInput extends SearchSchemaInput\n      ? Omit<TSearchSchemaInput, keyof SearchSchemaInput>\n      : ResolveSearchSchemaFn<TSearchValidator>\n    : AnySchema\n\nexport type ResolveSearchSchemaInput<TSearchValidator> =\n  TSearchValidator extends AnyStandardSchemaValidator\n    ? NonNullable<TSearchValidator['~standard']['types']>['input']\n    : TSearchValidator extends AnyValidatorAdapter\n      ? TSearchValidator['types']['input']\n      : TSearchValidator extends AnyValidatorObj\n        ? ResolveSearchSchemaFnInput<TSearchValidator['parse']>\n        : ResolveSearchSchemaFnInput<TSearchValidator>\n\nexport type ResolveSearchSchemaFn<TSearchValidator> = TSearchValidator extends (\n  ...args: any\n) => infer TSearchSchema\n  ? TSearchSchema\n  : AnySchema\n\nexport type ResolveSearchSchema<TSearchValidator> =\n  unknown extends TSearchValidator\n    ? TSearchValidator\n    : TSearchValidator extends AnyStandardSchemaValidator\n      ? NonNullable<TSearchValidator['~standard']['types']>['output']\n      : TSearchValidator extends AnyValidatorAdapter\n        ? TSearchValidator['types']['output']\n        : TSearchValidator extends AnyValidatorObj\n          ? ResolveSearchSchemaFn<TSearchValidator['parse']>\n          : ResolveSearchSchemaFn<TSearchValidator>\n\nexport type ParseSplatParams<TPath extends string> = TPath &\n  `${string}$` extends never\n  ? TPath & `${string}$/${string}` extends never\n    ? never\n    : '_splat'\n  : '_splat'\n\nexport interface SplatParams {\n  _splat?: string\n}\n\nexport type ResolveParams<TPath extends string> =\n  ParseSplatParams<TPath> extends never\n    ? Record<ParsePathParams<TPath>, string>\n    : Record<ParsePathParams<TPath>, string> & SplatParams\n\nexport type ParseParamsFn<in out TPath extends string, in out TParams> = (\n  rawParams: ResolveParams<TPath>,\n) => TParams extends Record<ParsePathParams<TPath>, any>\n  ? TParams\n  : Record<ParsePathParams<TPath>, any>\n\nexport type StringifyParamsFn<in out TPath extends string, in out TParams> = (\n  params: TParams,\n) => ResolveParams<TPath>\n\nexport type ParamsOptions<in out TPath extends string, in out TParams> = {\n  params?: {\n    parse?: ParseParamsFn<TPath, TParams>\n    stringify?: StringifyParamsFn<TPath, TParams>\n  }\n\n  /** \n  @deprecated Use params.parse instead\n  */\n  parseParams?: ParseParamsFn<TPath, TParams>\n\n  /** \n  @deprecated Use params.stringify instead\n  */\n  stringifyParams?: StringifyParamsFn<TPath, TParams>\n}\n\ninterface RequiredStaticDataRouteOption {\n  staticData: StaticDataRouteOption\n}\n\ninterface OptionalStaticDataRouteOption {\n  staticData?: StaticDataRouteOption\n}\n\nexport type UpdatableStaticRouteOption = {} extends StaticDataRouteOption\n  ? OptionalStaticDataRouteOption\n  : RequiredStaticDataRouteOption\n\nexport type MetaDescriptor =\n  | { charSet: 'utf-8' }\n  | { title: string }\n  | { name: string; content: string }\n  | { property: string; content: string }\n  | { httpEquiv: string; content: string }\n  | { 'script:ld+json': LdJsonObject }\n  | { tagName: 'meta' | 'link'; [name: string]: string }\n  | Record<string, unknown>\n\ntype LdJsonObject = { [Key in string]: LdJsonValue } & {\n  [Key in string]?: LdJsonValue | undefined\n}\ntype LdJsonArray = Array<LdJsonValue> | ReadonlyArray<LdJsonValue>\ntype LdJsonPrimitive = string | number | boolean | null\ntype LdJsonValue = LdJsonPrimitive | LdJsonObject | LdJsonArray\n\nexport type RouteLinkEntry = {}\n\nexport type SearchValidator<TInput, TOutput> =\n  | ValidatorObj<TInput, TOutput>\n  | ValidatorFn<TInput, TOutput>\n  | ValidatorAdapter<TInput, TOutput>\n  | StandardSchemaValidator<TInput, TOutput>\n  | undefined\n\nexport type AnySearchValidator = SearchValidator<any, any>\n\nexport type DefaultSearchValidator = SearchValidator<\n  Record<string, unknown>,\n  AnySchema\n>\n\nexport type RoutePrefix<\n  TPrefix extends string,\n  TPath extends string,\n> = string extends TPath\n  ? RootRouteId\n  : TPath extends string\n    ? TPrefix extends RootRouteId\n      ? TPath extends '/'\n        ? '/'\n        : `/${TrimPath<TPath>}`\n      : `${TPrefix}/${TPath}` extends '/'\n        ? '/'\n        : `/${TrimPathLeft<`${TrimPathRight<TPrefix>}/${TrimPath<TPath>}`>}`\n    : never\n\nexport type TrimPath<T extends string> = '' extends T\n  ? ''\n  : TrimPathRight<TrimPathLeft<T>>\n\nexport type TrimPathLeft<T extends string> =\n  T extends `${RootRouteId}/${infer U}`\n    ? TrimPathLeft<U>\n    : T extends `/${infer U}`\n      ? TrimPathLeft<U>\n      : T\n\nexport type TrimPathRight<T extends string> = T extends '/'\n  ? '/'\n  : T extends `${infer U}/`\n    ? TrimPathRight<U>\n    : T\n\nexport type LooseReturnType<T> = T extends (\n  ...args: Array<any>\n) => infer TReturn\n  ? TReturn\n  : never\n\nexport type LooseAsyncReturnType<T> = T extends (\n  ...args: Array<any>\n) => infer TReturn\n  ? TReturn extends Promise<infer TReturn>\n    ? TReturn\n    : TReturn\n  : never\n\nexport type ContextReturnType<TContextFn> = unknown extends TContextFn\n  ? TContextFn\n  : LooseReturnType<TContextFn> extends never\n    ? AnyContext\n    : LooseReturnType<TContextFn>\n\nexport type ContextAsyncReturnType<TContextFn> = unknown extends TContextFn\n  ? TContextFn\n  : LooseAsyncReturnType<TContextFn> extends never\n    ? AnyContext\n    : LooseAsyncReturnType<TContextFn>\n\nexport type ResolveRouteContext<TRouteContextFn, TBeforeLoadFn> = Assign<\n  ContextReturnType<TRouteContextFn>,\n  ContextAsyncReturnType<TBeforeLoadFn>\n>\n\nexport type ResolveLoaderData<TLoaderFn> = unknown extends TLoaderFn\n  ? TLoaderFn\n  : LooseAsyncReturnType<TLoaderFn> extends never\n    ? undefined\n    : LooseAsyncReturnType<TLoaderFn>\n\nexport type ResolveFullSearchSchema<\n  TParentRoute extends AnyRoute,\n  TSearchValidator,\n> = unknown extends TParentRoute\n  ? ResolveValidatorOutput<TSearchValidator>\n  : IntersectAssign<\n      InferFullSearchSchema<TParentRoute>,\n      ResolveValidatorOutput<TSearchValidator>\n    >\n\nexport type ResolveFullSearchSchemaInput<\n  TParentRoute extends AnyRoute,\n  TSearchValidator,\n> = IntersectAssign<\n  InferFullSearchSchemaInput<TParentRoute>,\n  ResolveSearchValidatorInput<TSearchValidator>\n>\n\nexport type ResolveAllParamsFromParent<\n  TParentRoute extends AnyRoute,\n  TParams,\n> = Assign<InferAllParams<TParentRoute>, TParams>\n\nexport type RouteContextParameter<\n  TParentRoute extends AnyRoute,\n  TRouterContext,\n> = unknown extends TParentRoute\n  ? TRouterContext\n  : Assign<TRouterContext, InferAllContext<TParentRoute>>\n\nexport type BeforeLoadContextParameter<\n  TParentRoute extends AnyRoute,\n  TRouterContext,\n  TRouteContextFn,\n> = Assign<\n  RouteContextParameter<TParentRoute, TRouterContext>,\n  ContextReturnType<TRouteContextFn>\n>\n\nexport type ResolveAllContext<\n  TParentRoute extends AnyRoute,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n> = Assign<\n  BeforeLoadContextParameter<TParentRoute, TRouterContext, TRouteContextFn>,\n  ContextAsyncReturnType<TBeforeLoadFn>\n>\nexport interface FullSearchSchemaOption<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n> {\n  search: Expand<ResolveFullSearchSchema<TParentRoute, TSearchValidator>>\n}\n\nexport interface RemountDepsOptions<\n  in out TRouteId,\n  in out TFullSearchSchema,\n  in out TAllParams,\n  in out TLoaderDeps,\n> {\n  routeId: TRouteId\n  search: TFullSearchSchema\n  params: TAllParams\n  loaderDeps: TLoaderDeps\n}\n\nexport type MakeRemountDepsOptionsUnion<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n> =\n  ParseRoute<TRouteTree> extends infer TRoute extends AnyRoute\n    ? TRoute extends any\n      ? RemountDepsOptions<\n          TRoute['id'],\n          TRoute['types']['fullSearchSchema'],\n          TRoute['types']['allParams'],\n          TRoute['types']['loaderDeps']\n        >\n      : never\n    : never\n\nexport interface RouteTypes<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps,\n  in out TLoaderFn,\n  in out TChildren,\n  in out TFileRouteTypes,\n> {\n  parentRoute: TParentRoute\n  path: TPath\n  to: TrimPathRight<TFullPath>\n  fullPath: TFullPath\n  customId: TCustomId\n  id: TId\n  searchSchema: ResolveValidatorOutput<TSearchValidator>\n  searchSchemaInput: ResolveSearchValidatorInput<TSearchValidator>\n  searchValidator: TSearchValidator\n  fullSearchSchema: ResolveFullSearchSchema<TParentRoute, TSearchValidator>\n  fullSearchSchemaInput: ResolveFullSearchSchemaInput<\n    TParentRoute,\n    TSearchValidator\n  >\n  params: TParams\n  allParams: ResolveAllParamsFromParent<TParentRoute, TParams>\n  routerContext: TRouterContext\n  routeContext: ResolveRouteContext<TRouteContextFn, TBeforeLoadFn>\n  routeContextFn: TRouteContextFn\n  beforeLoadFn: TBeforeLoadFn\n  allContext: ResolveAllContext<\n    TParentRoute,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >\n  children: TChildren\n  loaderData: ResolveLoaderData<TLoaderFn>\n  loaderDeps: TLoaderDeps\n  fileRouteTypes: TFileRouteTypes\n}\n\nexport type ResolveFullPath<\n  TParentRoute extends AnyRoute,\n  TPath extends string,\n  TPrefixed = RoutePrefix<TParentRoute['fullPath'], TPath>,\n> = TPrefixed extends RootRouteId ? '/' : TPrefixed\n\nexport interface RouteExtensions<in out TId, in out TFullPath> {\n  id: TId\n  fullPath: TFullPath\n}\n\nexport type RouteLazyFn<TRoute extends AnyRoute> = (\n  lazyFn: () => Promise<LazyRoute>,\n) => TRoute\n\nexport type RouteAddChildrenFn<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps extends Record<string, any>,\n  in out TLoaderFn,\n  in out TFileRouteTypes,\n> = <const TNewChildren>(\n  children: Constrain<\n    TNewChildren,\n    ReadonlyArray<AnyRoute> | Record<string, AnyRoute>\n  >,\n) => Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TNewChildren,\n  TFileRouteTypes\n>\n\nexport type RouteAddFileChildrenFn<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps extends Record<string, any>,\n  in out TLoaderFn,\n  in out TFileRouteTypes,\n> = <const TNewChildren>(\n  children: TNewChildren,\n) => Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TNewChildren,\n  TFileRouteTypes\n>\n\nexport type RouteAddFileTypesFn<\n  TParentRoute extends AnyRoute,\n  TPath extends string,\n  TFullPath extends string,\n  TCustomId extends string,\n  TId extends string,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps extends Record<string, any>,\n  TLoaderFn,\n  TChildren,\n> = <TNewFileRouteTypes>() => Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren,\n  TNewFileRouteTypes\n>\n\nexport interface Route<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps extends Record<string, any>,\n  in out TLoaderFn,\n  in out TChildren,\n  in out TFileRouteTypes,\n> extends RouteExtensions<TId, TFullPath> {\n  path: TPath\n  parentRoute: TParentRoute\n  children?: TChildren\n  types: RouteTypes<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >\n  isRoot: TParentRoute extends AnyRoute ? true : false\n  _componentsPromise?: Promise<Array<void>>\n  lazyFn?: () => Promise<LazyRoute>\n  _lazyPromise?: Promise<void>\n  rank: number\n  to: TrimPathRight<TFullPath>\n  init: (opts: { originalIndex: number; defaultSsr?: boolean }) => void\n  update: (\n    options: UpdatableRouteOptions<\n      TParentRoute,\n      TCustomId,\n      TFullPath,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TLoaderDeps,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) => this\n  lazy: RouteLazyFn<\n    Route<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n  >\n  addChildren: RouteAddChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  >\n  _addFileChildren: RouteAddFileChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  >\n  _addFileTypes: RouteAddFileTypesFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  >\n}\n\nexport type AnyRoute = Route<\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any\n>\n\nexport type AnyRouteWithContext<TContext> = AnyRoute & {\n  types: { allContext: TContext }\n}\n\nexport type RouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TId extends string = string,\n  TCustomId extends string = string,\n  TFullPath extends string = string,\n  TPath extends string = string,\n  TSearchValidator = undefined,\n  TParams = AnyPathParams,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n> = BaseRouteOptions<\n  TParentRoute,\n  TId,\n  TCustomId,\n  TPath,\n  TSearchValidator,\n  TParams,\n  TLoaderDeps,\n  TLoaderFn,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn\n> &\n  UpdatableRouteOptions<\n    NoInfer<TParentRoute>,\n    NoInfer<TCustomId>,\n    NoInfer<TFullPath>,\n    NoInfer<TParams>,\n    NoInfer<TSearchValidator>,\n    NoInfer<TLoaderFn>,\n    NoInfer<TLoaderDeps>,\n    NoInfer<TRouterContext>,\n    NoInfer<TRouteContextFn>,\n    NoInfer<TBeforeLoadFn>\n  >\n\nexport type RouteContextFn<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n> = (\n  ctx: RouteContextOptions<\n    TParentRoute,\n    TSearchValidator,\n    TParams,\n    TRouterContext\n  >,\n) => any\n\nexport type BeforeLoadFn<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n> = (\n  ctx: BeforeLoadContextOptions<\n    TParentRoute,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn\n  >,\n) => any\n\nexport type FileBaseRouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TId extends string = string,\n  TPath extends string = string,\n  TSearchValidator = undefined,\n  TParams = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TRemountDepsFn = AnyContext,\n> = ParamsOptions<TPath, TParams> & {\n  validateSearch?: Constrain<TSearchValidator, AnyValidator, DefaultValidator>\n\n  shouldReload?:\n    | boolean\n    | ((\n        match: LoaderFnContext<\n          TParentRoute,\n          TId,\n          TParams,\n          TLoaderDeps,\n          TRouterContext,\n          TRouteContextFn,\n          TBeforeLoadFn\n        >,\n      ) => any)\n\n  context?: Constrain<\n    TRouteContextFn,\n    (\n      ctx: RouteContextOptions<\n        TParentRoute,\n        TParams,\n        TRouterContext,\n        TLoaderDeps\n      >,\n    ) => any\n  >\n\n  // This async function is called before a route is loaded.\n  // If an error is thrown here, the route's loader will not be called.\n  // If thrown during a navigation, the navigation will be cancelled and the error will be passed to the `onError` function.\n  // If thrown during a preload event, the error will be logged to the console.\n  beforeLoad?: Constrain<\n    TBeforeLoadFn,\n    (\n      ctx: BeforeLoadContextOptions<\n        TParentRoute,\n        TSearchValidator,\n        TParams,\n        TRouterContext,\n        TRouteContextFn\n      >,\n    ) => any\n  >\n\n  loaderDeps?: (\n    opts: FullSearchSchemaOption<TParentRoute, TSearchValidator>,\n  ) => TLoaderDeps\n\n  remountDeps?: Constrain<\n    TRemountDepsFn,\n    (\n      opt: RemountDepsOptions<\n        TId,\n        FullSearchSchemaOption<TParentRoute, TSearchValidator>,\n        Expand<ResolveAllParamsFromParent<TParentRoute, TParams>>,\n        TLoaderDeps\n      >,\n    ) => any\n  >\n\n  loader?: Constrain<\n    TLoaderFn,\n    (\n      ctx: LoaderFnContext<\n        TParentRoute,\n        TId,\n        TParams,\n        TLoaderDeps,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n    ) => any\n  >\n}\n\nexport type BaseRouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TId extends string = string,\n  TCustomId extends string = string,\n  TPath extends string = string,\n  TSearchValidator = undefined,\n  TParams = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n> = RoutePathOptions<TCustomId, TPath> &\n  FileBaseRouteOptions<\n    TParentRoute,\n    TId,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  > & {\n    getParentRoute: () => TParentRoute\n  }\n\nexport interface ContextOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TParams,\n> {\n  abortController: AbortController\n  preload: boolean\n  params: Expand<ResolveAllParamsFromParent<TParentRoute, TParams>>\n  location: ParsedLocation\n  /**\n   * @deprecated Use `throw redirect({ to: '/somewhere' })` instead\n   **/\n  navigate: NavigateFn\n  buildLocation: BuildLocationFn\n  cause: 'preload' | 'enter' | 'stay'\n  matches: Array<MakeRouteMatchUnion>\n}\n\nexport interface RouteContextOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TParams,\n  in out TRouterContext,\n  in out TLoaderDeps,\n> extends ContextOptions<TParentRoute, TParams> {\n  deps: TLoaderDeps\n  context: Expand<RouteContextParameter<TParentRoute, TRouterContext>>\n}\n\nexport interface BeforeLoadContextOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n> extends ContextOptions<TParentRoute, TParams>,\n    FullSearchSchemaOption<TParentRoute, TSearchValidator> {\n  context: Expand<\n    BeforeLoadContextParameter<TParentRoute, TRouterContext, TRouteContextFn>\n  >\n}\n\ntype AssetFnContextOptions<\n  in out TRouteId,\n  in out TFullPath,\n  in out TParentRoute extends AnyRoute,\n  in out TParams,\n  in out TSearchValidator,\n  in out TLoaderFn,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps,\n> = {\n  matches: Array<\n    RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >\n  >\n  match: RouteMatch<\n    TRouteId,\n    TFullPath,\n    ResolveAllParamsFromParent<TParentRoute, TParams>,\n    ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n    ResolveLoaderData<TLoaderFn>,\n    ResolveAllContext<\n      TParentRoute,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n    TLoaderDeps\n  >\n  params: ResolveAllParamsFromParent<TParentRoute, TParams>\n  loaderData?: ResolveLoaderData<TLoaderFn>\n}\n\nexport interface DefaultUpdatableRouteOptionsExtensions {\n  component?: unknown\n  errorComponent?: unknown\n  notFoundComponent?: unknown\n  pendingComponent?: unknown\n}\n\nexport interface UpdatableRouteOptionsExtensions\n  extends DefaultUpdatableRouteOptionsExtensions {}\n\nexport interface UpdatableRouteOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TRouteId,\n  in out TFullPath,\n  in out TParams,\n  in out TSearchValidator,\n  in out TLoaderFn,\n  in out TLoaderDeps,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n> extends UpdatableStaticRouteOption,\n    UpdatableRouteOptionsExtensions {\n  // If true, this route will be matched as case-sensitive\n  caseSensitive?: boolean\n  // If true, this route will be forcefully wrapped in a suspense boundary\n  wrapInSuspense?: boolean\n  // The content to be rendered when the route is matched. If no component is provided, defaults to `<Outlet />`\n\n  pendingMs?: number\n  pendingMinMs?: number\n  staleTime?: number\n  gcTime?: number\n  preload?: boolean\n  preloadStaleTime?: number\n  preloadGcTime?: number\n  search?: {\n    middlewares?: Array<\n      SearchMiddleware<\n        ResolveFullSearchSchemaInput<TParentRoute, TSearchValidator>\n      >\n    >\n  }\n  /** \n  @deprecated Use search.middlewares instead\n  */\n  preSearchFilters?: Array<\n    SearchFilter<ResolveFullSearchSchema<TParentRoute, TSearchValidator>>\n  >\n  /** \n  @deprecated Use search.middlewares instead\n  */\n  postSearchFilters?: Array<\n    SearchFilter<ResolveFullSearchSchema<TParentRoute, TSearchValidator>>\n  >\n  onCatch?: (error: Error) => void\n  onError?: (err: any) => void\n  // These functions are called as route matches are loaded, stick around and leave the active\n  // matches\n  onEnter?: (\n    match: RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >,\n  ) => void\n  onStay?: (\n    match: RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >,\n  ) => void\n  onLeave?: (\n    match: RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >,\n  ) => void\n  headers?: (\n    ctx: AssetFnContextOptions<\n      TRouteId,\n      TFullPath,\n      TParentRoute,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps\n    >,\n  ) => Record<string, string>\n  head?: (\n    ctx: AssetFnContextOptions<\n      TRouteId,\n      TFullPath,\n      TParentRoute,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps\n    >,\n  ) => {\n    links?: AnyRouteMatch['links']\n    scripts?: AnyRouteMatch['headScripts']\n    meta?: AnyRouteMatch['meta']\n  }\n  scripts?: (\n    ctx: AssetFnContextOptions<\n      TRouteId,\n      TFullPath,\n      TParentRoute,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps\n    >,\n  ) => AnyRouteMatch['scripts']\n  ssr?: boolean\n  codeSplitGroupings?: Array<\n    Array<\n      | 'loader'\n      | 'component'\n      | 'pendingComponent'\n      | 'notFoundComponent'\n      | 'errorComponent'\n    >\n  >\n}\n\nexport type RouteLoaderFn<\n  in out TParentRoute extends AnyRoute = AnyRoute,\n  in out TId extends string = string,\n  in out TParams = {},\n  in out TLoaderDeps = {},\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n> = (\n  match: LoaderFnContext<\n    TParentRoute,\n    TId,\n    TParams,\n    TLoaderDeps,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n) => any\n\nexport interface LoaderFnContext<\n  in out TParentRoute extends AnyRoute = AnyRoute,\n  in out TId extends string = string,\n  in out TParams = {},\n  in out TLoaderDeps = {},\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n> {\n  abortController: AbortController\n  preload: boolean\n  params: Expand<ResolveAllParamsFromParent<TParentRoute, TParams>>\n  deps: TLoaderDeps\n  context: Expand<\n    ResolveAllContext<\n      TParentRoute,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >\n  >\n  location: ParsedLocation // Do not supply search schema here so as to demotivate people from trying to shortcut loaderDeps\n  /**\n   * @deprecated Use `throw redirect({ to: '/somewhere' })` instead\n   **/\n  navigate: (opts: NavigateOptions<AnyRouter>) => Promise<void> | void\n  // root route does not have a parent match\n  parentMatchPromise: TId extends RootRouteId\n    ? never\n    : Promise<MakeRouteMatchFromRoute<TParentRoute>>\n  cause: 'preload' | 'enter' | 'stay'\n  route: AnyRoute\n}\n\nexport type RootRouteOptions<\n  TSearchValidator = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n> = Omit<\n  RouteOptions<\n    any, // TParentRoute\n    RootRouteId, // TId\n    RootRouteId, // TCustomId\n    '', // TFullPath\n    '', // TPath\n    TSearchValidator,\n    {}, // TParams\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n  | 'path'\n  | 'id'\n  | 'getParentRoute'\n  | 'caseSensitive'\n  | 'parseParams'\n  | 'stringifyParams'\n  | 'params'\n>\n\nexport type RouteConstraints = {\n  TParentRoute: AnyRoute\n  TPath: string\n  TFullPath: string\n  TCustomId: string\n  TId: string\n  TSearchSchema: AnySchema\n  TFullSearchSchema: AnySchema\n  TParams: Record<string, any>\n  TAllParams: Record<string, any>\n  TParentContext: AnyContext\n  TRouteContext: RouteContext\n  TAllContext: AnyContext\n  TRouterContext: AnyContext\n  TChildren: unknown\n  TRouteTree: AnyRoute\n}\n\nexport type RouteTypesById<TRouter extends AnyRouter, TId> = RouteById<\n  TRouter['routeTree'],\n  TId\n>['types']\n\nexport type RouteMask<TRouteTree extends AnyRoute> = {\n  routeTree: TRouteTree\n  from: RoutePaths<TRouteTree>\n  to?: any\n  params?: any\n  search?: any\n  hash?: any\n  state?: any\n  unmaskOnReload?: boolean\n}\n\n/**\n * @deprecated Use `ErrorComponentProps` instead.\n */\nexport type ErrorRouteProps = {\n  error: unknown\n  info?: { componentStack: string }\n  reset: () => void\n}\n\nexport type ErrorComponentProps = {\n  error: Error\n  info?: { componentStack: string }\n  reset: () => void\n}\nexport type NotFoundRouteProps = {\n  // TODO: Make sure this is `| null | undefined` (this is for global not-founds)\n  data: unknown\n}\n\nexport class BaseRoute<\n  in out TParentRoute extends AnyRoute = AnyRoute,\n  in out TPath extends string = '/',\n  in out TFullPath extends string = ResolveFullPath<TParentRoute, TPath>,\n  in out TCustomId extends string = string,\n  in out TId extends string = ResolveId<TParentRoute, TCustomId, TPath>,\n  in out TSearchValidator = undefined,\n  in out TParams = ResolveParams<TPath>,\n  in out TRouterContext = AnyContext,\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n  in out TLoaderDeps extends Record<string, any> = {},\n  in out TLoaderFn = undefined,\n  in out TChildren = unknown,\n  in out TFileRouteTypes = unknown,\n> {\n  isRoot: TParentRoute extends AnyRoute ? true : false\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >\n\n  // The following properties are set up in this.init()\n  parentRoute!: TParentRoute\n  private _id!: TId\n  private _path!: TPath\n  private _fullPath!: TFullPath\n  private _to!: TrimPathRight<TFullPath>\n  private _ssr!: boolean\n\n  public get to() {\n    return this._to\n  }\n\n  public get id() {\n    return this._id\n  }\n\n  public get path() {\n    return this._path\n  }\n\n  public get fullPath() {\n    return this._fullPath\n  }\n\n  public get ssr() {\n    return this._ssr\n  }\n\n  // Optional\n  children?: TChildren\n  originalIndex?: number\n  rank!: number\n  lazyFn?: () => Promise<LazyRoute>\n  _lazyPromise?: Promise<void>\n  _componentsPromise?: Promise<Array<void>>\n\n  constructor(\n    options?: RouteOptions<\n      TParentRoute,\n      TId,\n      TCustomId,\n      TFullPath,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) {\n    this.options = (options as any) || {}\n    this.isRoot = !options?.getParentRoute as any\n\n    if ((options as any)?.id && (options as any)?.path) {\n      throw new Error(`Route cannot have both an 'id' and a 'path' option.`)\n    }\n  }\n\n  types!: RouteTypes<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n\n  init = (opts: { originalIndex: number; defaultSsr?: boolean }): void => {\n    this.originalIndex = opts.originalIndex\n\n    const options = this.options as\n      | (RouteOptions<\n          TParentRoute,\n          TId,\n          TCustomId,\n          TFullPath,\n          TPath,\n          TSearchValidator,\n          TParams,\n          TLoaderDeps,\n          TLoaderFn,\n          TRouterContext,\n          TRouteContextFn,\n          TBeforeLoadFn\n        > &\n          RoutePathOptionsIntersection<TCustomId, TPath>)\n      | undefined\n\n    const isRoot = !options?.path && !options?.id\n\n    this.parentRoute = this.options.getParentRoute?.()\n\n    if (isRoot) {\n      this._path = rootRouteId as TPath\n    } else if (!this.parentRoute) {\n      throw new Error(\n        `Child Route instances must pass a 'getParentRoute: () => ParentRoute' option that returns a Route instance.`,\n      )\n    }\n\n    let path: undefined | string = isRoot ? rootRouteId : options?.path\n\n    // If the path is anything other than an index path, trim it up\n    if (path && path !== '/') {\n      path = trimPathLeft(path)\n    }\n\n    const customId = options?.id || path\n\n    // Strip the parentId prefix from the first level of children\n    let id = isRoot\n      ? rootRouteId\n      : joinPaths([\n          this.parentRoute.id === rootRouteId ? '' : this.parentRoute.id,\n          customId,\n        ])\n\n    if (path === rootRouteId) {\n      path = '/'\n    }\n\n    if (id !== rootRouteId) {\n      id = joinPaths(['/', id])\n    }\n\n    const fullPath =\n      id === rootRouteId ? '/' : joinPaths([this.parentRoute.fullPath, path])\n\n    this._path = path as TPath\n    this._id = id as TId\n    this._fullPath = fullPath as TFullPath\n    this._to = fullPath as TrimPathRight<TFullPath>\n    this._ssr = options?.ssr ?? opts.defaultSsr ?? true\n  }\n\n  clone = (other: typeof this) => {\n    this._path = other._path\n    this._id = other._id\n    this._fullPath = other._fullPath\n    this._to = other._to\n    this._ssr = other._ssr\n    this.options.getParentRoute = other.options.getParentRoute\n    this.children = other.children\n  }\n\n  addChildren: RouteAddChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  > = (children) => {\n    return this._addFileChildren(children) as any\n  }\n\n  _addFileChildren: RouteAddFileChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  > = (children) => {\n    if (Array.isArray(children)) {\n      this.children = children as TChildren\n    }\n\n    if (typeof children === 'object' && children !== null) {\n      this.children = Object.values(children) as TChildren\n    }\n\n    return this as any\n  }\n\n  _addFileTypes: RouteAddFileTypesFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  > = () => {\n    return this as any\n  }\n\n  updateLoader = <TNewLoaderFn>(options: {\n    loader: Constrain<\n      TNewLoaderFn,\n      RouteLoaderFn<\n        TParentRoute,\n        TCustomId,\n        TParams,\n        TLoaderDeps,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >\n    >\n  }) => {\n    Object.assign(this.options, options)\n    return this as unknown as BaseRoute<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TNewLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n  }\n\n  update = (\n    options: UpdatableRouteOptions<\n      TParentRoute,\n      TCustomId,\n      TFullPath,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TLoaderDeps,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ): this => {\n    Object.assign(this.options, options)\n    return this\n  }\n\n  lazy: RouteLazyFn<\n    Route<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n  > = (lazyFn) => {\n    this.lazyFn = lazyFn\n    return this\n  }\n}\n\nexport class BaseRouteApi<TId, TRouter extends AnyRouter = RegisteredRouter> {\n  id: TId\n\n  constructor({ id }: { id: TId }) {\n    this.id = id as any\n  }\n\n  notFound = (opts?: NotFoundError) => {\n    return notFound({ routeId: this.id as string, ...opts })\n  }\n}\n\nexport interface RootRoute<\n  in out TSearchValidator = undefined,\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n  in out TLoaderDeps extends Record<string, any> = {},\n  in out TLoaderFn = undefined,\n  in out TChildren = unknown,\n  in out TFileRouteTypes = unknown,\n> extends Route<\n    any, // TParentRoute\n    '/', // TPath\n    '/', // TFullPath\n    string, // TCustomId\n    RootRouteId, // TId\n    TSearchValidator, // TSearchValidator\n    {}, // TParams\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren, // TChildren\n    TFileRouteTypes\n  > {}\n\nexport class BaseRootRoute<\n  in out TSearchValidator = undefined,\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n  in out TLoaderDeps extends Record<string, any> = {},\n  in out TLoaderFn = undefined,\n  in out TChildren = unknown,\n  in out TFileRouteTypes = unknown,\n> extends BaseRoute<\n  any, // TParentRoute\n  '/', // TPath\n  '/', // TFullPath\n  string, // TCustomId\n  RootRouteId, // TId\n  TSearchValidator, // TSearchValidator\n  {}, // TParams\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren, // TChildren\n  TFileRouteTypes\n> {\n  constructor(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) {\n    super(options as any)\n  }\n}\n\n//\n"], "names": ["options", "rootRouteId", "path", "trimPathLeft", "joinPaths", "notFound"], "mappings": ";;;;;AAqwCO,MAAM,UAeX;AAAA,EAqDA,YACE,SAcA;AA0BF,SAAA,OAAO,CAAC,SAAgE;;AACtE,WAAK,gBAAgB,KAAK;AAE1B,YAAMA,WAAU,KAAK;AAkBrB,YAAM,SAAS,EAACA,YAAA,gBAAAA,SAAS,SAAQ,EAACA,YAAA,gBAAAA,SAAS;AAEtC,WAAA,eAAc,gBAAK,SAAQ,mBAAb;AAEnB,UAAI,QAAQ;AACV,aAAK,QAAQC,KAAA;AAAA,MAAA,WACJ,CAAC,KAAK,aAAa;AAC5B,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MAAA;AAGE,UAAAC,SAA2B,SAASD,KAAA,cAAcD,YAAA,gBAAAA,SAAS;AAG3D,UAAAE,UAAQA,WAAS,KAAK;AACxBA,iBAAOC,kBAAaD,MAAI;AAAA,MAAA;AAGpB,YAAA,YAAWF,YAAA,gBAAAA,SAAS,OAAME;AAG5B,UAAA,KAAK,SACLD,KAAA,cACAG,eAAU;AAAA,QACR,KAAK,YAAY,OAAOH,KAAAA,cAAc,KAAK,KAAK,YAAY;AAAA,QAC5D;AAAA,MAAA,CACD;AAEL,UAAIC,WAASD,KAAAA,aAAa;AACjBC,iBAAA;AAAA,MAAA;AAGT,UAAI,OAAOD,KAAAA,aAAa;AACtB,aAAKG,KAAU,UAAA,CAAC,KAAK,EAAE,CAAC;AAAA,MAAA;AAGpB,YAAA,WACJ,OAAOH,KAAA,cAAc,MAAMG,KAAAA,UAAU,CAAC,KAAK,YAAY,UAAUF,MAAI,CAAC;AAExE,WAAK,QAAQA;AACb,WAAK,MAAM;AACX,WAAK,YAAY;AACjB,WAAK,MAAM;AACX,WAAK,QAAOF,YAAA,gBAAAA,SAAS,QAAO,KAAK,cAAc;AAAA,IACjD;AAEA,SAAA,QAAQ,CAAC,UAAuB;AAC9B,WAAK,QAAQ,MAAM;AACnB,WAAK,MAAM,MAAM;AACjB,WAAK,YAAY,MAAM;AACvB,WAAK,MAAM,MAAM;AACjB,WAAK,OAAO,MAAM;AACb,WAAA,QAAQ,iBAAiB,MAAM,QAAQ;AAC5C,WAAK,WAAW,MAAM;AAAA,IACxB;AAEA,SAAA,cAcI,CAAC,aAAa;AACT,aAAA,KAAK,iBAAiB,QAAQ;AAAA,IACvC;AAEA,SAAA,mBAcI,CAAC,aAAa;AACZ,UAAA,MAAM,QAAQ,QAAQ,GAAG;AAC3B,aAAK,WAAW;AAAA,MAAA;AAGlB,UAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AAChD,aAAA,WAAW,OAAO,OAAO,QAAQ;AAAA,MAAA;AAGjC,aAAA;AAAA,IACT;AAEA,SAAA,gBAcI,MAAM;AACD,aAAA;AAAA,IACT;AAEA,SAAA,eAAe,CAAeA,aAaxB;AACG,aAAA,OAAO,KAAK,SAASA,QAAO;AAC5B,aAAA;AAAA,IAgBT;AAEA,SAAA,SAAS,CACPA,aAYS;AACF,aAAA,OAAO,KAAK,SAASA,QAAO;AAC5B,aAAA;AAAA,IACT;AAEA,SAAA,OAiBI,CAAC,WAAW;AACd,WAAK,SAAS;AACP,aAAA;AAAA,IACT;AA5OO,SAAA,UAAW,WAAmB,CAAC;AAC/B,SAAA,SAAS,EAAC,mCAAS;AAEnB,SAAA,mCAAiB,QAAO,mCAAiB,OAAM;AAC5C,YAAA,IAAI,MAAM,qDAAqD;AAAA,IAAA;AAAA,EACvE;AAAA,EAjDF,IAAW,KAAK;AACd,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,IAAW,KAAK;AACd,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,IAAW,OAAO;AAChB,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,IAAW,WAAW;AACpB,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,IAAW,MAAM;AACf,WAAO,KAAK;AAAA,EAAA;AAwQhB;AAEO,MAAM,aAAgE;AAAA,EAG3E,YAAY,EAAE,MAAmB;AAIjC,SAAA,WAAW,CAAC,SAAyB;AACnC,aAAOK,SAAAA,SAAS,EAAE,SAAS,KAAK,IAAc,GAAG,MAAM;AAAA,IACzD;AALE,SAAK,KAAK;AAAA,EAAA;AAMd;AA4BO,MAAM,sBASH,UAeR;AAAA,EACA,YACE,SAQA;AACA,UAAM,OAAc;AAAA,EAAA;AAExB;;;;"}