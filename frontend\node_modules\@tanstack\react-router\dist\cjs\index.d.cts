export { default as invariant } from 'tiny-invariant';
export { default as warning } from 'tiny-warning';
export { defer, TSR_DEFERRED_PROMISE, isMatch, joinPaths, cleanPath, trimPathLeft, trimPathRight, trimPath, resolvePath, parsePathname, interpolatePath, matchPathname, removeBasepath, matchByPath, encode, decode, rootRouteId, defaultSerializeError, defaultParseSearch, defaultStringifySearch, parseSearchWith, stringifySearchWith, escapeJSON, // SSR
pick, functionalUpdate, replaceEqualDeep, isPlainObject, isPlainArray, deepEqual, shallow, createControlledPromise, retainSearchParams, stripSearchParams, } from '@tanstack/router-core';
export type { AnyRoute, StartSerializer, Serializable, SerializerParse, SerializerParseBy, SerializerStringify, SerializerStringifyBy, DeferredPromiseState, DeferredPromise, ParsedLocation, ParsePathParams, RemoveTrailingSlashes, RemoveLeadingSlashes, ActiveOptions, Segment, ResolveRelativePath, RootRouteId, AnyPathParams, ResolveParams, SearchSchemaInput, AnyContext, RouteContext, PreloadableObj, RoutePathOptions, StaticDataRouteOption, RoutePathOptionsIntersection, UpdatableStaticRouteOption, MetaDescriptor, RouteLinkEntry, ParseParamsFn, SearchFilter, ResolveId, InferFullSearchSchema, InferFullSearchSchemaInput, ErrorRouteProps, ErrorComponentProps, NotFoundRouteProps, TrimPath, TrimPathLeft, TrimPathRight, ParseSplatParams, SplatParams, StringifyParamsFn, ParamsOptions, InferAllParams, InferAllContext, LooseReturnType, LooseAsyncReturnType, ContextReturnType, ContextAsyncReturnType, ResolveLoaderData, ResolveRouteContext, SearchSerializer, SearchParser, TrailingSlashOption, ExtractedEntry, ExtractedStream, ExtractedPromise, StreamState, Manifest, RouterManagedTag, ControlledPromise, Constrain, Expand, MergeAll, Assign, IntersectAssign, ResolveValidatorInput, ResolveValidatorOutput, Register, AnyValidator, DefaultValidator, ValidatorFn, AnySchema, AnyValidatorAdapter, AnyValidatorFn, AnyValidatorObj, ResolveValidatorInputFn, ResolveValidatorOutputFn, ResolveSearchValidatorInput, ResolveSearchValidatorInputFn, Validator, ValidatorAdapter, ValidatorObj, FileRoutesByPath, RouteById, RootRouteOptions, SerializerExtensions, } from '@tanstack/router-core';
export type * from './serializer.cjs';
export { createHistory, createBrowserHistory, createHashHistory, createMemoryHistory, } from '@tanstack/history';
export type { BlockerFn, HistoryLocation, RouterHistory, ParsedPath, HistoryState, } from '@tanstack/history';
export { useAwaited, Await } from './awaited.cjs';
export type { AwaitOptions } from './awaited.cjs';
export { CatchBoundary, ErrorComponent } from './CatchBoundary.cjs';
export { ClientOnly } from './ClientOnly.cjs';
export { FileRoute, createFileRoute, FileRouteLoader, LazyRoute, createLazyRoute, createLazyFileRoute, } from './fileRoute.cjs';
export * from './history.cjs';
export { lazyRouteComponent } from './lazyRouteComponent.cjs';
export { useLinkProps, createLink, Link, linkOptions } from './link.cjs';
export type { InferDescendantToPaths, RelativeToPath, RelativeToParentPath, RelativeToCurrentPath, AbsoluteToPath, RelativeToPathAutoComplete, NavigateOptions, ToOptions, ToMaskOptions, ToSubOptions, ResolveRoute, SearchParamOptions, PathParamOptions, ToPathOption, LinkOptions, MakeOptionalPathParams, FileRouteTypes, RouteContextParameter, BeforeLoadContextParameter, ResolveAllContext, ResolveAllParamsFromParent, ResolveFullSearchSchema, ResolveFullSearchSchemaInput, RouteIds, NavigateFn, BuildLocationFn, FullSearchSchemaOption, MakeRemountDepsOptionsUnion, RemountDepsOptions, ResolveFullPath, AnyRouteWithContext, AnyRouterWithContext, CommitLocationOptions, MatchLocation, UseNavigateResult, AnyRedirect, Redirect, ResolvedRedirect, MakeRouteMatch, MakeRouteMatchUnion, RouteMatch, AnyRouteMatch, RouteContextFn, RouteContextOptions, BeforeLoadFn, BeforeLoadContextOptions, ContextOptions, RouteOptions, FileBaseRouteOptions, BaseRouteOptions, UpdatableRouteOptions, RouteLoaderFn, LoaderFnContext, LazyRouteOptions, AnyRouter, RegisteredRouter, RouterContextOptions, ControllablePromise, InjectedHtmlEntry, RouterOptions, RouterErrorSerializer, RouterState, ListenerFn, BuildNextOptions, RouterConstructorOptions, RouterEvents, RouterEvent, RouterListener, RouteConstraints, RouteMask, MatchRouteOptions, } from '@tanstack/router-core';
export type { UseLinkPropsOptions, ActiveLinkOptions, LinkProps, LinkComponent, LinkComponentProps, CreateLinkProps, } from './link.cjs';
export { Matches, useMatchRoute, MatchRoute, useMatches, useParentMatches, useChildMatches, } from './Matches.cjs';
export type { UseMatchRouteOptions, MakeMatchRouteOptions } from './Matches.cjs';
export { matchContext } from './matchContext.cjs';
export { Match, Outlet } from './Match.cjs';
export { useMatch } from './useMatch.cjs';
export { useLoaderDeps } from './useLoaderDeps.cjs';
export { useLoaderData } from './useLoaderData.cjs';
export { redirect, isRedirect } from '@tanstack/router-core';
export { RouteApi, getRouteApi, Route, createRoute, RootRoute, rootRouteWithContext, createRootRoute, createRootRouteWithContext, createRouteMask, NotFoundRoute, } from './route.cjs';
export type { AnyRootRoute, ReactNode, SyncRouteComponent, AsyncRouteComponent, RouteComponent, ErrorRouteComponent, NotFoundRouteComponent, } from './route.cjs';
export { createRouter, Router } from './router.cjs';
export { componentTypes, lazyFn, SearchParamError, PathParamError, getInitialRouterState, } from '@tanstack/router-core';
export { RouterProvider, RouterContextProvider } from './RouterProvider.cjs';
export type { RouterProps } from './RouterProvider.cjs';
export { useElementScrollRestoration, ScrollRestoration, } from './ScrollRestoration.cjs';
export type { UseBlockerOpts, ShouldBlockFn } from './useBlocker.cjs';
export { useBlocker, Block } from './useBlocker.cjs';
export { useNavigate, Navigate } from './useNavigate.cjs';
export { useParams } from './useParams.cjs';
export { useSearch } from './useSearch.cjs';
export { getRouterContext, } from './routerContext.cjs';
export { useRouteContext } from './useRouteContext.cjs';
export { useRouter } from './useRouter.cjs';
export { useRouterState } from './useRouterState.cjs';
export { useLocation } from './useLocation.cjs';
export { useCanGoBack } from './useCanGoBack.cjs';
export { useLayoutEffect, // SSR
useStableCallback, } from './utils.cjs';
export { CatchNotFound, DefaultGlobalNotFound } from './not-found.cjs';
export { notFound, isNotFound } from '@tanstack/router-core';
export type { NotFoundError } from '@tanstack/router-core';
export type { ValidateLinkOptions, InferStructuralSharing, ValidateUseSearchOptions, ValidateUseParamsOptions, ValidateLinkOptionsArray, } from './typePrimitives.cjs';
export type { ValidateFromPath, ValidateToPath, ValidateSearch, ValidateParams, InferFrom, InferTo, InferMaskTo, InferMaskFrom, ValidateNavigateOptions, ValidateNavigateOptionsArray, ValidateRedirectOptions, ValidateRedirectOptionsArray, ValidateId, InferStrict, InferShouldThrow, InferSelected, ValidateUseSearchResult, ValidateUseParamsResult, } from '@tanstack/router-core';
export { ScriptOnce } from './ScriptOnce.cjs';
export { Asset } from './Asset.cjs';
export { HeadContent } from './HeadContent.cjs';
export { Scripts } from './Scripts.cjs';
