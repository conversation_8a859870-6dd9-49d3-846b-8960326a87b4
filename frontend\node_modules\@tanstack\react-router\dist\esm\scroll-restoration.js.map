{"version": 3, "file": "scroll-restoration.js", "sources": ["../../src/scroll-restoration.tsx"], "sourcesContent": ["import {\n  defaultGetScrollRestorationKey,\n  restoreScroll,\n  storageKey,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport { ScriptOnce } from './ScriptOnce'\n\nexport function ScrollRestoration() {\n  const router = useRouter()\n  const getKey =\n    router.options.getScrollRestorationKey || defaultGetScrollRestorationKey\n  const userKey = getKey(router.latestLocation)\n  const resolvedKey =\n    userKey !== defaultGetScrollRestorationKey(router.latestLocation)\n      ? userKey\n      : null\n\n  if (!router.isScrollRestoring || !router.isServer) {\n    return null\n  }\n\n  return (\n    <ScriptOnce\n      children={`(${restoreScroll.toString()})(${JSON.stringify(storageKey)},${JSON.stringify(resolvedKey)}, undefined, true)`}\n      log={false}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAQO,SAAS,oBAAoB;AAClC,QAAM,SAAS,UAAU;AACnB,QAAA,SACJ,OAAO,QAAQ,2BAA2B;AACtC,QAAA,UAAU,OAAO,OAAO,cAAc;AAC5C,QAAM,cACJ,YAAY,+BAA+B,OAAO,cAAc,IAC5D,UACA;AAEN,MAAI,CAAC,OAAO,qBAAqB,CAAC,OAAO,UAAU;AAC1C,WAAA;AAAA,EAAA;AAIP,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,UAAU,IAAI,cAAc,SAAU,CAAA,KAAK,KAAK,UAAU,UAAU,CAAC,IAAI,KAAK,UAAU,WAAW,CAAC;AAAA,MACpG,KAAK;AAAA,IAAA;AAAA,EACP;AAEJ;"}