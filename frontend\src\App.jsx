import React, { useState } from "react";
import "./index.css";
import HomePage from "./pages/HomePage.jsx";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AuthPage from "./pages/AuthPage.jsx";

const queryClient = new QueryClient();

function App() {
  const [currentPage, setCurrentPage] = useState("auth"); // 'home' or 'auth' - start with auth
  const [user, setUser] = useState(null);

  const handleSwitchToHome = (userData) => {
    setUser(userData);
    setCurrentPage("home");
  };

  const handleLogout = () => {
    setUser(null);
    setCurrentPage("auth");
  };

  const goToAuth = () => {
    setCurrentPage("auth");
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="app-container">
        {currentPage === "home" ? (
          <HomePage user={user} onLogout={handleLogout} onGoToAuth={goToAuth} />
        ) : (
          <AuthPage onSwitchToHome={handleSwitchToHome} />
        )}
      </div>
    </QueryClientProvider>
  );
}

export default App;
