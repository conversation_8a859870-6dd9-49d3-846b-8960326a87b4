import React, { useState, useEffect } from "react";
import "./index.css";
import HomePage from "./pages/HomePage.jsx";
import DashboardPage from "./pages/DashboardPage.jsx";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AuthPage from "./pages/AuthPage.jsx";
import { getErrorMessage } from "./utils/errorHandler.js";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

function App() {
  const [currentPage, setCurrentPage] = useState("auth"); // 'home', 'dashboard', or 'auth'
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check for existing user session on app load
  useEffect(() => {
    const checkUserSession = () => {
      try {
        const savedUser = localStorage.getItem("user");
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          setUser(userData);
          setCurrentPage("home");
        }
      } catch (error) {
        console.error("Error loading user session:", getErrorMessage(error));
        localStorage.removeItem("user"); // Clear corrupted data
      } finally {
        setLoading(false);
      }
    };

    checkUserSession();
  }, []);

  const handleSwitchToHome = (userData) => {
    try {
      setUser(userData);
      setCurrentPage("home");
      // Save user session
      localStorage.setItem("user", JSON.stringify(userData));
    } catch (error) {
      console.error("Error saving user session:", getErrorMessage(error));
    }
  };

  const handleLogout = async () => {
    try {
      // Clear user session
      setUser(null);
      setCurrentPage("auth");
      localStorage.removeItem("user");

      // Optional: Call logout API
      // await logoutUser();
    } catch (error) {
      console.error("Error during logout:", getErrorMessage(error));
      // Still clear local state even if API call fails
      setUser(null);
      setCurrentPage("auth");
      localStorage.removeItem("user");
    }
  };

  const goToAuth = () => {
    setCurrentPage("auth");
  };

  const goToDashboard = () => {
    setCurrentPage("dashboard");
  };

  const goToHome = () => {
    setCurrentPage("home");
  };

  // Show loading spinner while checking session
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="app-container">
        {currentPage === "home" ? (
          <HomePage
            user={user}
            onLogout={handleLogout}
            onGoToAuth={goToAuth}
            onGoToDashboard={goToDashboard}
            onGoToHome={goToHome}
          />
        ) : currentPage === "dashboard" ? (
          <DashboardPage
            user={user}
            onLogout={handleLogout}
            onGoToAuth={goToAuth}
            onGoToHome={goToHome}
          />
        ) : (
          <AuthPage onSwitchToHome={handleSwitchToHome} />
        )}
      </div>
    </QueryClientProvider>
  );
}

export default App;
