import React, { useState } from "react";
import "./index.css";
import HomePage from "./pages/HomePage.jsx";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AuthPage from "./pages/AuthPage.jsx";

const queryClient = new QueryClient();

function App() {
  const [currentPage, setCurrentPage] = useState("auth"); // 'home' or 'auth' - start with auth
  const [user, setUser] = useState(null);

  const handleSwitchToHome = (userData) => {
    setUser(userData);
    setCurrentPage("home");
  };

  const handleLogout = () => {
    setUser(null);
    setCurrentPage("auth");
  };

  const goToAuth = () => {
    setCurrentPage("auth");
  };

  console.log("App render - currentPage:", currentPage, "user:", user);

  return (
    <QueryClientProvider client={queryClient}>
      <div className="app-container">
        {/* Debug indicator */}
        <div className="fixed top-0 left-0 bg-black text-white px-2 py-1 text-xs z-50">
          Page: {currentPage} | User: {user ? "Logged In" : "Not Logged In"}
        </div>

        {currentPage === "home" ? (
          <div key="home-page">
            <HomePage
              user={user}
              onLogout={handleLogout}
              onGoToAuth={goToAuth}
            />
          </div>
        ) : (
          <div key="auth-page">
            <AuthPage onSwitchToHome={handleSwitchToHome} />
          </div>
        )}
      </div>
    </QueryClientProvider>
  );
}

export default App;
