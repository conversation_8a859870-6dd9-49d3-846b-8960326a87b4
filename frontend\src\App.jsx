import React from "react";
import "./index.css";
import UrlShortener from "./components/UrlForm.jsx";
import HomePage from "./pages/HomePage.jsx";
import {createRoot} from 'react-dom/client'
import {QueryClient, QueryClientProvider} from '@tanstack/react-query'
import LoginForm from "./components/LoginForm.jsx";
import AuthPage from "./pages/AuthPage.jsx";
const queryClient = new QueryClient()

function App() {
  return (
    
    <QueryClientProvider client={queryClient}>
    <HomePage/>
    <AuthPage/>
    </QueryClientProvider>
    
  );
}

export default App;
