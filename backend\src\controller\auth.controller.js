import { registerUser } from "../services/auth.service.js";
import User from "../models/user.model.js";
import { cookieOptions } from "../config/config.js";
//import wrapAsync from "../utils/tryCatchWrapper.js";
export const register = async (req, res) => {
  const { name, email, password } = req.body;
  const token = await registerUser(name, email, password);
  res.cookie("accesstoken", token, cookieOptions);
  res.status(201).json(token);
};
export const login = async (req, res) => {
 const {  email, password } = req.body;
  const token = await loginUser( email, password);
  res.cookie("accesstoken", token, cookieOptions);
  res.status(201).json(token);
};
