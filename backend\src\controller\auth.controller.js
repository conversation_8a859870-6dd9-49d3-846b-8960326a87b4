import { registerUser } from "../services/auth.service.js";
import User from "../models/user.model.js";
//import wrapAsync from "../utils/tryCatchWrapper.js";
export const register = async (req, res) => {
  const { name, email, password } = req.body;
  const token = await registerUser(name, email, password);
  res.status(201).json(token);
};
export const login = async (req, res) => {
  res.send("Login");
};
