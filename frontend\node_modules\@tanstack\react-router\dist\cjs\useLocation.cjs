"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const useRouterState = require("./useRouterState.cjs");
function useLocation(opts) {
  return useRouterState.useRouterState({
    select: (state) => (opts == null ? void 0 : opts.select) ? opts.select(state.location) : state.location
  });
}
exports.useLocation = useLocation;
//# sourceMappingURL=useLocation.cjs.map
