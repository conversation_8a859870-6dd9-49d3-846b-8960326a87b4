services:
  - type: web
    name: url-shortener-backend
    env: node
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: JWT_SECRET
        value: thisisasecretkey
      - key: APP_URL
        value: https://url-shortner-qnp4.onrender.com/
      - key: MONGO_URI
        value: mongodb+srv://Sanskar:<EMAIL>/urlshortener?retryWrites=true&w=majority
      - key: PORT
        value: 5000
