const API_BASE_URL = "http://localhost:5000";

export const shortenUrl = async (url) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ url }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    const data = await response.json();
    return data.data; // Return the shortened URL from the data field
  } catch (error) {
    console.error("Error shortening URL:", error);
    throw error;
  }
};

export const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};
