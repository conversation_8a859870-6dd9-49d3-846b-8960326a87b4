{"version": 3, "file": "path.cjs", "sources": ["../../src/path.ts"], "sourcesContent": ["import { last } from './utils'\nimport type { MatchLocation } from './RouterProvider'\nimport type { AnyPathParams } from './route'\n\nexport interface Segment {\n  type: 'pathname' | 'param' | 'wildcard'\n  value: string\n}\n\nexport function joinPaths(paths: Array<string | undefined>) {\n  return cleanPath(\n    paths\n      .filter((val) => {\n        return val !== undefined\n      })\n      .join('/'),\n  )\n}\n\nexport function cleanPath(path: string) {\n  // remove double slashes\n  return path.replace(/\\/{2,}/g, '/')\n}\n\nexport function trimPathLeft(path: string) {\n  return path === '/' ? path : path.replace(/^\\/{1,}/, '')\n}\n\nexport function trimPathRight(path: string) {\n  return path === '/' ? path : path.replace(/\\/{1,}$/, '')\n}\n\nexport function trimPath(path: string) {\n  return trimPathRight(trimPathLeft(path))\n}\n\nexport function removeTrailingSlash(value: string, basepath: string): string {\n  if (value?.endsWith('/') && value !== '/' && value !== `${basepath}/`) {\n    return value.slice(0, -1)\n  }\n  return value\n}\n\n// intended to only compare path name\n// see the usage in the isActive under useLinkProps\n// /sample/path1 = /sample/path1/\n// /sample/path1/some <> /sample/path1\nexport function exactPathTest(\n  pathName1: string,\n  pathName2: string,\n  basepath: string,\n): boolean {\n  return (\n    removeTrailingSlash(pathName1, basepath) ===\n    removeTrailingSlash(pathName2, basepath)\n  )\n}\n\n// When resolving relative paths, we treat all paths as if they are trailing slash\n// documents. All trailing slashes are removed after the path is resolved.\n// Here are a few examples:\n//\n// /a/b/c + ./d = /a/b/c/d\n// /a/b/c + ../d = /a/b/d\n// /a/b/c + ./d/ = /a/b/c/d\n// /a/b/c + ../d/ = /a/b/d\n// /a/b/c + ./ = /a/b/c\n//\n// Absolute paths that start with `/` short circuit the resolution process to the root\n// path.\n//\n// Here are some examples:\n//\n// /a/b/c + /d = /d\n// /a/b/c + /d/ = /d\n// /a/b/c + / = /\n//\n// Non-.-prefixed paths are still treated as relative paths, resolved like `./`\n//\n// Here are some examples:\n//\n// /a/b/c + d = /a/b/c/d\n// /a/b/c + d/ = /a/b/c/d\n// /a/b/c + d/e = /a/b/c/d/e\ninterface ResolvePathOptions {\n  basepath: string\n  base: string\n  to: string\n  trailingSlash?: 'always' | 'never' | 'preserve'\n  caseSensitive?: boolean\n}\n\nexport function resolvePath({\n  basepath,\n  base,\n  to,\n  trailingSlash = 'never',\n  caseSensitive,\n}: ResolvePathOptions) {\n  base = removeBasepath(basepath, base, caseSensitive)\n  to = removeBasepath(basepath, to, caseSensitive)\n\n  let baseSegments = parsePathname(base)\n  const toSegments = parsePathname(to)\n\n  if (baseSegments.length > 1 && last(baseSegments)?.value === '/') {\n    baseSegments.pop()\n  }\n\n  toSegments.forEach((toSegment, index) => {\n    if (toSegment.value === '/') {\n      if (!index) {\n        // Leading slash\n        baseSegments = [toSegment]\n      } else if (index === toSegments.length - 1) {\n        // Trailing Slash\n        baseSegments.push(toSegment)\n      } else {\n        // ignore inter-slashes\n      }\n    } else if (toSegment.value === '..') {\n      baseSegments.pop()\n    } else if (toSegment.value === '.') {\n      // ignore\n    } else {\n      baseSegments.push(toSegment)\n    }\n  })\n\n  if (baseSegments.length > 1) {\n    if (last(baseSegments)?.value === '/') {\n      if (trailingSlash === 'never') {\n        baseSegments.pop()\n      }\n    } else if (trailingSlash === 'always') {\n      baseSegments.push({ type: 'pathname', value: '/' })\n    }\n  }\n\n  const joined = joinPaths([basepath, ...baseSegments.map((d) => d.value)])\n  return cleanPath(joined)\n}\n\nexport function parsePathname(pathname?: string): Array<Segment> {\n  if (!pathname) {\n    return []\n  }\n\n  pathname = cleanPath(pathname)\n\n  const segments: Array<Segment> = []\n\n  if (pathname.slice(0, 1) === '/') {\n    pathname = pathname.substring(1)\n    segments.push({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  if (!pathname) {\n    return segments\n  }\n\n  // Remove empty segments and '.' segments\n  const split = pathname.split('/').filter(Boolean)\n\n  segments.push(\n    ...split.map((part): Segment => {\n      if (part === '$' || part === '*') {\n        return {\n          type: 'wildcard',\n          value: part,\n        }\n      }\n\n      if (part.charAt(0) === '$') {\n        return {\n          type: 'param',\n          value: part,\n        }\n      }\n\n      return {\n        type: 'pathname',\n        value: part.includes('%25')\n          ? part\n              .split('%25')\n              .map((segment) => decodeURI(segment))\n              .join('%25')\n          : decodeURI(part),\n      }\n    }),\n  )\n\n  if (pathname.slice(-1) === '/') {\n    pathname = pathname.substring(1)\n    segments.push({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  return segments\n}\n\ninterface InterpolatePathOptions {\n  path?: string\n  params: Record<string, unknown>\n  leaveWildcards?: boolean\n  leaveParams?: boolean\n  // Map of encoded chars to decoded chars (e.g. '%40' -> '@') that should remain decoded in path params\n  decodeCharMap?: Map<string, string>\n}\n\ntype InterPolatePathResult = {\n  interpolatedPath: string\n  usedParams: Record<string, unknown>\n  isMissingParams: boolean // true if any params were not available when being looked up in the params object\n}\nexport function interpolatePath({\n  path,\n  params,\n  leaveWildcards,\n  leaveParams,\n  decodeCharMap,\n}: InterpolatePathOptions): InterPolatePathResult {\n  const interpolatedPathSegments = parsePathname(path)\n\n  function encodeParam(key: string): any {\n    const value = params[key]\n    const isValueString = typeof value === 'string'\n\n    if (['*', '_splat'].includes(key)) {\n      // the splat/catch-all routes shouldn't have the '/' encoded out\n      return isValueString ? encodeURI(value) : value\n    } else {\n      return isValueString ? encodePathParam(value, decodeCharMap) : value\n    }\n  }\n\n  // Tracking if any params are missing in the `params` object\n  // when interpolating the path\n  let isMissingParams = false\n\n  const usedParams: Record<string, unknown> = {}\n  const interpolatedPath = joinPaths(\n    interpolatedPathSegments.map((segment) => {\n      if (segment.type === 'wildcard') {\n        usedParams._splat = params._splat\n        const value = encodeParam('_splat')\n        if (leaveWildcards) return `${segment.value}${value ?? ''}`\n        return value\n      }\n\n      if (segment.type === 'param') {\n        const key = segment.value.substring(1)\n        if (!isMissingParams && !(key in params)) {\n          isMissingParams = true\n        }\n        usedParams[key] = params[key]\n        if (leaveParams) {\n          const value = encodeParam(segment.value)\n          return `${segment.value}${value ?? ''}`\n        }\n        return encodeParam(key) ?? 'undefined'\n      }\n\n      return segment.value\n    }),\n  )\n  return { usedParams, interpolatedPath, isMissingParams }\n}\n\nfunction encodePathParam(value: string, decodeCharMap?: Map<string, string>) {\n  let encoded = encodeURIComponent(value)\n  if (decodeCharMap) {\n    for (const [encodedChar, char] of decodeCharMap) {\n      encoded = encoded.replaceAll(encodedChar, char)\n    }\n  }\n  return encoded\n}\n\nexport function matchPathname(\n  basepath: string,\n  currentPathname: string,\n  matchLocation: Pick<MatchLocation, 'to' | 'fuzzy' | 'caseSensitive'>,\n): AnyPathParams | undefined {\n  const pathParams = matchByPath(basepath, currentPathname, matchLocation)\n  // const searchMatched = matchBySearch(location.search, matchLocation)\n\n  if (matchLocation.to && !pathParams) {\n    return\n  }\n\n  return pathParams ?? {}\n}\n\nexport function removeBasepath(\n  basepath: string,\n  pathname: string,\n  caseSensitive: boolean = false,\n) {\n  // normalize basepath and pathname for case-insensitive comparison if needed\n  const normalizedBasepath = caseSensitive ? basepath : basepath.toLowerCase()\n  const normalizedPathname = caseSensitive ? pathname : pathname.toLowerCase()\n\n  switch (true) {\n    // default behaviour is to serve app from the root - pathname\n    // left untouched\n    case normalizedBasepath === '/':\n      return pathname\n\n    // shortcut for removing the basepath if it matches the pathname\n    case normalizedPathname === normalizedBasepath:\n      return ''\n\n    // in case pathname is shorter than basepath - there is\n    // nothing to remove\n    case pathname.length < basepath.length:\n      return pathname\n\n    // avoid matching partial segments - strict equality handled\n    // earlier, otherwise, basepath separated from pathname with\n    // separator, therefore lack of separator means partial\n    // segment match (`/app` should not match `/application`)\n    case normalizedPathname[normalizedBasepath.length] !== '/':\n      return pathname\n\n    // remove the basepath from the pathname if it starts with it\n    case normalizedPathname.startsWith(normalizedBasepath):\n      return pathname.slice(basepath.length)\n\n    // otherwise, return the pathname as is\n    default:\n      return pathname\n  }\n}\n\nexport function matchByPath(\n  basepath: string,\n  from: string,\n  matchLocation: Pick<MatchLocation, 'to' | 'caseSensitive' | 'fuzzy'>,\n): Record<string, string> | undefined {\n  // check basepath first\n  if (basepath !== '/' && !from.startsWith(basepath)) {\n    return undefined\n  }\n  // Remove the base path from the pathname\n  from = removeBasepath(basepath, from, matchLocation.caseSensitive)\n  // Default to to $ (wildcard)\n  const to = removeBasepath(\n    basepath,\n    `${matchLocation.to ?? '$'}`,\n    matchLocation.caseSensitive,\n  )\n\n  // Parse the from and to\n  const baseSegments = parsePathname(from)\n  const routeSegments = parsePathname(to)\n\n  if (!from.startsWith('/')) {\n    baseSegments.unshift({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  if (!to.startsWith('/')) {\n    routeSegments.unshift({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  const params: Record<string, string> = {}\n\n  const isMatch = (() => {\n    for (\n      let i = 0;\n      i < Math.max(baseSegments.length, routeSegments.length);\n      i++\n    ) {\n      const baseSegment = baseSegments[i]\n      const routeSegment = routeSegments[i]\n\n      const isLastBaseSegment = i >= baseSegments.length - 1\n      const isLastRouteSegment = i >= routeSegments.length - 1\n\n      if (routeSegment) {\n        if (routeSegment.type === 'wildcard') {\n          const _splat = decodeURI(\n            joinPaths(baseSegments.slice(i).map((d) => d.value)),\n          )\n          // TODO: Deprecate *\n          params['*'] = _splat\n          params['_splat'] = _splat\n          return true\n        }\n\n        if (routeSegment.type === 'pathname') {\n          if (routeSegment.value === '/' && !baseSegment?.value) {\n            return true\n          }\n\n          if (baseSegment) {\n            if (matchLocation.caseSensitive) {\n              if (routeSegment.value !== baseSegment.value) {\n                return false\n              }\n            } else if (\n              routeSegment.value.toLowerCase() !==\n              baseSegment.value.toLowerCase()\n            ) {\n              return false\n            }\n          }\n        }\n\n        if (!baseSegment) {\n          return false\n        }\n\n        if (routeSegment.type === 'param') {\n          if (baseSegment.value === '/') {\n            return false\n          }\n          if (baseSegment.value.charAt(0) !== '$') {\n            params[routeSegment.value.substring(1)] = decodeURIComponent(\n              baseSegment.value,\n            )\n          }\n        }\n      }\n\n      if (!isLastBaseSegment && isLastRouteSegment) {\n        params['**'] = joinPaths(baseSegments.slice(i + 1).map((d) => d.value))\n        return !!matchLocation.fuzzy && routeSegment?.value !== '/'\n      }\n    }\n\n    return true\n  })()\n\n  return isMatch ? params : undefined\n}\n"], "names": ["last"], "mappings": ";;;AASO,SAAS,UAAU,OAAkC;AACnD,SAAA;AAAA,IACL,MACG,OAAO,CAAC,QAAQ;AACf,aAAO,QAAQ;AAAA,IAAA,CAChB,EACA,KAAK,GAAG;AAAA,EACb;AACF;AAEO,SAAS,UAAU,MAAc;AAE/B,SAAA,KAAK,QAAQ,WAAW,GAAG;AACpC;AAEO,SAAS,aAAa,MAAc;AACzC,SAAO,SAAS,MAAM,OAAO,KAAK,QAAQ,WAAW,EAAE;AACzD;AAEO,SAAS,cAAc,MAAc;AAC1C,SAAO,SAAS,MAAM,OAAO,KAAK,QAAQ,WAAW,EAAE;AACzD;AAEO,SAAS,SAAS,MAAc;AAC9B,SAAA,cAAc,aAAa,IAAI,CAAC;AACzC;AAEgB,SAAA,oBAAoB,OAAe,UAA0B;AACvE,OAAA,+BAAO,SAAS,SAAQ,UAAU,OAAO,UAAU,GAAG,QAAQ,KAAK;AAC9D,WAAA,MAAM,MAAM,GAAG,EAAE;AAAA,EAAA;AAEnB,SAAA;AACT;AAMgB,SAAA,cACd,WACA,WACA,UACS;AACT,SACE,oBAAoB,WAAW,QAAQ,MACvC,oBAAoB,WAAW,QAAQ;AAE3C;AAoCO,SAAS,YAAY;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB;AACF,GAAuB;;AACd,SAAA,eAAe,UAAU,MAAM,aAAa;AAC9C,OAAA,eAAe,UAAU,IAAI,aAAa;AAE3C,MAAA,eAAe,cAAc,IAAI;AAC/B,QAAA,aAAa,cAAc,EAAE;AAEnC,MAAI,aAAa,SAAS,OAAKA,gBAAK,YAAY,MAAjBA,mBAAoB,WAAU,KAAK;AAChE,iBAAa,IAAI;AAAA,EAAA;AAGR,aAAA,QAAQ,CAAC,WAAW,UAAU;AACnC,QAAA,UAAU,UAAU,KAAK;AAC3B,UAAI,CAAC,OAAO;AAEV,uBAAe,CAAC,SAAS;AAAA,MAChB,WAAA,UAAU,WAAW,SAAS,GAAG;AAE1C,qBAAa,KAAK,SAAS;AAAA,MAAA,MACtB;AAAA,IAEP,WACS,UAAU,UAAU,MAAM;AACnC,mBAAa,IAAI;AAAA,IACnB,WAAW,UAAU,UAAU,IAAK;AAAA,SAE7B;AACL,mBAAa,KAAK,SAAS;AAAA,IAAA;AAAA,EAC7B,CACD;AAEG,MAAA,aAAa,SAAS,GAAG;AAC3B,UAAIA,gBAAK,YAAY,MAAjBA,mBAAoB,WAAU,KAAK;AACrC,UAAI,kBAAkB,SAAS;AAC7B,qBAAa,IAAI;AAAA,MAAA;AAAA,IACnB,WACS,kBAAkB,UAAU;AACrC,mBAAa,KAAK,EAAE,MAAM,YAAY,OAAO,KAAK;AAAA,IAAA;AAAA,EACpD;AAGF,QAAM,SAAS,UAAU,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxE,SAAO,UAAU,MAAM;AACzB;AAEO,SAAS,cAAc,UAAmC;AAC/D,MAAI,CAAC,UAAU;AACb,WAAO,CAAC;AAAA,EAAA;AAGV,aAAW,UAAU,QAAQ;AAE7B,QAAM,WAA2B,CAAC;AAElC,MAAI,SAAS,MAAM,GAAG,CAAC,MAAM,KAAK;AACrB,eAAA,SAAS,UAAU,CAAC;AAC/B,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,IAAA,CACR;AAAA,EAAA;AAGH,MAAI,CAAC,UAAU;AACN,WAAA;AAAA,EAAA;AAIT,QAAM,QAAQ,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO;AAEvC,WAAA;AAAA,IACP,GAAG,MAAM,IAAI,CAAC,SAAkB;AAC1B,UAAA,SAAS,OAAO,SAAS,KAAK;AACzB,eAAA;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MAAA;AAGF,UAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AACnB,eAAA;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MAAA;AAGK,aAAA;AAAA,QACL,MAAM;AAAA,QACN,OAAO,KAAK,SAAS,KAAK,IACtB,KACG,MAAM,KAAK,EACX,IAAI,CAAC,YAAY,UAAU,OAAO,CAAC,EACnC,KAAK,KAAK,IACb,UAAU,IAAI;AAAA,MACpB;AAAA,IACD,CAAA;AAAA,EACH;AAEA,MAAI,SAAS,MAAM,EAAE,MAAM,KAAK;AACnB,eAAA,SAAS,UAAU,CAAC;AAC/B,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,IAAA,CACR;AAAA,EAAA;AAGI,SAAA;AACT;AAgBO,SAAS,gBAAgB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAkD;AAC1C,QAAA,2BAA2B,cAAc,IAAI;AAEnD,WAAS,YAAY,KAAkB;AAC/B,UAAA,QAAQ,OAAO,GAAG;AAClB,UAAA,gBAAgB,OAAO,UAAU;AAEvC,QAAI,CAAC,KAAK,QAAQ,EAAE,SAAS,GAAG,GAAG;AAE1B,aAAA,gBAAgB,UAAU,KAAK,IAAI;AAAA,IAAA,OACrC;AACL,aAAO,gBAAgB,gBAAgB,OAAO,aAAa,IAAI;AAAA,IAAA;AAAA,EACjE;AAKF,MAAI,kBAAkB;AAEtB,QAAM,aAAsC,CAAC;AAC7C,QAAM,mBAAmB;AAAA,IACvB,yBAAyB,IAAI,CAAC,YAAY;AACpC,UAAA,QAAQ,SAAS,YAAY;AAC/B,mBAAW,SAAS,OAAO;AACrB,cAAA,QAAQ,YAAY,QAAQ;AAClC,YAAI,eAAuB,QAAA,GAAG,QAAQ,KAAK,GAAG,SAAS,EAAE;AAClD,eAAA;AAAA,MAAA;AAGL,UAAA,QAAQ,SAAS,SAAS;AAC5B,cAAM,MAAM,QAAQ,MAAM,UAAU,CAAC;AACrC,YAAI,CAAC,mBAAmB,EAAE,OAAO,SAAS;AACtB,4BAAA;AAAA,QAAA;AAET,mBAAA,GAAG,IAAI,OAAO,GAAG;AAC5B,YAAI,aAAa;AACT,gBAAA,QAAQ,YAAY,QAAQ,KAAK;AACvC,iBAAO,GAAG,QAAQ,KAAK,GAAG,SAAS,EAAE;AAAA,QAAA;AAEhC,eAAA,YAAY,GAAG,KAAK;AAAA,MAAA;AAG7B,aAAO,QAAQ;AAAA,IAChB,CAAA;AAAA,EACH;AACO,SAAA,EAAE,YAAY,kBAAkB,gBAAgB;AACzD;AAEA,SAAS,gBAAgB,OAAe,eAAqC;AACvE,MAAA,UAAU,mBAAmB,KAAK;AACtC,MAAI,eAAe;AACjB,eAAW,CAAC,aAAa,IAAI,KAAK,eAAe;AACrC,gBAAA,QAAQ,WAAW,aAAa,IAAI;AAAA,IAAA;AAAA,EAChD;AAEK,SAAA;AACT;AAEgB,SAAA,cACd,UACA,iBACA,eAC2B;AAC3B,QAAM,aAAa,YAAY,UAAU,iBAAiB,aAAa;AAGnE,MAAA,cAAc,MAAM,CAAC,YAAY;AACnC;AAAA,EAAA;AAGF,SAAO,cAAc,CAAC;AACxB;AAEO,SAAS,eACd,UACA,UACA,gBAAyB,OACzB;AAEA,QAAM,qBAAqB,gBAAgB,WAAW,SAAS,YAAY;AAC3E,QAAM,qBAAqB,gBAAgB,WAAW,SAAS,YAAY;AAE3E,UAAQ,MAAM;AAAA;AAAA;AAAA,IAGZ,KAAK,uBAAuB;AACnB,aAAA;AAAA;AAAA,IAGT,KAAK,uBAAuB;AACnB,aAAA;AAAA;AAAA;AAAA,IAIT,KAAK,SAAS,SAAS,SAAS;AACvB,aAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMT,KAAK,mBAAmB,mBAAmB,MAAM,MAAM;AAC9C,aAAA;AAAA;AAAA,IAGT,KAAK,mBAAmB,WAAW,kBAAkB;AAC5C,aAAA,SAAS,MAAM,SAAS,MAAM;AAAA;AAAA,IAGvC;AACS,aAAA;AAAA,EAAA;AAEb;AAEgB,SAAA,YACd,UACA,MACA,eACoC;AAEpC,MAAI,aAAa,OAAO,CAAC,KAAK,WAAW,QAAQ,GAAG;AAC3C,WAAA;AAAA,EAAA;AAGT,SAAO,eAAe,UAAU,MAAM,cAAc,aAAa;AAEjE,QAAM,KAAK;AAAA,IACT;AAAA,IACA,GAAG,cAAc,MAAM,GAAG;AAAA,IAC1B,cAAc;AAAA,EAChB;AAGM,QAAA,eAAe,cAAc,IAAI;AACjC,QAAA,gBAAgB,cAAc,EAAE;AAEtC,MAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,iBAAa,QAAQ;AAAA,MACnB,MAAM;AAAA,MACN,OAAO;AAAA,IAAA,CACR;AAAA,EAAA;AAGH,MAAI,CAAC,GAAG,WAAW,GAAG,GAAG;AACvB,kBAAc,QAAQ;AAAA,MACpB,MAAM;AAAA,MACN,OAAO;AAAA,IAAA,CACR;AAAA,EAAA;AAGH,QAAM,SAAiC,CAAC;AAExC,QAAM,WAAW,MAAM;AAEf,aAAA,IAAI,GACR,IAAI,KAAK,IAAI,aAAa,QAAQ,cAAc,MAAM,GACtD,KACA;AACM,YAAA,cAAc,aAAa,CAAC;AAC5B,YAAA,eAAe,cAAc,CAAC;AAE9B,YAAA,oBAAoB,KAAK,aAAa,SAAS;AAC/C,YAAA,qBAAqB,KAAK,cAAc,SAAS;AAEvD,UAAI,cAAc;AACZ,YAAA,aAAa,SAAS,YAAY;AACpC,gBAAM,SAAS;AAAA,YACb,UAAU,aAAa,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,UACrD;AAEA,iBAAO,GAAG,IAAI;AACd,iBAAO,QAAQ,IAAI;AACZ,iBAAA;AAAA,QAAA;AAGL,YAAA,aAAa,SAAS,YAAY;AACpC,cAAI,aAAa,UAAU,OAAO,EAAC,2CAAa,QAAO;AAC9C,mBAAA;AAAA,UAAA;AAGT,cAAI,aAAa;AACf,gBAAI,cAAc,eAAe;AAC3B,kBAAA,aAAa,UAAU,YAAY,OAAO;AACrC,uBAAA;AAAA,cAAA;AAAA,YACT,WAEA,aAAa,MAAM,kBACnB,YAAY,MAAM,eAClB;AACO,qBAAA;AAAA,YAAA;AAAA,UACT;AAAA,QACF;AAGF,YAAI,CAAC,aAAa;AACT,iBAAA;AAAA,QAAA;AAGL,YAAA,aAAa,SAAS,SAAS;AAC7B,cAAA,YAAY,UAAU,KAAK;AACtB,mBAAA;AAAA,UAAA;AAET,cAAI,YAAY,MAAM,OAAO,CAAC,MAAM,KAAK;AACvC,mBAAO,aAAa,MAAM,UAAU,CAAC,CAAC,IAAI;AAAA,cACxC,YAAY;AAAA,YACd;AAAA,UAAA;AAAA,QACF;AAAA,MACF;AAGE,UAAA,CAAC,qBAAqB,oBAAoB;AAC5C,eAAO,IAAI,IAAI,UAAU,aAAa,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AACtE,eAAO,CAAC,CAAC,cAAc,UAAS,6CAAc,WAAU;AAAA,MAAA;AAAA,IAC1D;AAGK,WAAA;AAAA,EAAA,GACN;AAEH,SAAO,UAAU,SAAS;AAC5B;;;;;;;;;;;;;;"}