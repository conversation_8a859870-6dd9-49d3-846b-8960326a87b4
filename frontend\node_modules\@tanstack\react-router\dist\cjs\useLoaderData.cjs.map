{"version": 3, "file": "useLoaderData.cjs", "sources": ["../../src/useLoaderData.tsx"], "sourcesContent": ["import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseLoaderData,\n  StrictOrFrom,\n  UseLoaderDataResult,\n} from '@tanstack/router-core'\n\nexport interface UseLoaderDataBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    match: ResolveUseLoaderData<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLoaderDataOptions<\n  TRouter extends AnyRouter,\n  T<PERSON>rom extends string | undefined,\n  TStrict extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseLoaderDataBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseLoaderDataRoute<out TId> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLoaderDataBaseOptions<\n    TRouter,\n    TId,\n    true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseLoaderDataResult<TRouter, TId, true, TSelected>\n\nexport function useLoaderData<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseLoaderDataOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TSelected,\n    TStructuralSharing\n  >,\n): UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    structuralSharing: opts.structuralSharing,\n    select: (s: any) => {\n      return opts.select ? opts.select(s.loaderData) : s.loaderData\n    },\n  } as any) as UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected>\n}\n"], "names": ["useMatch"], "mappings": ";;;AAwDO,SAAS,cAOd,MAOyD;AACzD,SAAOA,kBAAS;AAAA,IACd,MAAM,KAAK;AAAA,IACX,QAAQ,KAAK;AAAA,IACb,mBAAmB,KAAK;AAAA,IACxB,QAAQ,CAAC,MAAW;AAClB,aAAO,KAAK,SAAS,KAAK,OAAO,EAAE,UAAU,IAAI,EAAE;AAAA,IAAA;AAAA,EACrD,CACM;AACV;;"}