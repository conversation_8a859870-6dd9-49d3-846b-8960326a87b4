import dotenv from "dotenv";
dotenv.config();

console.log("✅ Environment loaded");
console.log("MongoDB URI:", process.env.MONGO_URI);
console.log("JWT Secret:", process.env.JWT_SECRET ? "✅ Set" : "❌ Missing");

import express from "express";
console.log("✅ Express imported");

import mongoose from "mongoose";
console.log("✅ Mongoose imported");

const app = express();
console.log("✅ Express app created");

// Test MongoDB connection
const testConnection = async () => {
  try {
    console.log("🔄 Testing MongoDB connection...");
    await mongoose.connect(process.env.MONGO_URI);
    console.log("✅ MongoDB Connected Successfully!");
    mongoose.disconnect();
  } catch (err) {
    console.log("❌ MongoDB Connection Failed:", err.message);
  }
};

testConnection();

console.log("✅ Test completed");
