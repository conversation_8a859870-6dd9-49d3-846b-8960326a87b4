{"version": 3, "file": "searchParams.cjs", "sources": ["../../src/searchParams.ts"], "sourcesContent": ["import { decode, encode } from './qss'\nimport type { AnySchema } from './validators'\n\nexport const defaultParseSearch = parseSearchWith(JSON.parse)\nexport const defaultStringifySearch = stringifySearchWith(\n  JSON.stringify,\n  JSON.parse,\n)\n\nexport function parseSearchWith(parser: (str: string) => any) {\n  return (searchStr: string): AnySchema => {\n    if (searchStr.substring(0, 1) === '?') {\n      searchStr = searchStr.substring(1)\n    }\n\n    const query: Record<string, unknown> = decode(searchStr)\n\n    // Try to parse any query params that might be json\n    for (const key in query) {\n      const value = query[key]\n      if (typeof value === 'string') {\n        try {\n          query[key] = parser(value)\n        } catch (err) {\n          //\n        }\n      }\n    }\n\n    return query\n  }\n}\n\nexport function stringifySearchWith(\n  stringify: (search: any) => string,\n  parser?: (str: string) => any,\n) {\n  function stringifyValue(val: any) {\n    if (typeof val === 'object' && val !== null) {\n      try {\n        return stringify(val)\n      } catch (err) {\n        // silent\n      }\n    } else if (typeof val === 'string' && typeof parser === 'function') {\n      try {\n        // Check if it's a valid parseable string.\n        // If it is, then stringify it again.\n        parser(val)\n        return stringify(val)\n      } catch (err) {\n        // silent\n      }\n    }\n    return val\n  }\n\n  return (search: Record<string, any>) => {\n    search = { ...search }\n\n    Object.keys(search).forEach((key) => {\n      const val = search[key]\n      if (typeof val === 'undefined' || val === undefined) {\n        delete search[key]\n      } else {\n        search[key] = stringifyValue(val)\n      }\n    })\n\n    const searchStr = encode(search as Record<string, string>).toString()\n\n    return searchStr ? `?${searchStr}` : ''\n  }\n}\n\nexport type SearchSerializer = (searchObj: Record<string, any>) => string\nexport type SearchParser = (searchStr: string) => Record<string, any>\n"], "names": ["decode", "encode"], "mappings": ";;;AAGa,MAAA,qBAAqB,gBAAgB,KAAK,KAAK;AACrD,MAAM,yBAAyB;AAAA,EACpC,KAAK;AAAA,EACL,KAAK;AACP;AAEO,SAAS,gBAAgB,QAA8B;AAC5D,SAAO,CAAC,cAAiC;AACvC,QAAI,UAAU,UAAU,GAAG,CAAC,MAAM,KAAK;AACzB,kBAAA,UAAU,UAAU,CAAC;AAAA,IAAA;AAG7B,UAAA,QAAiCA,WAAO,SAAS;AAGvD,eAAW,OAAO,OAAO;AACjB,YAAA,QAAQ,MAAM,GAAG;AACnB,UAAA,OAAO,UAAU,UAAU;AACzB,YAAA;AACI,gBAAA,GAAG,IAAI,OAAO,KAAK;AAAA,iBAClB,KAAK;AAAA,QAAA;AAAA,MAEd;AAAA,IACF;AAGK,WAAA;AAAA,EACT;AACF;AAEgB,SAAA,oBACd,WACA,QACA;AACA,WAAS,eAAe,KAAU;AAChC,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACvC,UAAA;AACF,eAAO,UAAU,GAAG;AAAA,eACb,KAAK;AAAA,MAAA;AAAA,eAGL,OAAO,QAAQ,YAAY,OAAO,WAAW,YAAY;AAC9D,UAAA;AAGF,eAAO,GAAG;AACV,eAAO,UAAU,GAAG;AAAA,eACb,KAAK;AAAA,MAAA;AAAA,IAEd;AAEK,WAAA;AAAA,EAAA;AAGT,SAAO,CAAC,WAAgC;AAC7B,aAAA,EAAE,GAAG,OAAO;AAErB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,YAAA,MAAM,OAAO,GAAG;AACtB,UAAI,OAAO,QAAQ,eAAe,QAAQ,QAAW;AACnD,eAAO,OAAO,GAAG;AAAA,MAAA,OACZ;AACE,eAAA,GAAG,IAAI,eAAe,GAAG;AAAA,MAAA;AAAA,IAClC,CACD;AAED,UAAM,YAAYC,IAAAA,OAAO,MAAgC,EAAE,SAAS;AAE7D,WAAA,YAAY,IAAI,SAAS,KAAK;AAAA,EACvC;AACF;;;;;"}