{"version": 3, "file": "link.js", "sources": ["../../src/link.ts"], "sourcesContent": ["import type { HistoryState, ParsedHistoryState } from '@tanstack/history'\nimport type {\n  AllParams,\n  CatchAllPaths,\n  CurrentPath,\n  FullSearchSchema,\n  FullSearchSchemaInput,\n  ParentPath,\n  RouteByPath,\n  RouteByToPath,\n  RoutePaths,\n  RouteToPath,\n  ToPath,\n} from './routeInfo'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ViewTransitionOptions,\n} from './router'\nimport type {\n  ConstrainLiteral,\n  Expand,\n  MakeDifferenceOptional,\n  NoInfer,\n  NonNullableUpdater,\n  Updater,\n} from './utils'\nimport type { ParsedLocation } from './location'\n\nexport type IsRequiredParams<TParams> =\n  Record<never, never> extends TParams ? never : true\n\nexport type ParsePathParams<T extends string, TAcc = never> = T &\n  `${string}$${string}` extends never\n  ? TAcc\n  : T extends `${string}$${infer TPossiblyParam}`\n    ? TPossiblyParam extends ''\n      ? TAcc\n      : TPossiblyParam & `${string}/${string}` extends never\n        ? TPossiblyParam | TAcc\n        : TPossiblyParam extends `${infer TParam}/${infer TRest}`\n          ? ParsePathParams<TRest, TParam extends '' ? TAcc : TParam | TAcc>\n          : never\n    : TAcc\n\nexport type AddTrailingSlash<T> = T extends `${string}/` ? T : `${T & string}/`\n\nexport type RemoveTrailingSlashes<T> = T & `${string}/` extends never\n  ? T\n  : T extends `${infer R}/`\n    ? R\n    : T\n\nexport type AddLeadingSlash<T> = T & `/${string}` extends never\n  ? `/${T & string}`\n  : T\n\nexport type RemoveLeadingSlashes<T> = T & `/${string}` extends never\n  ? T\n  : T extends `/${infer R}`\n    ? R\n    : T\n\ntype JoinPath<TLeft extends string, TRight extends string> = TRight extends ''\n  ? TLeft\n  : TLeft extends ''\n    ? TRight\n    : `${RemoveTrailingSlashes<TLeft>}/${RemoveLeadingSlashes<TRight>}`\n\ntype RemoveLastSegment<\n  T extends string,\n  TAcc extends string = '',\n> = T extends `${infer TSegment}/${infer TRest}`\n  ? TRest & `${string}/${string}` extends never\n    ? TRest extends ''\n      ? TAcc\n      : `${TAcc}${TSegment}`\n    : RemoveLastSegment<TRest, `${TAcc}${TSegment}/`>\n  : TAcc\n\nexport type ResolveCurrentPath<\n  TFrom extends string,\n  TTo extends string,\n> = TTo extends '.'\n  ? TFrom\n  : TTo extends './'\n    ? AddTrailingSlash<TFrom>\n    : TTo & `./${string}` extends never\n      ? never\n      : TTo extends `./${infer TRest}`\n        ? AddLeadingSlash<JoinPath<TFrom, TRest>>\n        : never\n\nexport type ResolveParentPath<\n  TFrom extends string,\n  TTo extends string,\n> = TTo extends '../' | '..'\n  ? TFrom extends '' | '/'\n    ? never\n    : AddLeadingSlash<RemoveLastSegment<TFrom>>\n  : TTo & `../${string}` extends never\n    ? AddLeadingSlash<JoinPath<TFrom, TTo>>\n    : TFrom extends '' | '/'\n      ? never\n      : TTo extends `../${infer ToRest}`\n        ? ResolveParentPath<RemoveLastSegment<TFrom>, ToRest>\n        : AddLeadingSlash<JoinPath<TFrom, TTo>>\n\nexport type ResolveRelativePath<TFrom, TTo = '.'> = string extends TFrom\n  ? TTo\n  : string extends TTo\n    ? TFrom\n    : undefined extends TTo\n      ? TFrom\n      : TTo extends string\n        ? TFrom extends string\n          ? TTo extends `/${string}`\n            ? TTo\n            : TTo extends `..${string}`\n              ? ResolveParentPath<TFrom, TTo>\n              : TTo extends `.${string}`\n                ? ResolveCurrentPath<TFrom, TTo>\n                : AddLeadingSlash<JoinPath<TFrom, TTo>>\n          : never\n        : never\n\nexport type FindDescendantToPaths<\n  TRouter extends AnyRouter,\n  TPrefix extends string,\n> = `${TPrefix}/${string}` & RouteToPath<TRouter>\n\nexport type InferDescendantToPaths<\n  TRouter extends AnyRouter,\n  TPrefix extends string,\n  TPaths = FindDescendantToPaths<TRouter, TPrefix>,\n> = TPaths extends `${TPrefix}/`\n  ? never\n  : TPaths extends `${TPrefix}/${infer TRest}`\n    ? TRest\n    : never\n\nexport type RelativeToPath<\n  TRouter extends AnyRouter,\n  TTo extends string,\n  TResolvedPath extends string,\n> =\n  | (TResolvedPath & RouteToPath<TRouter> extends never\n      ? never\n      : ToPath<TRouter, TTo>)\n  | `${RemoveTrailingSlashes<TTo>}/${InferDescendantToPaths<TRouter, RemoveTrailingSlashes<TResolvedPath>>}`\n\nexport type RelativeToParentPath<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string,\n  TResolvedPath extends string = ResolveRelativePath<TFrom, TTo>,\n> =\n  | RelativeToPath<TRouter, TTo, TResolvedPath>\n  | (TTo extends `${string}..` | `${string}../`\n      ? TResolvedPath extends '/' | ''\n        ? never\n        : FindDescendantToPaths<\n              TRouter,\n              RemoveTrailingSlashes<TResolvedPath>\n            > extends never\n          ? never\n          : `${RemoveTrailingSlashes<TTo>}/${ParentPath<TRouter>}`\n      : never)\n\nexport type RelativeToCurrentPath<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string,\n  TResolvedPath extends string = ResolveRelativePath<TFrom, TTo>,\n> = RelativeToPath<TRouter, TTo, TResolvedPath> | CurrentPath<TRouter>\n\nexport type AbsoluteToPath<TRouter extends AnyRouter, TFrom extends string> =\n  | (string extends TFrom\n      ? CurrentPath<TRouter>\n      : TFrom extends `/`\n        ? never\n        : CurrentPath<TRouter>)\n  | (string extends TFrom\n      ? ParentPath<TRouter>\n      : TFrom extends `/`\n        ? never\n        : ParentPath<TRouter>)\n  | RouteToPath<TRouter>\n  | (TFrom extends '/'\n      ? never\n      : string extends TFrom\n        ? never\n        : InferDescendantToPaths<TRouter, RemoveTrailingSlashes<TFrom>>)\n\nexport type RelativeToPathAutoComplete<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string,\n> = string extends TTo\n  ? string\n  : string extends TFrom\n    ? AbsoluteToPath<TRouter, TFrom>\n    : TTo & `..${string}` extends never\n      ? TTo & `.${string}` extends never\n        ? AbsoluteToPath<TRouter, TFrom>\n        : RelativeToCurrentPath<TRouter, TFrom, TTo>\n      : RelativeToParentPath<TRouter, TFrom, TTo>\n\nexport type NavigateOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ToOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & NavigateOptionProps\n\n/**\n * The NavigateOptions type is used to describe the options that can be used when describing a navigation action in TanStack Router.\n * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType)\n */\nexport interface NavigateOptionProps {\n  /**\n   * If set to `true`, the router will scroll the element with an id matching the hash into view with default `ScrollIntoViewOptions`.\n   * If set to `false`, the router will not scroll the element with an id matching the hash into view.\n   * If set to `ScrollIntoViewOptions`, the router will scroll the element with an id matching the hash into view with the provided options.\n   * @default true\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#hashscrollintoview)\n   * @see [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView)\n   */\n  hashScrollIntoView?: boolean | ScrollIntoViewOptions\n  /**\n   * `replace` is a boolean that determines whether the navigation should replace the current history entry or push a new one.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#replace)\n   */\n  replace?: boolean\n  /**\n   * Defaults to `true` so that the scroll position will be reset to 0,0 after the location is committed to the browser history.\n   * If `false`, the scroll position will not be reset to 0,0 after the location is committed to history.\n   * @default true\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#resetscroll)\n   */\n  resetScroll?: boolean\n  /** @deprecated All navigations now use startTransition under the hood */\n  startTransition?: boolean\n  /**\n   * If set to `true`, the router will wrap the resulting navigation in a `document.startViewTransition()` call.\n   * If `ViewTransitionOptions`, route navigations will be called using `document.startViewTransition({update, types})`\n   * where `types` will be the strings array passed with `ViewTransitionOptions[\"types\"]`.\n   * If the browser does not support viewTransition types, the navigation will fall back to normal `document.startTransition()`, same as if `true` was passed.\n   *\n   * If the browser does not support this api, this option will be ignored.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#viewtransition)\n   * @see [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Document/startViewTransition)\n   * @see [Google](https://developer.chrome.com/docs/web-platform/view-transitions/same-document#view-transition-types)\n   */\n  viewTransition?: boolean | ViewTransitionOptions\n  /**\n   * If `true`, navigation will ignore any blockers that might prevent it.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#ignoreblocker)\n   */\n  ignoreBlocker?: boolean\n  /**\n   * If `true`, navigation to a route inside of router will trigger a full page load instead of the traditional SPA navigation.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#reloaddocument)\n   */\n  reloadDocument?: boolean\n  /**\n   * This can be used instead of `to` to navigate to a fully built href, e.g. pointing to an external target.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#href)\n   */\n  href?: string\n}\n\nexport type ToOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ToSubOptions<TRouter, TFrom, TTo> & MaskOptions<TRouter, TMaskFrom, TMaskTo>\n\nexport interface MaskOptions<\n  in out TRouter extends AnyRouter,\n  in out TMaskFrom extends string,\n  in out TMaskTo extends string,\n> {\n  _fromLocation?: ParsedLocation\n  mask?: ToMaskOptions<TRouter, TMaskFrom, TMaskTo>\n}\n\nexport type ToMaskOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TMaskFrom extends string = string,\n  TMaskTo extends string = '.',\n> = ToSubOptions<TRouter, TMaskFrom, TMaskTo> & {\n  unmaskOnReload?: boolean\n}\n\nexport type ToSubOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n> = ToSubOptionsProps<TRouter, TFrom, TTo> &\n  SearchParamOptions<TRouter, TFrom, TTo> &\n  PathParamOptions<TRouter, TFrom, TTo>\n\nexport interface RequiredToOptions<\n  in out TRouter extends AnyRouter,\n  in out TFrom extends string,\n  in out TTo extends string | undefined,\n> {\n  to: ToPathOption<TRouter, TFrom, TTo> & {}\n}\n\nexport interface OptionalToOptions<\n  in out TRouter extends AnyRouter,\n  in out TFrom extends string,\n  in out TTo extends string | undefined,\n> {\n  to?: ToPathOption<TRouter, TFrom, TTo> & {}\n}\n\nexport type MakeToRequired<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string | undefined,\n> = string extends TFrom\n  ? string extends TTo\n    ? OptionalToOptions<TRouter, TFrom, TTo>\n    : TTo & CatchAllPaths<TRouter> extends never\n      ? RequiredToOptions<TRouter, TFrom, TTo>\n      : OptionalToOptions<TRouter, TFrom, TTo>\n  : OptionalToOptions<TRouter, TFrom, TTo>\n\nexport type ToSubOptionsProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends RoutePaths<TRouter['routeTree']> | string = string,\n  TTo extends string | undefined = '.',\n> = MakeToRequired<TRouter, TFrom, TTo> & {\n  hash?: true | Updater<string>\n  state?: true | NonNullableUpdater<ParsedHistoryState, HistoryState>\n  from?: FromPathOption<TRouter, TFrom> & {}\n}\n\nexport type ParamsReducerFn<\n  in out TRouter extends AnyRouter,\n  in out TParamVariant extends ParamVariant,\n  in out TFrom,\n  in out TTo,\n> = (\n  current: Expand<ResolveFromParams<TRouter, TParamVariant, TFrom>>,\n) => Expand<ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>>\n\ntype ParamsReducer<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  | Expand<ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>>\n  | (ParamsReducerFn<TRouter, TParamVariant, TFrom, TTo> & {})\n\ntype ParamVariant = 'PATH' | 'SEARCH'\n\nexport type ResolveRoute<\n  TRouter extends AnyRouter,\n  TFrom,\n  TTo,\n  TPath = ResolveRelativePath<TFrom, TTo>,\n> = TPath extends string\n  ? TFrom extends TPath\n    ? RouteByPath<TRouter['routeTree'], TPath>\n    : RouteByToPath<TRouter, TPath>\n  : never\n\ntype ResolveFromParamType<TParamVariant extends ParamVariant> =\n  TParamVariant extends 'PATH' ? 'allParams' : 'fullSearchSchema'\n\ntype ResolveFromAllParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n> = TParamVariant extends 'PATH'\n  ? AllParams<TRouter['routeTree']>\n  : FullSearchSchema<TRouter['routeTree']>\n\ntype ResolveFromParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n> = string extends TFrom\n  ? ResolveFromAllParams<TRouter, TParamVariant>\n  : RouteByPath<\n      TRouter['routeTree'],\n      TFrom\n    >['types'][ResolveFromParamType<TParamVariant>]\n\ntype ResolveToParamType<TParamVariant extends ParamVariant> =\n  TParamVariant extends 'PATH' ? 'allParams' : 'fullSearchSchemaInput'\n\ntype ResolveAllToParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n> = TParamVariant extends 'PATH'\n  ? AllParams<TRouter['routeTree']>\n  : FullSearchSchemaInput<TRouter['routeTree']>\n\nexport type ResolveToParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  ResolveRelativePath<TFrom, TTo> extends infer TPath\n    ? undefined extends TPath\n      ? never\n      : string extends TPath\n        ? ResolveAllToParams<TRouter, TParamVariant>\n        : TPath extends CatchAllPaths<TRouter>\n          ? ResolveAllToParams<TRouter, TParamVariant>\n          : ResolveRoute<\n              TRouter,\n              TFrom,\n              TTo\n            >['types'][ResolveToParamType<TParamVariant>]\n    : never\n\ntype ResolveRelativeToParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n  TToParams = ResolveToParams<TRouter, TParamVariant, TFrom, TTo>,\n> = TParamVariant extends 'SEARCH'\n  ? TToParams\n  : string extends TFrom\n    ? TToParams\n    : MakeDifferenceOptional<\n        ResolveFromParams<TRouter, TParamVariant, TFrom>,\n        TToParams\n      >\n\nexport interface MakeOptionalSearchParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  search?: true | (ParamsReducer<TRouter, 'SEARCH', TFrom, TTo> & {})\n}\n\nexport interface MakeOptionalPathParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  params?: true | (ParamsReducer<TRouter, 'PATH', TFrom, TTo> & {})\n}\n\ntype MakeRequiredParamsReducer<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  | (string extends TFrom\n      ? never\n      : ResolveFromParams<\n            TRouter,\n            TParamVariant,\n            TFrom\n          > extends ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>\n        ? true\n        : never)\n  | (ParamsReducer<TRouter, TParamVariant, TFrom, TTo> & {})\n\nexport interface MakeRequiredPathParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  params: MakeRequiredParamsReducer<TRouter, 'PATH', TFrom, TTo> & {}\n}\n\nexport interface MakeRequiredSearchParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  search: MakeRequiredParamsReducer<TRouter, 'SEARCH', TFrom, TTo> & {}\n}\n\nexport type IsRequired<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  ResolveRelativePath<TFrom, TTo> extends infer TPath\n    ? undefined extends TPath\n      ? never\n      : TPath extends CatchAllPaths<TRouter>\n        ? never\n        : IsRequiredParams<\n            ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>\n          >\n    : never\n\nexport type SearchParamOptions<TRouter extends AnyRouter, TFrom, TTo> =\n  IsRequired<TRouter, 'SEARCH', TFrom, TTo> extends never\n    ? MakeOptionalSearchParams<TRouter, TFrom, TTo>\n    : MakeRequiredSearchParams<TRouter, TFrom, TTo>\n\nexport type PathParamOptions<TRouter extends AnyRouter, TFrom, TTo> =\n  IsRequired<TRouter, 'PATH', TFrom, TTo> extends never\n    ? MakeOptionalPathParams<TRouter, TFrom, TTo>\n    : MakeRequiredPathParams<TRouter, TFrom, TTo>\n\nexport type ToPathOption<\n  TRouter extends AnyRouter = AnyRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = string,\n> = ConstrainLiteral<\n  TTo,\n  RelativeToPathAutoComplete<\n    TRouter,\n    NoInfer<TFrom> extends string ? NoInfer<TFrom> : '',\n    NoInfer<TTo> & string\n  >\n>\n\nexport type FromPathOption<TRouter extends AnyRouter, TFrom> = ConstrainLiteral<\n  TFrom,\n  RoutePaths<TRouter['routeTree']>\n>\n\n/**\n * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/navigation#active-options)\n */\nexport interface ActiveOptions {\n  /**\n   * If true, the link will be active if the current route matches the `to` route path exactly (no children routes)\n   * @default false\n   */\n  exact?: boolean\n  /**\n   * If true, the link will only be active if the current URL hash matches the `hash` prop\n   * @default false\n   */\n  includeHash?: boolean\n  /**\n   * If true, the link will only be active if the current URL search params inclusively match the `search` prop\n   * @default true\n   */\n  includeSearch?: boolean\n  /**\n   * This modifies the `includeSearch` behavior.\n   * If true,  properties in `search` that are explicitly `undefined` must NOT be present in the current URL search params for the link to be active.\n   * @default false\n   */\n  explicitUndefined?: boolean\n}\n\nexport interface LinkOptionsProps {\n  /**\n   * The standard anchor tag target attribute\n   */\n  target?: HTMLAnchorElement['target']\n  /**\n   * Configurable options to determine if the link should be considered active or not\n   * @default {exact:true,includeHash:true}\n   */\n  activeOptions?: ActiveOptions\n  /**\n   * The preloading strategy for this link\n   * - `false` - No preloading\n   * - `'intent'` - Preload the linked route on hover and cache it for this many milliseconds in hopes that the user will eventually navigate there.\n   * - `'viewport'` - Preload the linked route when it enters the viewport\n   */\n  preload?: false | 'intent' | 'viewport' | 'render'\n  /**\n   * When a preload strategy is set, this delays the preload by this many milliseconds.\n   * If the user exits the link before this delay, the preload will be cancelled.\n   */\n  preloadDelay?: number\n  /**\n   * Control whether the link should be disabled or not\n   * If set to `true`, the link will be rendered without an `href` attribute\n   * @default false\n   */\n  disabled?: boolean\n}\n\nexport type LinkOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & LinkOptionsProps\n\nexport type LinkCurrentTargetElement = {\n  preloadTimeout?: null | ReturnType<typeof setTimeout>\n}\n\nexport const preloadWarning = 'Error preloading route! ☝️'\n"], "names": [], "mappings": "AA+lBO,MAAM,iBAAiB;"}