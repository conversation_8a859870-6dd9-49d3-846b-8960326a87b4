{"name": "@tanstack/store", "version": "0.7.1", "description": "Framework agnostic type-safe store w/ reactive framework adapters", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TanStack/store.git", "directory": "packages/store"}, "homepage": "https://tanstack.com/store", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["store", "typescript"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "devDependencies": {"@angular/core": "^19.2.13", "@preact/signals": "^1.3.2", "solid-js": "^1.9.7", "vue": "^3.5.14"}, "scripts": {}}