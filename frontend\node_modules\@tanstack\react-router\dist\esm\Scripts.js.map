{"version": 3, "file": "Scripts.js", "sources": ["../../src/Scripts.tsx"], "sourcesContent": ["import { Asset } from './Asset'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport type { RouterManagedTag } from '@tanstack/router-core'\n\nexport const Scripts = () => {\n  const router = useRouter()\n\n  const assetScripts = useRouterState({\n    select: (state) => {\n      const assetScripts: Array<RouterManagedTag> = []\n      const manifest = router.ssr?.manifest\n\n      if (!manifest) {\n        return []\n      }\n\n      state.matches\n        .map((match) => router.looseRoutesById[match.routeId]!)\n        .forEach((route) =>\n          manifest.routes[route.id]?.assets\n            ?.filter((d) => d.tag === 'script')\n            .forEach((asset) => {\n              assetScripts.push({\n                tag: 'script',\n                attrs: asset.attrs,\n                children: asset.children,\n              } as any)\n            }),\n        )\n\n      return assetScripts\n    },\n    structuralSharing: true as any,\n  })\n\n  const { scripts } = useRouterState({\n    select: (state) => ({\n      scripts: (\n        state.matches\n          .map((match) => match.scripts!)\n          .flat(1)\n          .filter(Boolean) as Array<RouterManagedTag>\n      ).map(({ children, ...script }) => ({\n        tag: 'script',\n        attrs: {\n          ...script,\n          suppressHydrationWarning: true,\n        },\n        children,\n      })),\n    }),\n  })\n\n  const allScripts = [...scripts, ...assetScripts] as Array<RouterManagedTag>\n\n  return (\n    <>\n      {allScripts.map((asset, i) => (\n        <Asset {...asset} key={`tsr-scripts-${asset.tag}-${i}`} />\n      ))}\n    </>\n  )\n}\n"], "names": ["assetScripts", "_a"], "mappings": ";;;;;AAKO,MAAM,UAAU,MAAM;AAC3B,QAAM,SAAS,UAAU;AAEzB,QAAM,eAAe,eAAe;AAAA,IAClC,QAAQ,CAAC,UAAU;;AACjB,YAAMA,gBAAwC,CAAC;AACzC,YAAA,YAAW,YAAO,QAAP,mBAAY;AAE7B,UAAI,CAAC,UAAU;AACb,eAAO,CAAC;AAAA,MAAA;AAGJ,YAAA,QACH,IAAI,CAAC,UAAU,OAAO,gBAAgB,MAAM,OAAO,CAAE,EACrD;AAAA,QAAQ,CAAC,UACR;;AAAA,wBAAAC,MAAA,SAAS,OAAO,MAAM,EAAE,MAAxB,gBAAAA,IAA2B,WAA3B,mBACI,OAAO,CAAC,MAAM,EAAE,QAAQ,UACzB,QAAQ,CAAC,UAAU;AAClBD,0BAAa,KAAK;AAAA,cAChB,KAAK;AAAA,cACL,OAAO,MAAM;AAAA,cACb,UAAU,MAAM;AAAA,YAAA,CACV;AAAA,UACT;AAAA;AAAA,MACL;AAEKA,aAAAA;AAAAA,IACT;AAAA,IACA,mBAAmB;AAAA,EAAA,CACpB;AAEK,QAAA,EAAE,QAAQ,IAAI,eAAe;AAAA,IACjC,QAAQ,CAAC,WAAW;AAAA,MAClB,SACE,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,OAAQ,EAC7B,KAAK,CAAC,EACN,OAAO,OAAO,EACjB,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAAA,QAClC,KAAK;AAAA,QACL,OAAO;AAAA,UACL,GAAG;AAAA,UACH,0BAA0B;AAAA,QAC5B;AAAA,QACA;AAAA,MAAA,EACA;AAAA,IACJ;AAAA,EAAA,CACD;AAED,QAAM,aAAa,CAAC,GAAG,SAAS,GAAG,YAAY;AAE/C,yCAEK,UAAW,WAAA,IAAI,CAAC,OAAO,oCACrB,OAAO,EAAA,GAAG,OAAO,KAAK,eAAe,MAAM,GAAG,IAAI,CAAC,IAAI,CACzD,GACH;AAEJ;"}