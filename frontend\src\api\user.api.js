import axiosInstance from "./axiosinstance.js";

export const loginUser = async (password,email) => {
  try {
    const { data } = await axiosInstance.post("/api/auth/login", { email,password });
    return data;
  } catch (error) {
    // Error is already handled by axios interceptor
    // Just re-throw it so the component can handle it
    throw error;
  }
};
export const registerUser = async(name,password,email) =>{
  const {data} = await axiosInstance.post("/api/auht/register",{name,password,email});
  return data;
}
export const logoutUser = async () =>{
  const {data} = await axiosInstance.get("/api/auth/logout");
  return data;
}
