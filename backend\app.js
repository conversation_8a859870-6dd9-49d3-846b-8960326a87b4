import dotenv from "dotenv";
dotenv.config();

// Import environment configuration
import config from "./src/config/env.config.js";

// Debug environment variables
console.log("🔧 Environment Configuration:");
console.log("NODE_ENV:", config.NODE_ENV);
console.log("APP_URL:", config.APP_URL);
console.log("PORT:", config.PORT);
console.log("MONGO_URI:", config.MONGO_URI ? "✅ Set" : "❌ Missing");
console.log("JWT_SECRET:", config.JWT_SECRET ? "✅ Set" : "❌ Missing");

import express from "express";
import cors from "cors";
const app = express();

import short_url from "./src/routes/short_url.route.js";
import authRoute from "./src/routes/auth.routes.js";
import { redirectFromShortUrl } from "./src/controller/short_url.controller.js";

import connectDB from "./src/config/mongo.config.js";
import { errorHandler } from "./src/utils/errorHandler.js";
import { attachuser } from "./src/utils/attachUser.js";
import cookieParser from "cookie-parser";

// CORS configuration
app.use(
  cors({
    origin: [
      config.FRONTEND_URL,
      "http://localhost:3000", // Additional development URL if needed
    ],
    credentials: true,
  })
);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(attachuser);
// Health check route (specific route first)
app.get("/", (req, res) => {
  res.send("URL Shortener Backend is Live ✅");
});

// API routes
app.use("/api/auth", authRoute);
app.use("/api/create", short_url);
app.use("/api", short_url); // This handles /api/urls routes

// Short URL redirect route (catch-all, must be last)
app.get("/:id", redirectFromShortUrl);

app.use(errorHandler);

const PORT = config.PORT;

// Connect to database first
connectDB();

// Then start the server
app.listen(PORT, () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 Base URL: ${process.env.APP_URL || `http://localhost:${PORT}`}`);
});
