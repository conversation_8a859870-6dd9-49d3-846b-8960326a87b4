{"name": "@tanstack/react-store", "version": "0.7.1", "description": "Framework agnostic type-safe store w/ reactive framework adapters", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/store.git", "directory": "packages/react-store"}, "homepage": "https://tanstack.com/store", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["store", "react", "typescript"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "dependencies": {"use-sync-external-store": "^1.5.0", "@tanstack/store": "0.7.1"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/use-sync-external-store": "^0.0.6", "@vitejs/plugin-react": "^4.5.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "scripts": {}}