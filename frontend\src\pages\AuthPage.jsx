import React, { useState } from "react";
import LoginForm from "../components/LoginForm";
import RegisterForm from "../components/RegisterForm";

const AuthPage = ({ onLoginSuccess, onRegisterSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);

  const handleLoginSuccess = (userData) => {
    console.log("Login successful:", userData);
    if (onLoginSuccess) {
      onLoginSuccess(userData);
    }
  };

  const handleRegisterSuccess = (userData) => {
    console.log("Registration successful:", userData);
    if (onRegisterSuccess) {
      onRegisterSuccess(userData);
    }
  };

  const switchToRegister = () => {
    setIsLogin(false);
  };

  const switchToLogin = () => {
    setIsLogin(true);
  };

  return (
    <div>
      {isLogin ? (
        <LoginForm
          onSuccess={handleLoginSuccess}
          onSwitchToRegister={switchToRegister}
        />
      ) : (
        <RegisterForm
          onSuccess={handleRegisterSuccess}
          onSwitchToLogin={switchToLogin}
        />
      )}
    </div>
  );
};

export default AuthPage;
