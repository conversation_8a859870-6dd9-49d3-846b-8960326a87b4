{"version": 3, "file": "awaited.cjs", "sources": ["../../src/awaited.tsx"], "sourcesContent": ["import * as React from 'react'\n\nimport { TSR_DEFERRED_PROMISE, defer } from '@tanstack/router-core'\nimport type { DeferredPromise } from '@tanstack/router-core'\n\nexport type AwaitOptions<T> = {\n  promise: Promise<T>\n}\n\nexport function useAwaited<T>({\n  promise: _promise,\n}: AwaitOptions<T>): [T, DeferredPromise<T>] {\n  const promise = defer(_promise)\n\n  if (promise[TSR_DEFERRED_PROMISE].status === 'pending') {\n    throw promise\n  }\n\n  if (promise[TSR_DEFERRED_PROMISE].status === 'error') {\n    throw promise[TSR_DEFERRED_PROMISE].error\n  }\n\n  return [promise[TSR_DEFERRED_PROMISE].data, promise]\n}\n\nexport function Await<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n) {\n  const inner = <AwaitInner {...props} />\n  if (props.fallback) {\n    return <React.Suspense fallback={props.fallback}>{inner}</React.Suspense>\n  }\n  return inner\n}\n\nfunction AwaitInner<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n): React.JSX.Element {\n  const [data] = useAwaited(props)\n\n  return props.children(data) as React.JSX.Element\n}\n"], "names": ["defer", "TSR_DEFERRED_PROMISE", "jsx", "React"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AASO,SAAS,WAAc;AAAA,EAC5B,SAAS;AACX,GAA6C;AACrC,QAAA,UAAUA,iBAAM,QAAQ;AAE9B,MAAI,QAAQC,WAAAA,oBAAoB,EAAE,WAAW,WAAW;AAChD,UAAA;AAAA,EAAA;AAGR,MAAI,QAAQA,WAAAA,oBAAoB,EAAE,WAAW,SAAS;AAC9C,UAAA,QAAQA,WAAoB,oBAAA,EAAE;AAAA,EAAA;AAGtC,SAAO,CAAC,QAAQA,WAAAA,oBAAoB,EAAE,MAAM,OAAO;AACrD;AAEO,SAAS,MACd,OAIA;AACA,QAAM,QAAQC,2BAAA,IAAC,YAAY,EAAA,GAAG,MAAO,CAAA;AACrC,MAAI,MAAM,UAAU;AAClB,0CAAQC,iBAAM,UAAN,EAAe,UAAU,MAAM,UAAW,UAAM,OAAA;AAAA,EAAA;AAEnD,SAAA;AACT;AAEA,SAAS,WACP,OAImB;AACnB,QAAM,CAAC,IAAI,IAAI,WAAW,KAAK;AAExB,SAAA,MAAM,SAAS,IAAI;AAC5B;;;"}