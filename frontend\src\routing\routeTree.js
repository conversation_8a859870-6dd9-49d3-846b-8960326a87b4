import { createRootRoute, createRoute } from "@tanstack/react-router";
import RootLayout from "../RootLayout";
import HomePage from "../pages/HomePage";
import AuthPage from "../pages/AuthPage";
import DashboardPage from "../pages/DashboardPage";

// Create the root route
export const rootRoute = createRootRoute({
  component: RootLayout,
});

// Create individual routes
export const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: HomePage,
});

export const authRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/auth",
  component: AuthPage,
});

export const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/dashboard",
  component: DashboardPage,
});

// Create the route tree
export const routeTree = rootRoute.addChildren([
  homeRoute,
  authRoute,
  dashboardRoute,
]);
