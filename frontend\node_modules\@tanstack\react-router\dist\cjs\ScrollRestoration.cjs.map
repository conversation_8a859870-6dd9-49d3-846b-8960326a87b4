{"version": 3, "file": "ScrollRestoration.cjs", "sources": ["../../src/ScrollRestoration.tsx"], "sourcesContent": ["import {\n  defaultGetScrollRestorationKey,\n  getCssSelector,\n  scrollRestorationCache,\n  setupScrollRestoration,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport type {\n  ParsedLocation,\n  ScrollRestorationEntry,\n  ScrollRestorationOptions,\n} from '@tanstack/router-core'\n\nfunction useScrollRestoration() {\n  const router = useRouter()\n  setupScrollRestoration(router, true)\n}\n\n/**\n * @deprecated use createRouter's `scrollRestoration` option instead\n */\nexport function ScrollRestoration(_props: ScrollRestorationOptions) {\n  useScrollRestoration()\n\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      \"The ScrollRestoration component is deprecated. Use createRouter's `scrollRestoration` option instead.\",\n    )\n  }\n\n  return null\n}\n\nexport function useElementScrollRestoration(\n  options: (\n    | {\n        id: string\n        getElement?: () => Window | Element | undefined | null\n      }\n    | {\n        id?: string\n        getElement: () => Window | Element | undefined | null\n      }\n  ) & {\n    getKey?: (location: ParsedLocation) => string\n  },\n): ScrollRestorationEntry | undefined {\n  useScrollRestoration()\n\n  const router = useRouter()\n  const getKey = options.getKey || defaultGetScrollRestorationKey\n\n  let elementSelector = ''\n\n  if (options.id) {\n    elementSelector = `[data-scroll-restoration-id=\"${options.id}\"]`\n  } else {\n    const element = options.getElement?.()\n    if (!element) {\n      return\n    }\n    elementSelector =\n      element instanceof Window ? 'window' : getCssSelector(element)\n  }\n\n  const restoreKey = getKey(router.latestLocation)\n  const byKey = scrollRestorationCache?.state[restoreKey]\n  return byKey?.[elementSelector]\n}\n"], "names": ["useRouter", "setupScrollRestoration", "defaultGetScrollRestorationKey", "getCssSelector", "scrollRestorationCache"], "mappings": ";;;;AAaA,SAAS,uBAAuB;AAC9B,QAAM,SAASA,UAAAA,UAAU;AACzBC,aAAA,uBAAuB,QAAQ,IAAI;AACrC;AAKO,SAAS,kBAAkB,QAAkC;AAC7C,uBAAA;AAEjB,MAAA,QAAQ,IAAI,aAAa,eAAe;AAClC,YAAA;AAAA,MACN;AAAA,IACF;AAAA,EAAA;AAGK,SAAA;AACT;AAEO,SAAS,4BACd,SAYoC;;AACf,uBAAA;AAErB,QAAM,SAASD,UAAAA,UAAU;AACnB,QAAA,SAAS,QAAQ,UAAUE,WAAA;AAEjC,MAAI,kBAAkB;AAEtB,MAAI,QAAQ,IAAI;AACI,sBAAA,gCAAgC,QAAQ,EAAE;AAAA,EAAA,OACvD;AACC,UAAA,WAAU,aAAQ,eAAR;AAChB,QAAI,CAAC,SAAS;AACZ;AAAA,IAAA;AAEF,sBACE,mBAAmB,SAAS,WAAWC,WAAAA,eAAe,OAAO;AAAA,EAAA;AAG3D,QAAA,aAAa,OAAO,OAAO,cAAc;AACzC,QAAA,SAAQC,gBAAAA,2BAAAA,mBAAwB,MAAM;AAC5C,SAAO,+BAAQ;AACjB;;;"}