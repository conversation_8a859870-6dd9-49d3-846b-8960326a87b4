{"version": 3, "file": "effect.cjs", "sources": ["../../src/effect.ts"], "sourcesContent": ["import { Derived } from './derived'\nimport type { DerivedOptions } from './derived'\n\ninterface EffectOptions\n  extends Omit<\n    DerivedOptions<unknown>,\n    'onUpdate' | 'onSubscribe' | 'lazy' | 'fn'\n  > {\n  /**\n   * Should the effect trigger immediately?\n   * @default false\n   */\n  eager?: boolean\n  fn: () => void\n}\n\nexport class Effect {\n  /**\n   * @private\n   */\n  _derived: Derived<void>\n\n  constructor(opts: EffectOptions) {\n    const { eager, fn, ...derivedProps } = opts\n\n    this._derived = new Derived({\n      ...derivedProps,\n      fn: () => {},\n      onUpdate() {\n        fn()\n      },\n    })\n\n    if (eager) {\n      fn()\n    }\n  }\n\n  mount() {\n    return this._derived.mount()\n  }\n}\n"], "names": ["Derived"], "mappings": ";;;AAgBO,MAAM,OAAO;AAAA,EAMlB,YAAY,MAAqB;AAC/B,UAAM,EAAE,OAAO,IAAI,GAAG,aAAiB,IAAA;AAElC,SAAA,WAAW,IAAIA,gBAAQ;AAAA,MAC1B,GAAG;AAAA,MACH,IAAI,MAAM;AAAA,MAAC;AAAA,MACX,WAAW;AACN,WAAA;AAAA,MAAA;AAAA,IACL,CACD;AAED,QAAI,OAAO;AACN,SAAA;AAAA,IAAA;AAAA,EACL;AAAA,EAGF,QAAQ;AACC,WAAA,KAAK,SAAS,MAAM;AAAA,EAAA;AAE/B;;"}