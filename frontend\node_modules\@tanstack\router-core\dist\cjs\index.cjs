"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const defer = require("./defer.cjs");
const link = require("./link.cjs");
const Matches = require("./Matches.cjs");
const path = require("./path.cjs");
const qss = require("./qss.cjs");
const root = require("./root.cjs");
const route = require("./route.cjs");
const router = require("./router.cjs");
const searchMiddleware = require("./searchMiddleware.cjs");
const searchParams = require("./searchParams.cjs");
const utils = require("./utils.cjs");
const redirect = require("./redirect.cjs");
const notFound = require("./not-found.cjs");
const scrollRestoration = require("./scroll-restoration.cjs");
exports.TSR_DEFERRED_PROMISE = defer.TSR_DEFERRED_PROMISE;
exports.defer = defer.defer;
exports.preloadWarning = link.preloadWarning;
exports.isMatch = Matches.isMatch;
exports.cleanPath = path.cleanPath;
exports.exactPathTest = path.exactPathTest;
exports.interpolatePath = path.interpolatePath;
exports.joinPaths = path.joinPaths;
exports.matchByPath = path.matchByPath;
exports.matchPathname = path.matchPathname;
exports.parsePathname = path.parsePathname;
exports.removeBasepath = path.removeBasepath;
exports.removeTrailingSlash = path.removeTrailingSlash;
exports.resolvePath = path.resolvePath;
exports.trimPath = path.trimPath;
exports.trimPathLeft = path.trimPathLeft;
exports.trimPathRight = path.trimPathRight;
exports.decode = qss.decode;
exports.encode = qss.encode;
exports.rootRouteId = root.rootRouteId;
exports.BaseRootRoute = route.BaseRootRoute;
exports.BaseRoute = route.BaseRoute;
exports.BaseRouteApi = route.BaseRouteApi;
exports.PathParamError = router.PathParamError;
exports.RouterCore = router.RouterCore;
exports.SearchParamError = router.SearchParamError;
exports.componentTypes = router.componentTypes;
exports.defaultSerializeError = router.defaultSerializeError;
exports.getInitialRouterState = router.getInitialRouterState;
exports.getLocationChangeInfo = router.getLocationChangeInfo;
exports.lazyFn = router.lazyFn;
exports.retainSearchParams = searchMiddleware.retainSearchParams;
exports.stripSearchParams = searchMiddleware.stripSearchParams;
exports.defaultParseSearch = searchParams.defaultParseSearch;
exports.defaultStringifySearch = searchParams.defaultStringifySearch;
exports.parseSearchWith = searchParams.parseSearchWith;
exports.stringifySearchWith = searchParams.stringifySearchWith;
exports.createControlledPromise = utils.createControlledPromise;
exports.deepEqual = utils.deepEqual;
exports.escapeJSON = utils.escapeJSON;
exports.functionalUpdate = utils.functionalUpdate;
exports.isPlainArray = utils.isPlainArray;
exports.isPlainObject = utils.isPlainObject;
exports.last = utils.last;
exports.pick = utils.pick;
exports.replaceEqualDeep = utils.replaceEqualDeep;
exports.shallow = utils.shallow;
exports.isRedirect = redirect.isRedirect;
exports.isResolvedRedirect = redirect.isResolvedRedirect;
exports.redirect = redirect.redirect;
exports.isNotFound = notFound.isNotFound;
exports.notFound = notFound.notFound;
exports.defaultGetScrollRestorationKey = scrollRestoration.defaultGetScrollRestorationKey;
exports.getCssSelector = scrollRestoration.getCssSelector;
exports.handleHashScroll = scrollRestoration.handleHashScroll;
exports.restoreScroll = scrollRestoration.restoreScroll;
exports.scrollRestorationCache = scrollRestoration.scrollRestorationCache;
exports.setupScrollRestoration = scrollRestoration.setupScrollRestoration;
exports.storageKey = scrollRestoration.storageKey;
//# sourceMappingURL=index.cjs.map
