{"version": 3, "file": "Matches.js", "sources": ["../../src/Matches.tsx"], "sourcesContent": ["import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { CatchBoundary, ErrorComponent } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { Transitioner } from './Transitioner'\nimport { matchContext } from './matchContext'\nimport { Match } from './Match'\nimport { SafeFragment } from './SafeFragment'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type { ReactNode } from './route'\nimport type {\n  AnyRouter,\n  DeepPartial,\n  MakeOptionalPathParams,\n  MakeOptionalSearchParams,\n  MakeRouteMatchUnion,\n  MaskOptions,\n  MatchRouteOptions,\n  NoInfer,\n  RegisteredRouter,\n  ResolveRelativePath,\n  ResolveRoute,\n  RouteByPath,\n  RouterState,\n  ToSubOptionsProps,\n} from '@tanstack/router-core'\n\ndeclare module '@tanstack/router-core' {\n  export interface RouteMatchExtensions {\n    meta?: Array<React.JSX.IntrinsicElements['meta'] | undefined>\n    links?: Array<React.JSX.IntrinsicElements['link'] | undefined>\n    scripts?: Array<React.JSX.IntrinsicElements['script'] | undefined>\n    headScripts?: Array<React.JSX.IntrinsicElements['script'] | undefined>\n  }\n}\n\nexport function Matches() {\n  const router = useRouter()\n\n  const pendingElement = router.options.defaultPendingComponent ? (\n    <router.options.defaultPendingComponent />\n  ) : null\n\n  // Do not render a root Suspense during SSR or hydrating from SSR\n  const ResolvedSuspense =\n    router.isServer || (typeof document !== 'undefined' && router.clientSsr)\n      ? SafeFragment\n      : React.Suspense\n\n  const inner = (\n    <ResolvedSuspense fallback={pendingElement}>\n      <Transitioner />\n      <MatchesInner />\n    </ResolvedSuspense>\n  )\n\n  return router.options.InnerWrap ? (\n    <router.options.InnerWrap>{inner}</router.options.InnerWrap>\n  ) : (\n    inner\n  )\n}\n\nfunction MatchesInner() {\n  const matchId = useRouterState({\n    select: (s) => {\n      return s.matches[0]?.id\n    },\n  })\n\n  const resetKey = useRouterState({\n    select: (s) => s.loadedAt,\n  })\n\n  return (\n    <matchContext.Provider value={matchId}>\n      <CatchBoundary\n        getResetKey={() => resetKey}\n        errorComponent={ErrorComponent}\n        onCatch={(error) => {\n          warning(\n            false,\n            `The following error wasn't caught by any route! At the very least, consider setting an 'errorComponent' in your RootRoute!`,\n          )\n          warning(false, error.message || error.toString())\n        }}\n      >\n        {matchId ? <Match matchId={matchId} /> : null}\n      </CatchBoundary>\n    </matchContext.Provider>\n  )\n}\n\nexport type UseMatchRouteOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = ToSubOptionsProps<TRouter, TFrom, TTo> &\n  DeepPartial<MakeOptionalSearchParams<TRouter, TFrom, TTo>> &\n  DeepPartial<MakeOptionalPathParams<TRouter, TFrom, TTo>> &\n  MaskOptions<TRouter, TMaskFrom, TMaskTo> &\n  MatchRouteOptions\n\nexport function useMatchRoute<TRouter extends AnyRouter = RegisteredRouter>() {\n  const router = useRouter()\n\n  useRouterState({\n    select: (s) => [s.location.href, s.resolvedLocation?.href, s.status],\n    structuralSharing: true as any,\n  })\n\n  return React.useCallback(\n    <\n      const TFrom extends string = string,\n      const TTo extends string | undefined = undefined,\n      const TMaskFrom extends string = TFrom,\n      const TMaskTo extends string = '',\n    >(\n      opts: UseMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n    ): false | ResolveRoute<TRouter, TFrom, TTo>['types']['allParams'] => {\n      const { pending, caseSensitive, fuzzy, includeSearch, ...rest } = opts\n\n      return router.matchRoute(rest as any, {\n        pending,\n        caseSensitive,\n        fuzzy,\n        includeSearch,\n      })\n    },\n    [router],\n  )\n}\n\nexport type MakeMatchRouteOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = UseMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | ((\n        params?: RouteByPath<\n          TRouter['routeTree'],\n          ResolveRelativePath<TFrom, NoInfer<TTo>>\n        >['types']['allParams'],\n      ) => ReactNode)\n    | React.ReactNode\n}\n\nexport function MatchRoute<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(props: MakeMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): any {\n  const matchRoute = useMatchRoute()\n  const params = matchRoute(props as any) as boolean\n\n  if (typeof props.children === 'function') {\n    return (props.children as any)(params)\n  }\n\n  return params ? props.children : null\n}\n\nexport interface UseMatchesBaseOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    matches: Array<MakeRouteMatchUnion<TRouter>>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseMatchesResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected ? Array<MakeRouteMatchUnion<TRouter>> : TSelected\n\nexport function useMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  return useRouterState({\n    select: (state: RouterState<TRouter['routeTree']>) => {\n      const matches = state.matches\n      return opts?.select\n        ? opts.select(matches as Array<MakeRouteMatchUnion<TRouter>>)\n        : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any) as UseMatchesResult<TRouter, TSelected>\n}\n\nexport function useParentMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches: Array<MakeRouteMatchUnion<TRouter>>) => {\n      matches = matches.slice(\n        0,\n        matches.findIndex((d) => d.id === contextMatchId),\n      )\n      return opts?.select ? opts.select(matches) : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any)\n}\n\nexport function useChildMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches: Array<MakeRouteMatchUnion<TRouter>>) => {\n      matches = matches.slice(\n        matches.findIndex((d) => d.id === contextMatchId) + 1,\n      )\n      return opts?.select ? opts.select(matches) : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAwCO,SAAS,UAAU;AACxB,QAAM,SAAS,UAAU;AAEnB,QAAA,iBAAiB,OAAO,QAAQ,8CACnC,OAAO,QAAQ,yBAAf,CAAuC,CAAA,IACtC;AAGE,QAAA,mBACJ,OAAO,YAAa,OAAO,aAAa,eAAe,OAAO,YAC1D,eACA,MAAM;AAEZ,QAAM,QACJ,qBAAC,kBAAiB,EAAA,UAAU,gBAC1B,UAAA;AAAA,IAAA,oBAAC,cAAa,EAAA;AAAA,wBACb,cAAa,CAAA,CAAA;AAAA,EAAA,GAChB;AAGK,SAAA,OAAO,QAAQ,YACpB,oBAAC,OAAO,QAAQ,WAAf,EAA0B,UAAA,MAAA,CAAM,IAEjC;AAEJ;AAEA,SAAS,eAAe;AACtB,QAAM,UAAU,eAAe;AAAA,IAC7B,QAAQ,CAAC,MAAM;;AACN,cAAA,OAAE,QAAQ,CAAC,MAAX,mBAAc;AAAA,IAAA;AAAA,EACvB,CACD;AAED,QAAM,WAAW,eAAe;AAAA,IAC9B,QAAQ,CAAC,MAAM,EAAE;AAAA,EAAA,CAClB;AAED,SACG,oBAAA,aAAa,UAAb,EAAsB,OAAO,SAC5B,UAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,aAAa,MAAM;AAAA,MACnB,gBAAgB;AAAA,MAChB,SAAS,CAAC,UAAU;AAClB;AAAA,UACE;AAAA,UACA;AAAA,QACF;AACA,gBAAQ,OAAO,MAAM,WAAW,MAAM,UAAU;AAAA,MAClD;AAAA,MAEC,UAAU,UAAA,oBAAC,OAAM,EAAA,QAAkB,CAAA,IAAK;AAAA,IAAA;AAAA,EAAA,GAE7C;AAEJ;AAcO,SAAS,gBAA8D;AAC5E,QAAM,SAAS,UAAU;AAEV,iBAAA;AAAA,IACb,QAAQ,CAAC;;AAAM,cAAC,EAAE,SAAS,OAAM,OAAE,qBAAF,mBAAoB,MAAM,EAAE,MAAM;AAAA;AAAA,IACnE,mBAAmB;AAAA,EAAA,CACpB;AAED,SAAO,MAAM;AAAA,IACX,CAME,SACoE;AACpE,YAAM,EAAE,SAAS,eAAe,OAAO,eAAe,GAAG,SAAS;AAE3D,aAAA,OAAO,WAAW,MAAa;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACD;AAAA,IACH;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AACF;AAoBO,SAAS,WAMd,OAA4E;AAC5E,QAAM,aAAa,cAAc;AAC3B,QAAA,SAAS,WAAW,KAAY;AAElC,MAAA,OAAO,MAAM,aAAa,YAAY;AAChC,WAAA,MAAM,SAAiB,MAAM;AAAA,EAAA;AAGhC,SAAA,SAAS,MAAM,WAAW;AACnC;AAiBO,SAAS,WAKd,MAEsC;AACtC,SAAO,eAAe;AAAA,IACpB,QAAQ,CAAC,UAA6C;AACpD,YAAM,UAAU,MAAM;AACtB,cAAO,6BAAM,UACT,KAAK,OAAO,OAA8C,IAC1D;AAAA,IACN;AAAA,IACA,mBAAmB,6BAAM;AAAA,EAAA,CACnB;AACV;AAEO,SAAS,iBAKd,MAEsC;AAChC,QAAA,iBAAiB,MAAM,WAAW,YAAY;AAEpD,SAAO,WAAW;AAAA,IAChB,QAAQ,CAAC,YAAiD;AACxD,gBAAU,QAAQ;AAAA,QAChB;AAAA,QACA,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc;AAAA,MAClD;AACA,cAAO,6BAAM,UAAS,KAAK,OAAO,OAAO,IAAI;AAAA,IAC/C;AAAA,IACA,mBAAmB,6BAAM;AAAA,EAAA,CACnB;AACV;AAEO,SAAS,gBAKd,MAEsC;AAChC,QAAA,iBAAiB,MAAM,WAAW,YAAY;AAEpD,SAAO,WAAW;AAAA,IAChB,QAAQ,CAAC,YAAiD;AACxD,gBAAU,QAAQ;AAAA,QAChB,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc,IAAI;AAAA,MACtD;AACA,cAAO,6BAAM,UAAS,KAAK,OAAO,OAAO,IAAI;AAAA,IAC/C;AAAA,IACA,mBAAmB,6BAAM;AAAA,EAAA,CACnB;AACV;"}