export { default as invariant } from 'tiny-invariant';
export { default as warning } from 'tiny-warning';
export { defer, TSR_DEFERRED_PROMISE, isMatch, joinPaths, cleanPath, trimPathLeft, trimPathRight, trimPath, resolvePath, parsePathname, interpolatePath, matchPathname, removeBasepath, matchByPath, encode, decode, rootRouteId, defaultSerializeError, defaultParseSearch, defaultStringifySearch, parseSearchWith, stringifySearchWith, escapeJSON, // SSR
pick, functionalUpdate, replaceEqualDeep, isPlainObject, isPlainArray, deepEqual, shallow, createControlledPromise, retainSearchParams, stripSearchParams, } from '@tanstack/router-core';
export type { AnyRoute, StartSerializer, Serializable, SerializerParse, SerializerParseBy, SerializerStringify, SerializerStringifyBy, DeferredPromiseState, DeferredPromise, ParsedLocation, ParsePathParams, RemoveTrailingSlashes, RemoveLeadingSlashes, ActiveOptions, Segment, ResolveRelativePath, RootRouteId, AnyPathParams, ResolveParams, SearchSchemaInput, AnyContext, RouteContext, PreloadableObj, RoutePathOptions, StaticDataRouteOption, RoutePathOptionsIntersection, UpdatableStaticRouteOption, MetaDescriptor, RouteLinkEntry, ParseParamsFn, SearchFilter, ResolveId, InferFullSearchSchema, InferFullSearchSchemaInput, ErrorRouteProps, ErrorComponentProps, NotFoundRouteProps, TrimPath, TrimPathLeft, TrimPathRight, ParseSplatParams, SplatParams, StringifyParamsFn, ParamsOptions, InferAllParams, InferAllContext, LooseReturnType, LooseAsyncReturnType, ContextReturnType, ContextAsyncReturnType, ResolveLoaderData, ResolveRouteContext, SearchSerializer, SearchParser, TrailingSlashOption, ExtractedEntry, ExtractedStream, ExtractedPromise, StreamState, Manifest, RouterManagedTag, ControlledPromise, Constrain, Expand, MergeAll, Assign, IntersectAssign, ResolveValidatorInput, ResolveValidatorOutput, Register, AnyValidator, DefaultValidator, ValidatorFn, AnySchema, AnyValidatorAdapter, AnyValidatorFn, AnyValidatorObj, ResolveValidatorInputFn, ResolveValidatorOutputFn, ResolveSearchValidatorInput, ResolveSearchValidatorInputFn, Validator, ValidatorAdapter, ValidatorObj, FileRoutesByPath, RouteById, RootRouteOptions, SerializerExtensions, } from '@tanstack/router-core';
export type * from './serializer.js';
export { createHistory, createBrowserHistory, createHashHistory, createMemoryHistory, } from '@tanstack/history';
export type { BlockerFn, HistoryLocation, RouterHistory, ParsedPath, HistoryState, } from '@tanstack/history';
export { useAwaited, Await } from './awaited.js';
export type { AwaitOptions } from './awaited.js';
export { CatchBoundary, ErrorComponent } from './CatchBoundary.js';
export { ClientOnly } from './ClientOnly.js';
export { FileRoute, createFileRoute, FileRouteLoader, LazyRoute, createLazyRoute, createLazyFileRoute, } from './fileRoute.js';
export * from './history.js';
export { lazyRouteComponent } from './lazyRouteComponent.js';
export { useLinkProps, createLink, Link, linkOptions } from './link.js';
export type { InferDescendantToPaths, RelativeToPath, RelativeToParentPath, RelativeToCurrentPath, AbsoluteToPath, RelativeToPathAutoComplete, NavigateOptions, ToOptions, ToMaskOptions, ToSubOptions, ResolveRoute, SearchParamOptions, PathParamOptions, ToPathOption, LinkOptions, MakeOptionalPathParams, FileRouteTypes, RouteContextParameter, BeforeLoadContextParameter, ResolveAllContext, ResolveAllParamsFromParent, ResolveFullSearchSchema, ResolveFullSearchSchemaInput, RouteIds, NavigateFn, BuildLocationFn, FullSearchSchemaOption, MakeRemountDepsOptionsUnion, RemountDepsOptions, ResolveFullPath, AnyRouteWithContext, AnyRouterWithContext, CommitLocationOptions, MatchLocation, UseNavigateResult, AnyRedirect, Redirect, ResolvedRedirect, MakeRouteMatch, MakeRouteMatchUnion, RouteMatch, AnyRouteMatch, RouteContextFn, RouteContextOptions, BeforeLoadFn, BeforeLoadContextOptions, ContextOptions, RouteOptions, FileBaseRouteOptions, BaseRouteOptions, UpdatableRouteOptions, RouteLoaderFn, LoaderFnContext, LazyRouteOptions, AnyRouter, RegisteredRouter, RouterContextOptions, ControllablePromise, InjectedHtmlEntry, RouterOptions, RouterErrorSerializer, RouterState, ListenerFn, BuildNextOptions, RouterConstructorOptions, RouterEvents, RouterEvent, RouterListener, RouteConstraints, RouteMask, MatchRouteOptions, } from '@tanstack/router-core';
export type { UseLinkPropsOptions, ActiveLinkOptions, LinkProps, LinkComponent, LinkComponentProps, CreateLinkProps, } from './link.js';
export { Matches, useMatchRoute, MatchRoute, useMatches, useParentMatches, useChildMatches, } from './Matches.js';
export type { UseMatchRouteOptions, MakeMatchRouteOptions } from './Matches.js';
export { matchContext } from './matchContext.js';
export { Match, Outlet } from './Match.js';
export { useMatch } from './useMatch.js';
export { useLoaderDeps } from './useLoaderDeps.js';
export { useLoaderData } from './useLoaderData.js';
export { redirect, isRedirect } from '@tanstack/router-core';
export { RouteApi, getRouteApi, Route, createRoute, RootRoute, rootRouteWithContext, createRootRoute, createRootRouteWithContext, createRouteMask, NotFoundRoute, } from './route.js';
export type { AnyRootRoute, ReactNode, SyncRouteComponent, AsyncRouteComponent, RouteComponent, ErrorRouteComponent, NotFoundRouteComponent, } from './route.js';
export { createRouter, Router } from './router.js';
export { componentTypes, lazyFn, SearchParamError, PathParamError, getInitialRouterState, } from '@tanstack/router-core';
export { RouterProvider, RouterContextProvider } from './RouterProvider.js';
export type { RouterProps } from './RouterProvider.js';
export { useElementScrollRestoration, ScrollRestoration, } from './ScrollRestoration.js';
export type { UseBlockerOpts, ShouldBlockFn } from './useBlocker.js';
export { useBlocker, Block } from './useBlocker.js';
export { useNavigate, Navigate } from './useNavigate.js';
export { useParams } from './useParams.js';
export { useSearch } from './useSearch.js';
export { getRouterContext, } from './routerContext.js';
export { useRouteContext } from './useRouteContext.js';
export { useRouter } from './useRouter.js';
export { useRouterState } from './useRouterState.js';
export { useLocation } from './useLocation.js';
export { useCanGoBack } from './useCanGoBack.js';
export { useLayoutEffect, // SSR
useStableCallback, } from './utils.js';
export { CatchNotFound, DefaultGlobalNotFound } from './not-found.js';
export { notFound, isNotFound } from '@tanstack/router-core';
export type { NotFoundError } from '@tanstack/router-core';
export type { ValidateLinkOptions, InferStructuralSharing, ValidateUseSearchOptions, ValidateUseParamsOptions, ValidateLinkOptionsArray, } from './typePrimitives.js';
export type { ValidateFromPath, ValidateToPath, ValidateSearch, ValidateParams, InferFrom, InferTo, InferMaskTo, InferMaskFrom, ValidateNavigateOptions, ValidateNavigateOptionsArray, ValidateRedirectOptions, ValidateRedirectOptionsArray, ValidateId, InferStrict, InferShouldThrow, InferSelected, ValidateUseSearchResult, ValidateUseParamsResult, } from '@tanstack/router-core';
export { ScriptOnce } from './ScriptOnce.js';
export { Asset } from './Asset.js';
export { HeadContent } from './HeadContent.js';
export { Scripts } from './Scripts.js';
