{"version": 3, "file": "matchContext.js", "sources": ["../../src/matchContext.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport const matchContext = React.createContext<string | undefined>(undefined)\n\n// N.B. this only exists so we can conditionally call useContext on it when we are not interested in the nearest match\nexport const dummyMatchContext = React.createContext<string | undefined>(\n  undefined,\n)\n"], "names": [], "mappings": ";AAEa,MAAA,eAAe,MAAM,cAAkC,MAAS;AAGtE,MAAM,oBAAoB,MAAM;AAAA,EACrC;AACF;"}