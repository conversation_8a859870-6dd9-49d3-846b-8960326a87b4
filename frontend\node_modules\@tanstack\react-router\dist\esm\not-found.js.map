{"version": 3, "file": "not-found.js", "sources": ["../../src/not-found.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { isNotFound } from '@tanstack/router-core'\nimport { CatchBoundary } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport type { ErrorInfo } from 'react'\nimport type { NotFoundError } from '@tanstack/router-core'\n\nexport function CatchNotFound(props: {\n  fallback?: (error: NotFoundError) => React.ReactElement\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n  children: React.ReactNode\n}) {\n  // TODO: Some way for the user to programmatically reset the not-found boundary?\n  const resetKey = useRouterState({\n    select: (s) => `not-found-${s.location.pathname}-${s.status}`,\n  })\n\n  return (\n    <CatchBoundary\n      getResetKey={() => resetKey}\n      onCatch={(error, errorInfo) => {\n        if (isNotFound(error)) {\n          props.onCatch?.(error, errorInfo)\n        } else {\n          throw error\n        }\n      }}\n      errorComponent={({ error }: { error: Error }) => {\n        if (isNotFound(error)) {\n          return props.fallback?.(error)\n        } else {\n          throw error\n        }\n      }}\n    >\n      {props.children}\n    </CatchBoundary>\n  )\n}\n\nexport function DefaultGlobalNotFound() {\n  return <p>Not Found</p>\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,cAAc,OAI3B;AAED,QAAM,WAAW,eAAe;AAAA,IAC9B,QAAQ,CAAC,MAAM,aAAa,EAAE,SAAS,QAAQ,IAAI,EAAE,MAAM;AAAA,EAAA,CAC5D;AAGC,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,aAAa,MAAM;AAAA,MACnB,SAAS,CAAC,OAAO,cAAc;;AACzB,YAAA,WAAW,KAAK,GAAG;AACf,sBAAA,YAAA,+BAAU,OAAO;AAAA,QAAS,OAC3B;AACC,gBAAA;AAAA,QAAA;AAAA,MAEV;AAAA,MACA,gBAAgB,CAAC,EAAE,YAA8B;;AAC3C,YAAA,WAAW,KAAK,GAAG;AACd,kBAAA,WAAM,aAAN,+BAAiB;AAAA,QAAK,OACxB;AACC,gBAAA;AAAA,QAAA;AAAA,MAEV;AAAA,MAEC,UAAM,MAAA;AAAA,IAAA;AAAA,EACT;AAEJ;AAEO,SAAS,wBAAwB;AAC/B,SAAA,oBAAC,OAAE,UAAS,YAAA,CAAA;AACrB;"}