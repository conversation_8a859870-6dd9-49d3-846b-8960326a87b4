import { StructuralSharingOption, ValidateSelected } from './structuralSharing.js';
import { AnyRouter, RegisteredRouter, ResolveUseLoaderData, StrictOrFrom, UseLoaderDataResult } from '@tanstack/router-core';
export interface UseLoaderDataBaseOptions<TRouter extends AnyRouter, TFrom, TStrict extends boolean, TSelected, TStructuralSharing> {
    select?: (match: ResolveUseLoaderData<TRouter, TFrom, TStrict>) => ValidateSelected<TRouter, TSelected, TStructuralSharing>;
}
export type UseLoaderDataOptions<TRouter extends AnyRouter, T<PERSON>rom extends string | undefined, TStrict extends boolean, TSelected, TStructuralSharing> = StrictOrFrom<TRouter, TFrom, TStrict> & UseLoaderDataBaseOptions<TRouter, TFrom, TStrict, TSelected, TStructuralSharing> & StructuralSharingOption<TRouter, TSelected, TStructuralSharing>;
export type UseLoaderDataRoute<out TId> = <TRouter extends AnyRouter = RegisteredRouter, TSelected = unknown, TStructuralSharing extends boolean = boolean>(opts?: UseLoaderDataBaseOptions<TRouter, TId, true, TSelected, TStructuralSharing> & StructuralSharingOption<TRouter, TSelected, TStructuralSharing>) => UseLoaderDataResult<TRouter, TId, true, TSelected>;
export declare function useLoaderData<TRouter extends AnyRouter = RegisteredRouter, const TFrom extends string | undefined = undefined, TStrict extends boolean = true, TSelected = unknown, TStructuralSharing extends boolean = boolean>(opts: UseLoaderDataOptions<TRouter, TFrom, TStrict, TSelected, TStructuralSharing>): UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected>;
