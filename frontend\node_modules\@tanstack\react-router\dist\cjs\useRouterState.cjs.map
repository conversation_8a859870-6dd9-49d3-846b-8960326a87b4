{"version": 3, "file": "useRouterState.cjs", "sources": ["../../src/useRouterState.tsx"], "sourcesContent": ["import { useStore } from '@tanstack/react-store'\nimport { useRef } from 'react'\nimport { replaceEqualDeep } from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterState,\n} from '@tanstack/router-core'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\n\nexport type UseRouterStateOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing,\n> = {\n  router?: TRouter\n  select?: (\n    state: RouterState<TRouter['routeTree']>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n} & StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseRouterStateResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected ? RouterState<TRouter['routeTree']> : TSelected\n\nexport function useRouterState<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseRouterStateOptions<TRouter, TSelected, TStructuralSharing>,\n): UseRouterStateResult<TRouter, TSelected> {\n  const contextRouter = useRouter<TRouter>({\n    warn: opts?.router === undefined,\n  })\n  const router = opts?.router || contextRouter\n  const previousResult =\n    useRef<ValidateSelected<TRouter, TSelected, TStructuralSharing>>(undefined)\n\n  return useStore(router.__store, (state) => {\n    if (opts?.select) {\n      if (opts.structuralSharing ?? router.options.defaultStructuralSharing) {\n        const newSlice = replaceEqualDeep(\n          previousResult.current,\n          opts.select(state),\n        )\n        previousResult.current = newSlice\n        return newSlice\n      }\n      return opts.select(state)\n    }\n    return state\n  }) as UseRouterStateResult<TRouter, TSelected>\n}\n"], "names": ["useRouter", "useRef", "useStore", "replaceEqualDeep"], "mappings": ";;;;;;AA8BO,SAAS,eAKd,MAC0C;AAC1C,QAAM,gBAAgBA,UAAAA,UAAmB;AAAA,IACvC,OAAM,6BAAM,YAAW;AAAA,EAAA,CACxB;AACK,QAAA,UAAS,6BAAM,WAAU;AACzB,QAAA,iBACJC,aAAiE,MAAS;AAE5E,SAAOC,oBAAS,OAAO,SAAS,CAAC,UAAU;AACzC,QAAI,6BAAM,QAAQ;AAChB,UAAI,KAAK,qBAAqB,OAAO,QAAQ,0BAA0B;AACrE,cAAM,WAAWC,WAAA;AAAA,UACf,eAAe;AAAA,UACf,KAAK,OAAO,KAAK;AAAA,QACnB;AACA,uBAAe,UAAU;AAClB,eAAA;AAAA,MAAA;AAEF,aAAA,KAAK,OAAO,KAAK;AAAA,IAAA;AAEnB,WAAA;AAAA,EAAA,CACR;AACH;;"}