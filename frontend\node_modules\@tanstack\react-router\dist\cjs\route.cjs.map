{"version": 3, "file": "route.cjs", "sources": ["../../src/route.tsx"], "sourcesContent": ["import {\n  BaseRootRoute,\n  <PERSON>Route,\n  BaseRouteApi,\n  notFound,\n} from '@tanstack/router-core'\nimport React from 'react'\nimport { useLoaderData } from './useLoaderData'\nimport { useLoaderDeps } from './useLoaderDeps'\nimport { useParams } from './useParams'\nimport { useSearch } from './useSearch'\nimport { useNavigate } from './useNavigate'\nimport { useMatch } from './useMatch'\nimport { useRouter } from './useRouter'\nimport { Link } from './link'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouter,\n  ConstrainLiteral,\n  ErrorComponentProps,\n  NotFoundError,\n  NotFoundRouteProps,\n  RegisteredRouter,\n  ResolveFullPath,\n  ResolveId,\n  ResolveParams,\n  RootRoute as RootRouteCore,\n  RootRouteId,\n  RootRouteOptions,\n  RouteConstraints,\n  Route as RouteCore,\n  RouteIds,\n  RouteMask,\n  RouteOptions,\n  RouteTypesById,\n  RouterCore,\n  ToMaskOptions,\n  UseNavigateResult,\n} from '@tanstack/router-core'\nimport type { UseLoaderDataRoute } from './useLoaderData'\nimport type { UseMatchRoute } from './useMatch'\nimport type { UseLoaderDepsRoute } from './useLoaderDeps'\nimport type { UseParamsRoute } from './useParams'\nimport type { UseSearchRoute } from './useSearch'\nimport type { UseRouteContextRoute } from './useRouteContext'\nimport type { LinkComponentRoute } from './link'\n\ndeclare module '@tanstack/router-core' {\n  export interface UpdatableRouteOptionsExtensions {\n    component?: RouteComponent\n    errorComponent?: false | null | ErrorRouteComponent\n    notFoundComponent?: NotFoundRouteComponent\n    pendingComponent?: RouteComponent\n  }\n\n  export interface RouteExtensions<\n    in out TId extends string,\n    in out TFullPath extends string,\n  > {\n    useMatch: UseMatchRoute<TId>\n    useRouteContext: UseRouteContextRoute<TId>\n    useSearch: UseSearchRoute<TId>\n    useParams: UseParamsRoute<TId>\n    useLoaderDeps: UseLoaderDepsRoute<TId>\n    useLoaderData: UseLoaderDataRoute<TId>\n    useNavigate: () => UseNavigateResult<TFullPath>\n    Link: LinkComponentRoute<TFullPath>\n  }\n}\n\nexport function getRouteApi<\n  const TId,\n  TRouter extends AnyRouter = RegisteredRouter,\n>(id: ConstrainLiteral<TId, RouteIds<TRouter['routeTree']>>) {\n  return new RouteApi<TId, TRouter>({ id })\n}\n\nexport class RouteApi<\n  TId,\n  TRouter extends AnyRouter = RegisteredRouter,\n> extends BaseRouteApi<TId, TRouter> {\n  /**\n   * @deprecated Use the `getRouteApi` function instead.\n   */\n  constructor({ id }: { id: TId }) {\n    super({ id })\n  }\n\n  useMatch: UseMatchRoute<TId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TId> = (opts) => {\n    return useMatch({\n      from: this.id as any,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id, strict: false } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id, strict: false } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<\n    RouteTypesById<TRouter, TId>['fullPath']\n  > => {\n    const router = useRouter()\n    return useNavigate({ from: router.routesById[this.id as string].fullPath })\n  }\n\n  notFound = (opts?: NotFoundError) => {\n    return notFound({ routeId: this.id as string, ...opts })\n  }\n\n  Link: LinkComponentRoute<RouteTypesById<TRouter, TId>['fullPath']> =\n    React.forwardRef((props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      const router = useRouter()\n      const fullPath = router.routesById[this.id as string].fullPath\n      return <Link ref={ref} from={fullPath as never} {...props} />\n    }) as unknown as LinkComponentRoute<\n      RouteTypesById<TRouter, TId>['fullPath']\n    >\n}\n\nexport class Route<\n    in out TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n    in out TPath extends RouteConstraints['TPath'] = '/',\n    in out TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n      TParentRoute,\n      TPath\n    >,\n    in out TCustomId extends RouteConstraints['TCustomId'] = string,\n    in out TId extends RouteConstraints['TId'] = ResolveId<\n      TParentRoute,\n      TCustomId,\n      TPath\n    >,\n    in out TSearchValidator = undefined,\n    in out TParams = ResolveParams<TPath>,\n    in out TRouterContext = AnyContext,\n    in out TRouteContextFn = AnyContext,\n    in out TBeforeLoadFn = AnyContext,\n    in out TLoaderDeps extends Record<string, any> = {},\n    in out TLoaderFn = undefined,\n    in out TChildren = unknown,\n    in out TFileRouteTypes = unknown,\n  >\n  extends BaseRoute<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  implements\n    RouteCore<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n{\n  /**\n   * @deprecated Use the `createRoute` function instead.\n   */\n  constructor(\n    options?: RouteOptions<\n      TParentRoute,\n      TId,\n      TCustomId,\n      TFullPath,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) {\n    super(options)\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<TId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TId> = (opts?) => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<TFullPath> => {\n    return useNavigate({ from: this.fullPath })\n  }\n\n  Link: LinkComponentRoute<TFullPath> = React.forwardRef(\n    (props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      return <Link ref={ref} from={this.fullPath as never} {...props} />\n    },\n  ) as unknown as LinkComponentRoute<TFullPath>\n}\n\nexport function createRoute<\n  TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n  TPath extends RouteConstraints['TPath'] = '/',\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    TPath\n  >,\n  TCustomId extends RouteConstraints['TCustomId'] = string,\n  TId extends RouteConstraints['TId'] = ResolveId<\n    TParentRoute,\n    TCustomId,\n    TPath\n  >,\n  TSearchValidator = undefined,\n  TParams = ResolveParams<TPath>,\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TChildren = unknown,\n>(\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n): Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  AnyContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren\n> {\n  return new Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  >(options)\n}\n\nexport type AnyRootRoute = RootRoute<any, any, any, any, any, any, any, any>\n\nexport function createRootRouteWithContext<TRouterContext extends {}>() {\n  return <\n    TRouteContextFn = AnyContext,\n    TBeforeLoadFn = AnyContext,\n    TSearchValidator = undefined,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderFn = undefined,\n  >(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) => {\n    return createRootRoute<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >(options as any)\n  }\n}\n\n/**\n * @deprecated Use the `createRootRouteWithContext` function instead.\n */\nexport const rootRouteWithContext = createRootRouteWithContext\n\nexport class RootRoute<\n    in out TSearchValidator = undefined,\n    in out TRouterContext = {},\n    in out TRouteContextFn = AnyContext,\n    in out TBeforeLoadFn = AnyContext,\n    in out TLoaderDeps extends Record<string, any> = {},\n    in out TLoaderFn = undefined,\n    in out TChildren = unknown,\n    in out TFileRouteTypes = unknown,\n  >\n  extends BaseRootRoute<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  implements\n    RootRouteCore<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n{\n  /**\n   * @deprecated `RootRoute` is now an internal implementation detail. Use `createRootRoute()` instead.\n   */\n  constructor(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) {\n    super(options)\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<RootRouteId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<RootRouteId> = (opts) => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<RootRouteId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<RootRouteId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<RootRouteId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<RootRouteId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<'/'> => {\n    return useNavigate({ from: this.fullPath })\n  }\n\n  Link: LinkComponentRoute<'/'> = React.forwardRef(\n    (props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      return <Link ref={ref} from={this.fullPath} {...props} />\n    },\n  ) as unknown as LinkComponentRoute<'/'>\n}\n\nexport function createRootRoute<\n  TSearchValidator = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n>(\n  options?: RootRouteOptions<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn\n  >,\n): RootRoute<\n  TSearchValidator,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  unknown,\n  unknown\n> {\n  return new RootRoute<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn\n  >(options)\n}\n\nexport function createRouteMask<\n  TRouteTree extends AnyRoute,\n  TFrom extends string,\n  TTo extends string,\n>(\n  opts: {\n    routeTree: TRouteTree\n  } & ToMaskOptions<RouterCore<TRouteTree, 'never', boolean>, TFrom, TTo>,\n): RouteMask<TRouteTree> {\n  return opts as any\n}\n\nexport type ReactNode = any\n\nexport type SyncRouteComponent<TProps> =\n  | ((props: TProps) => ReactNode)\n  | React.LazyExoticComponent<(props: TProps) => ReactNode>\n\nexport type AsyncRouteComponent<TProps> = SyncRouteComponent<TProps> & {\n  preload?: () => Promise<void>\n}\n\nexport type RouteComponent<TProps = any> = AsyncRouteComponent<TProps>\n\nexport type ErrorRouteComponent = RouteComponent<ErrorComponentProps>\n\nexport type NotFoundRouteComponent = SyncRouteComponent<NotFoundRouteProps>\n\nexport class NotFoundRoute<\n  TParentRoute extends AnyRootRoute,\n  TRouterContext = AnyContext,\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TSearchValidator = undefined,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TChildren = unknown,\n> extends Route<\n  TParentRoute,\n  '/404',\n  '/404',\n  '404',\n  '404',\n  TSearchValidator,\n  {},\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren\n> {\n  constructor(\n    options: Omit<\n      RouteOptions<\n        TParentRoute,\n        string,\n        string,\n        string,\n        string,\n        TSearchValidator,\n        {},\n        TLoaderDeps,\n        TLoaderFn,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      | 'caseSensitive'\n      | 'parseParams'\n      | 'stringifyParams'\n      | 'path'\n      | 'id'\n      | 'params'\n    >,\n  ) {\n    super({\n      ...(options as any),\n      id: '404',\n    })\n  }\n}\n"], "names": ["BaseRouteApi", "useMatch", "useSearch", "useParams", "useLoaderDeps", "useLoaderData", "useRouter", "useNavigate", "notFound", "Link", "BaseRoute", "BaseRootRoute"], "mappings": ";;;;;;;;;;;;;AAuEO,SAAS,YAGd,IAA2D;AAC3D,SAAO,IAAI,SAAuB,EAAE,IAAI;AAC1C;AAEO,MAAM,iBAGHA,WAAAA,aAA2B;AAAA;AAAA;AAAA;AAAA,EAInC,YAAY,EAAE,MAAmB;AACzB,UAAA,EAAE,IAAI;AAGd,SAAA,WAA+B,CAAC,SAAS;AACvC,aAAOC,kBAAS;AAAA,QACd,QAAQ,6BAAM;AAAA,QACd,MAAM,KAAK;AAAA,QACX,mBAAmB,6BAAM;AAAA,MAAA,CACnB;AAAA,IACV;AAEA,SAAA,kBAA6C,CAAC,SAAS;AACrD,aAAOA,kBAAS;AAAA,QACd,MAAM,KAAK;AAAA,QACX,QAAQ,CAAC,OAAO,6BAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;AAAA,MAAA,CAC3D;AAAA,IACH;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAOC,oBAAU;AAAA,QACf,QAAQ,6BAAM;AAAA,QACd,mBAAmB,6BAAM;AAAA,QACzB,MAAM,KAAK;AAAA,MAAA,CACL;AAAA,IACV;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAOC,oBAAU;AAAA,QACf,QAAQ,6BAAM;AAAA,QACd,mBAAmB,6BAAM;AAAA,QACzB,MAAM,KAAK;AAAA,MAAA,CACL;AAAA,IACV;AAEA,SAAA,gBAAyC,CAAC,SAAS;AAC1C,aAAAC,cAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,QAAQ,OAAc;AAAA,IACvE;AAEA,SAAA,gBAAyC,CAAC,SAAS;AAC1C,aAAAC,cAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,QAAQ,OAAc;AAAA,IACvE;AAEA,SAAA,cAAc,MAET;AACH,YAAM,SAASC,UAAAA,UAAU;AAClB,aAAAC,YAAA,YAAY,EAAE,MAAM,OAAO,WAAW,KAAK,EAAY,EAAE,UAAU;AAAA,IAC5E;AAEA,SAAA,WAAW,CAAC,SAAyB;AACnC,aAAOC,WAAAA,SAAS,EAAE,SAAS,KAAK,IAAc,GAAG,MAAM;AAAA,IACzD;AAEA,SAAA,OACE,MAAM,WAAW,CAAC,OAAO,QAA+C;AACtE,YAAM,SAASF,UAAAA,UAAU;AACzB,YAAM,WAAW,OAAO,WAAW,KAAK,EAAY,EAAE;AACtD,4CAAQG,KAAAA,MAAK,EAAA,KAAU,MAAM,UAAoB,GAAG,OAAO;AAAA,IAAA,CAC5D;AAAA,EAAA;AAGL;AAEO,MAAM,cAuBHC,WAAAA,UAiCV;AAAA;AAAA;AAAA;AAAA,EAIE,YACE,SAcA;AACA,UAAM,OAAO;AAIf,SAAA,WAA+B,CAAC,SAAS;AACvC,aAAOT,kBAAS;AAAA,QACd,QAAQ,6BAAM;AAAA,QACd,MAAM,KAAK;AAAA,QACX,mBAAmB,6BAAM;AAAA,MAAA,CACnB;AAAA,IACV;AAEA,SAAA,kBAA6C,CAAC,SAAU;AACtD,aAAOA,kBAAS;AAAA,QACd,GAAG;AAAA,QACH,MAAM,KAAK;AAAA,QACX,QAAQ,CAAC,OAAO,6BAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;AAAA,MAAA,CAC3D;AAAA,IACH;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAOC,oBAAU;AAAA,QACf,QAAQ,6BAAM;AAAA,QACd,mBAAmB,6BAAM;AAAA,QACzB,MAAM,KAAK;AAAA,MAAA,CACL;AAAA,IACV;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAOC,oBAAU;AAAA,QACf,QAAQ,6BAAM;AAAA,QACd,mBAAmB,6BAAM;AAAA,QACzB,MAAM,KAAK;AAAA,MAAA,CACL;AAAA,IACV;AAEA,SAAA,gBAAyC,CAAC,SAAS;AACjD,aAAOC,cAAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAW;AAAA,IACxD;AAEA,SAAA,gBAAyC,CAAC,SAAS;AACjD,aAAOC,cAAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAW;AAAA,IACxD;AAEA,SAAA,cAAc,MAAoC;AAChD,aAAOE,YAAY,YAAA,EAAE,MAAM,KAAK,UAAU;AAAA,IAC5C;AAEA,SAAA,OAAsC,MAAM;AAAA,MAC1C,CAAC,OAAO,QAA+C;AACrD,8CAAQE,KAAK,MAAA,EAAA,KAAU,MAAM,KAAK,UAAoB,GAAG,OAAO;AAAA,MAAA;AAAA,IAEpE;AArDI,SAAa,WAAW,OAAO,IAAI,YAAY;AAAA,EAAA;AAsDrD;AAEO,SAAS,YAqBd,SA4BA;AACO,SAAA,IAAI,MAcT,OAAO;AACX;AAIO,SAAS,6BAAwD;AACtE,SAAO,CAOL,YAQG;AACH,WAAO,gBAOL,OAAc;AAAA,EAClB;AACF;AAKO,MAAM,uBAAuB;AAE7B,MAAM,kBAUHE,WAAAA,cAqBV;AAAA;AAAA;AAAA;AAAA,EAIE,YACE,SAQA;AACA,UAAM,OAAO;AAIf,SAAA,WAAuC,CAAC,SAAS;AAC/C,aAAOV,kBAAS;AAAA,QACd,QAAQ,6BAAM;AAAA,QACd,MAAM,KAAK;AAAA,QACX,mBAAmB,6BAAM;AAAA,MAAA,CACnB;AAAA,IACV;AAEA,SAAA,kBAAqD,CAAC,SAAS;AAC7D,aAAOA,kBAAS;AAAA,QACd,GAAG;AAAA,QACH,MAAM,KAAK;AAAA,QACX,QAAQ,CAAC,OAAO,6BAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;AAAA,MAAA,CAC3D;AAAA,IACH;AAEA,SAAA,YAAyC,CAAC,SAAS;AAEjD,aAAOC,oBAAU;AAAA,QACf,QAAQ,6BAAM;AAAA,QACd,mBAAmB,6BAAM;AAAA,QACzB,MAAM,KAAK;AAAA,MAAA,CACL;AAAA,IACV;AAEA,SAAA,YAAyC,CAAC,SAAS;AAEjD,aAAOC,oBAAU;AAAA,QACf,QAAQ,6BAAM;AAAA,QACd,mBAAmB,6BAAM;AAAA,QACzB,MAAM,KAAK;AAAA,MAAA,CACL;AAAA,IACV;AAEA,SAAA,gBAAiD,CAAC,SAAS;AACzD,aAAOC,cAAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAW;AAAA,IACxD;AAEA,SAAA,gBAAiD,CAAC,SAAS;AACzD,aAAOC,cAAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAW;AAAA,IACxD;AAEA,SAAA,cAAc,MAA8B;AAC1C,aAAOE,YAAY,YAAA,EAAE,MAAM,KAAK,UAAU;AAAA,IAC5C;AAEA,SAAA,OAAgC,MAAM;AAAA,MACpC,CAAC,OAAO,QAA+C;AACrD,8CAAQE,KAAK,MAAA,EAAA,KAAU,MAAM,KAAK,UAAW,GAAG,OAAO;AAAA,MAAA;AAAA,IAE3D;AArDI,SAAa,WAAW,OAAO,IAAI,YAAY;AAAA,EAAA;AAsDrD;AAEO,SAAS,gBAQd,SAiBA;AACO,SAAA,IAAI,UAOT,OAAO;AACX;AAEO,SAAS,gBAKd,MAGuB;AAChB,SAAA;AACT;AAkBO,MAAM,sBASH,MAcR;AAAA,EACA,YACE,SAsBA;AACM,UAAA;AAAA,MACJ,GAAI;AAAA,MACJ,IAAI;AAAA,IAAA,CACL;AAAA,EAAA;AAEL;;;;;;;;;;;"}