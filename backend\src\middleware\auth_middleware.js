import {verifyToken} from "../utils/helper.js";
import {UnauthorizedError} from "../utils/errorHandler.js";
export const authMiddleware = async (req, res, next) => {
  const token = req.cookies.accesstoken;
  if (!token) {
    throw new UnauthorizedError("Unauthorized");
  }
  try {
    const decoded = verifyToken(token);
    const user = await findUserById(decoded);
    if (!user) {
      throw new UnauthorizedError("Unauthorized");
    }
    req.user = user;
    next();
  } catch (err) {
    throw new UnauthorizedError("Unauthorized");
  }
};
