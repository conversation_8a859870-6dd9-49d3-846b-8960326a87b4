{"version": 3, "file": "ClientOnly.cjs", "sources": ["../../src/ClientOnly.tsx"], "sourcesContent": ["import React from 'react'\n\nexport interface ClientOnlyProps {\n  /**\n   * The children to render if the JS is loaded.\n   */\n  children: React.ReactNode\n  /**\n   * The fallback component to render if the JS is not yet loaded.\n   */\n  fallback?: React.ReactNode\n}\n\n/**\n * Render the children only after the JS has loaded client-side. Use an optional\n * fallback component if the JS is not yet loaded.\n *\n * @example\n * Render a Chart component if JS loads, renders a simple FakeChart\n * component server-side or if there is no JS. The FakeChart can have only the\n * UI without the behavior or be a loading spinner or skeleton.\n *\n * ```tsx\n * return (\n *   <ClientOnly fallback={<FakeChart />}>\n *     <Chart />\n *   </ClientOnly>\n * )\n * ```\n */\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  return useHydrated() ? (\n    <React.Fragment>{children}</React.Fragment>\n  ) : (\n    <React.Fragment>{fallback}</React.Fragment>\n  )\n}\n\n/**\n * Return a boolean indicating if the JS has been hydrated already.\n * When doing Server-Side Rendering, the result will always be false.\n * When doing Client-Side Rendering, the result will always be false on the\n * first render and true from then on. Even if a new component renders it will\n * always start with true.\n *\n * @example\n * ```tsx\n * // Disable a button that needs JS to work.\n * let hydrated = useHydrated()\n * return (\n *   <button type=\"button\" disabled={!hydrated} onClick={doSomethingCustom}>\n *     Click me\n *   </button>\n * )\n * ```\n * @returns True if the JS has been hydrated already, false otherwise.\n */\nfunction useHydrated(): boolean {\n  return React.useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false,\n  )\n}\n\nfunction subscribe() {\n  return () => {}\n}\n"], "names": ["jsx"], "mappings": ";;;;AA8BO,SAAS,WAAW,EAAE,UAAU,WAAW,QAAyB;AACzE,SAAO,YAAY,IAChBA,2BAAAA,IAAA,MAAM,UAAN,EAAgB,SAAS,CAAA,IAEzBA,2BAAAA,IAAA,MAAM,UAAN,EAAgB,UAAS,SAAA,CAAA;AAE9B;AAqBA,SAAS,cAAuB;AAC9B,SAAO,MAAM;AAAA,IACX;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACF;AAEA,SAAS,YAAY;AACnB,SAAO,MAAM;AAAA,EAAC;AAChB;;"}