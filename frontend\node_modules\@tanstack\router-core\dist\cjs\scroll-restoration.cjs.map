{"version": 3, "file": "scroll-restoration.cjs", "sources": ["../../src/scroll-restoration.ts"], "sourcesContent": ["import { functionalUpdate } from './utils'\nimport type { AnyRouter } from './router'\nimport type { ParsedLocation } from './location'\nimport type { NonNullableUpdater } from './utils'\n\nexport type ScrollRestorationEntry = { scrollX: number; scrollY: number }\n\nexport type ScrollRestorationByElement = Record<string, ScrollRestorationEntry>\n\nexport type ScrollRestorationByKey = Record<string, ScrollRestorationByElement>\n\nexport type ScrollRestorationCache = {\n  state: ScrollRestorationByKey\n  set: (updater: NonNullableUpdater<ScrollRestorationByKey>) => void\n}\nexport type ScrollRestorationOptions = {\n  getKey?: (location: ParsedLocation) => string\n  scrollBehavior?: ScrollToOptions['behavior']\n}\n\nfunction getSafeSessionStorage() {\n  try {\n    if (\n      typeof window !== 'undefined' &&\n      typeof window.sessionStorage === 'object'\n    ) {\n      return window.sessionStorage\n    }\n  } catch {\n    return undefined\n  }\n  return undefined\n}\n\nexport const storageKey = 'tsr-scroll-restoration-v1_3'\n\nconst throttle = (fn: (...args: Array<any>) => void, wait: number) => {\n  let timeout: any\n  return (...args: Array<any>) => {\n    if (!timeout) {\n      timeout = setTimeout(() => {\n        fn(...args)\n        timeout = null\n      }, wait)\n    }\n  }\n}\n\nfunction createScrollRestorationCache(): ScrollRestorationCache | undefined {\n  const safeSessionStorage = getSafeSessionStorage()\n  if (!safeSessionStorage) {\n    return undefined\n  }\n\n  const persistedState = safeSessionStorage.getItem(storageKey)\n  let state: ScrollRestorationByKey = persistedState\n    ? JSON.parse(persistedState)\n    : {}\n\n  return {\n    state,\n    // This setter is simply to make sure that we set the sessionStorage right\n    // after the state is updated. It doesn't necessarily need to be a functional\n    // update.\n    set: (updater) => (\n      (state = functionalUpdate(updater, state) || state),\n      safeSessionStorage.setItem(storageKey, JSON.stringify(state))\n    ),\n  }\n}\n\nexport const scrollRestorationCache = createScrollRestorationCache()\n\n/**\n * The default `getKey` function for `useScrollRestoration`.\n * It returns the `key` from the location state or the `href` of the location.\n *\n * The `location.href` is used as a fallback to support the use case where the location state is not available like the initial render.\n */\n\nexport const defaultGetScrollRestorationKey = (location: ParsedLocation) => {\n  return location.state.key! || location.href\n}\n\nexport function getCssSelector(el: any): string {\n  const path = []\n  let parent\n  while ((parent = el.parentNode)) {\n    path.unshift(\n      `${el.tagName}:nth-child(${([].indexOf as any).call(parent.children, el) + 1})`,\n    )\n    el = parent\n  }\n  return `${path.join(' > ')}`.toLowerCase()\n}\n\nlet ignoreScroll = false\n\n// NOTE: This function must remain pure and not use any outside variables\n// unless they are passed in as arguments. Why? Because we need to be able to\n// toString() it into a script tag to execute as early as possible in the browser\n// during SSR. Additionally, we also call it from within the router lifecycle\nexport function restoreScroll(\n  storageKey: string,\n  key: string | undefined,\n  behavior: ScrollToOptions['behavior'] | undefined,\n  shouldScrollRestoration: boolean | undefined,\n  scrollToTopSelectors:\n    | Array<string | (() => Element | null | undefined)>\n    | undefined,\n) {\n  let byKey: ScrollRestorationByKey\n\n  try {\n    byKey = JSON.parse(sessionStorage.getItem(storageKey) || '{}')\n  } catch (error: any) {\n    console.error(error)\n    return\n  }\n\n  const resolvedKey = key || window.history.state?.key\n  const elementEntries = byKey[resolvedKey]\n\n  //\n  ignoreScroll = true\n\n  //\n  ;(() => {\n    // If we have a cached entry for this location state,\n    // we always need to prefer that over the hash scroll.\n    if (shouldScrollRestoration && elementEntries) {\n      for (const elementSelector in elementEntries) {\n        const entry = elementEntries[elementSelector]!\n        if (elementSelector === 'window') {\n          window.scrollTo({\n            top: entry.scrollY,\n            left: entry.scrollX,\n            behavior,\n          })\n        } else if (elementSelector) {\n          const element = document.querySelector(elementSelector)\n          if (element) {\n            element.scrollLeft = entry.scrollX\n            element.scrollTop = entry.scrollY\n          }\n        }\n      }\n\n      return\n    }\n\n    // If we don't have a cached entry for the hash,\n    // Which means we've never seen this location before,\n    // we need to check if there is a hash in the URL.\n    // If there is, we need to scroll it's ID into view.\n    const hash = window.location.hash.split('#')[1]\n\n    if (hash) {\n      const hashScrollIntoViewOptions =\n        (window.history.state || {}).__hashScrollIntoViewOptions ?? true\n\n      if (hashScrollIntoViewOptions) {\n        const el = document.getElementById(hash)\n        if (el) {\n          el.scrollIntoView(hashScrollIntoViewOptions)\n        }\n      }\n\n      return\n    }\n\n    // If there is no cached entry for the hash and there is no hash in the URL,\n    // we need to scroll to the top of the page for every scrollToTop element\n    ;[\n      'window',\n      ...(scrollToTopSelectors?.filter((d) => d !== 'window') ?? []),\n    ].forEach((selector) => {\n      const element =\n        selector === 'window'\n          ? window\n          : typeof selector === 'function'\n            ? selector()\n            : document.querySelector(selector)\n      if (element) {\n        element.scrollTo({\n          top: 0,\n          left: 0,\n          behavior,\n        })\n      }\n    })\n  })()\n\n  //\n  ignoreScroll = false\n}\n\nexport function setupScrollRestoration(router: AnyRouter, force?: boolean) {\n  if (scrollRestorationCache === undefined) {\n    return\n  }\n  const shouldScrollRestoration =\n    force ?? router.options.scrollRestoration ?? false\n\n  if (shouldScrollRestoration) {\n    router.isScrollRestoring = true\n  }\n\n  if (typeof document === 'undefined' || router.isScrollRestorationSetup) {\n    return\n  }\n\n  router.isScrollRestorationSetup = true\n\n  //\n  ignoreScroll = false\n\n  const getKey =\n    router.options.getScrollRestorationKey || defaultGetScrollRestorationKey\n\n  window.history.scrollRestoration = 'manual'\n\n  // // Create a MutationObserver to monitor DOM changes\n  // const mutationObserver = new MutationObserver(() => {\n  //   ;ignoreScroll = true\n  //   requestAnimationFrame(() => {\n  //     ;ignoreScroll = false\n\n  //     // Attempt to restore scroll position on each dom\n  //     // mutation until the user scrolls. We do this\n  //     // because dynamic content may come in at different\n  //     // ticks after the initial render and we want to\n  //     // keep up with that content as much as possible.\n  //     // As soon as the user scrolls, we no longer need\n  //     // to attempt router.\n  //     // console.log('mutation observer restoreScroll')\n  //     restoreScroll(\n  //       storageKey,\n  //       getKey(router.state.location),\n  //       router.options.scrollRestorationBehavior,\n  //     )\n  //   })\n  // })\n\n  // const observeDom = () => {\n  //   // Observe changes to the entire document\n  //   mutationObserver.observe(document, {\n  //     childList: true, // Detect added or removed child nodes\n  //     subtree: true, // Monitor all descendants\n  //     characterData: true, // Detect text content changes\n  //   })\n  // }\n\n  // const unobserveDom = () => {\n  //   mutationObserver.disconnect()\n  // }\n\n  // observeDom()\n\n  const onScroll = (event: Event) => {\n    // unobserveDom()\n\n    if (ignoreScroll || !router.isScrollRestoring) {\n      return\n    }\n\n    let elementSelector = ''\n\n    if (event.target === document || event.target === window) {\n      elementSelector = 'window'\n    } else {\n      const attrId = (event.target as Element).getAttribute(\n        'data-scroll-restoration-id',\n      )\n\n      if (attrId) {\n        elementSelector = `[data-scroll-restoration-id=\"${attrId}\"]`\n      } else {\n        elementSelector = getCssSelector(event.target)\n      }\n    }\n\n    const restoreKey = getKey(router.state.location)\n\n    scrollRestorationCache.set((state) => {\n      const keyEntry = (state[restoreKey] =\n        state[restoreKey] || ({} as ScrollRestorationByElement))\n\n      const elementEntry = (keyEntry[elementSelector] =\n        keyEntry[elementSelector] || ({} as ScrollRestorationEntry))\n\n      if (elementSelector === 'window') {\n        elementEntry.scrollX = window.scrollX || 0\n        elementEntry.scrollY = window.scrollY || 0\n      } else if (elementSelector) {\n        const element = document.querySelector(elementSelector)\n        if (element) {\n          elementEntry.scrollX = element.scrollLeft || 0\n          elementEntry.scrollY = element.scrollTop || 0\n        }\n      }\n\n      return state\n    })\n  }\n\n  // Throttle the scroll event to avoid excessive updates\n  if (typeof document !== 'undefined') {\n    document.addEventListener('scroll', throttle(onScroll, 100), true)\n  }\n\n  router.subscribe('onRendered', (event) => {\n    // unobserveDom()\n\n    const cacheKey = getKey(event.toLocation)\n\n    // If the user doesn't want to restore the scroll position,\n    // we don't need to do anything.\n    if (!router.resetNextScroll) {\n      router.resetNextScroll = true\n      return\n    }\n\n    restoreScroll(\n      storageKey,\n      cacheKey,\n      router.options.scrollRestorationBehavior || undefined,\n      router.isScrollRestoring || undefined,\n      router.options.scrollToTopSelectors || undefined,\n    )\n\n    if (router.isScrollRestoring) {\n      // Mark the location as having been seen\n      scrollRestorationCache.set((state) => {\n        state[cacheKey] = state[cacheKey] || ({} as ScrollRestorationByElement)\n\n        return state\n      })\n    }\n  })\n}\n\n/**\n * @internal\n * Handles hash-based scrolling after navigation completes.\n * To be used in framework-specific <Transitioner> components during the onResolved event.\n *\n * Provides hash scrolling for programmatic navigation when default browser handling is prevented.\n * @param router The router instance containing current location and state\n */\nexport function handleHashScroll(router: AnyRouter) {\n  if (typeof document !== 'undefined' && (document as any).querySelector) {\n    const hashScrollIntoViewOptions =\n      router.state.location.state.__hashScrollIntoViewOptions ?? true\n\n    if (hashScrollIntoViewOptions && router.state.location.hash !== '') {\n      const el = document.getElementById(router.state.location.hash)\n      if (el) {\n        el.scrollIntoView(hashScrollIntoViewOptions)\n      }\n    }\n  }\n}\n"], "names": ["functionalUpdate", "storageKey"], "mappings": ";;;AAoBA,SAAS,wBAAwB;AAC3B,MAAA;AACF,QACE,OAAO,WAAW,eAClB,OAAO,OAAO,mBAAmB,UACjC;AACA,aAAO,OAAO;AAAA,IAAA;AAAA,EAChB,QACM;AACC,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AAEO,MAAM,aAAa;AAE1B,MAAM,WAAW,CAAC,IAAmC,SAAiB;AAChE,MAAA;AACJ,SAAO,IAAI,SAAqB;AAC9B,QAAI,CAAC,SAAS;AACZ,gBAAU,WAAW,MAAM;AACzB,WAAG,GAAG,IAAI;AACA,kBAAA;AAAA,SACT,IAAI;AAAA,IAAA;AAAA,EAEX;AACF;AAEA,SAAS,+BAAmE;AAC1E,QAAM,qBAAqB,sBAAsB;AACjD,MAAI,CAAC,oBAAoB;AAChB,WAAA;AAAA,EAAA;AAGH,QAAA,iBAAiB,mBAAmB,QAAQ,UAAU;AAC5D,MAAI,QAAgC,iBAChC,KAAK,MAAM,cAAc,IACzB,CAAC;AAEE,SAAA;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAIA,KAAK,CAAC,aACH,QAAQA,MAAAA,iBAAiB,SAAS,KAAK,KAAK,OAC7C,mBAAmB,QAAQ,YAAY,KAAK,UAAU,KAAK,CAAC;AAAA,EAEhE;AACF;AAEO,MAAM,yBAAyB,6BAA6B;AAStD,MAAA,iCAAiC,CAAC,aAA6B;AACnE,SAAA,SAAS,MAAM,OAAQ,SAAS;AACzC;AAEO,SAAS,eAAe,IAAiB;AAC9C,QAAM,OAAO,CAAC;AACV,MAAA;AACI,SAAA,SAAS,GAAG,YAAa;AAC1B,SAAA;AAAA,MACH,GAAG,GAAG,OAAO,cAAe,CAAA,EAAG,QAAgB,KAAK,OAAO,UAAU,EAAE,IAAI,CAAC;AAAA,IAC9E;AACK,SAAA;AAAA,EAAA;AAEP,SAAO,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,YAAY;AAC3C;AAEA,IAAI,eAAe;AAMZ,SAAS,cACdC,aACA,KACA,UACA,yBACA,sBAGA;;AACI,MAAA;AAEA,MAAA;AACF,YAAQ,KAAK,MAAM,eAAe,QAAQA,WAAU,KAAK,IAAI;AAAA,WACtD,OAAY;AACnB,YAAQ,MAAM,KAAK;AACnB;AAAA,EAAA;AAGF,QAAM,cAAc,SAAO,YAAO,QAAQ,UAAf,mBAAsB;AAC3C,QAAA,iBAAiB,MAAM,WAAW;AAGzB,iBAAA;AAGd,GAAC,MAAM;AAGN,QAAI,2BAA2B,gBAAgB;AAC7C,iBAAW,mBAAmB,gBAAgB;AACtC,cAAA,QAAQ,eAAe,eAAe;AAC5C,YAAI,oBAAoB,UAAU;AAChC,iBAAO,SAAS;AAAA,YACd,KAAK,MAAM;AAAA,YACX,MAAM,MAAM;AAAA,YACZ;AAAA,UAAA,CACD;AAAA,mBACQ,iBAAiB;AACpB,gBAAA,UAAU,SAAS,cAAc,eAAe;AACtD,cAAI,SAAS;AACX,oBAAQ,aAAa,MAAM;AAC3B,oBAAQ,YAAY,MAAM;AAAA,UAAA;AAAA,QAC5B;AAAA,MACF;AAGF;AAAA,IAAA;AAOF,UAAM,OAAO,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC;AAE9C,QAAI,MAAM;AACR,YAAM,6BACH,OAAO,QAAQ,SAAS,CAAA,GAAI,+BAA+B;AAE9D,UAAI,2BAA2B;AACvB,cAAA,KAAK,SAAS,eAAe,IAAI;AACvC,YAAI,IAAI;AACN,aAAG,eAAe,yBAAyB;AAAA,QAAA;AAAA,MAC7C;AAGF;AAAA,IAAA;AAKD;AAAA,MACC;AAAA,MACA,IAAI,6DAAsB,OAAO,CAAC,MAAM,MAAM,cAAa,CAAA;AAAA,IAAC,EAC5D,QAAQ,CAAC,aAAa;AAChB,YAAA,UACJ,aAAa,WACT,SACA,OAAO,aAAa,aAClB,SAAS,IACT,SAAS,cAAc,QAAQ;AACvC,UAAI,SAAS;AACX,gBAAQ,SAAS;AAAA,UACf,KAAK;AAAA,UACL,MAAM;AAAA,UACN;AAAA,QAAA,CACD;AAAA,MAAA;AAAA,IACH,CACD;AAAA,EAAA,GACA;AAGY,iBAAA;AACjB;AAEgB,SAAA,uBAAuB,QAAmB,OAAiB;AACzE,MAAI,2BAA2B,QAAW;AACxC;AAAA,EAAA;AAEF,QAAM,0BACJ,SAAS,OAAO,QAAQ,qBAAqB;AAE/C,MAAI,yBAAyB;AAC3B,WAAO,oBAAoB;AAAA,EAAA;AAG7B,MAAI,OAAO,aAAa,eAAe,OAAO,0BAA0B;AACtE;AAAA,EAAA;AAGF,SAAO,2BAA2B;AAGnB,iBAAA;AAET,QAAA,SACJ,OAAO,QAAQ,2BAA2B;AAE5C,SAAO,QAAQ,oBAAoB;AAuC7B,QAAA,WAAW,CAAC,UAAiB;AAG7B,QAAA,gBAAgB,CAAC,OAAO,mBAAmB;AAC7C;AAAA,IAAA;AAGF,QAAI,kBAAkB;AAEtB,QAAI,MAAM,WAAW,YAAY,MAAM,WAAW,QAAQ;AACtC,wBAAA;AAAA,IAAA,OACb;AACC,YAAA,SAAU,MAAM,OAAmB;AAAA,QACvC;AAAA,MACF;AAEA,UAAI,QAAQ;AACV,0BAAkB,gCAAgC,MAAM;AAAA,MAAA,OACnD;AACa,0BAAA,eAAe,MAAM,MAAM;AAAA,MAAA;AAAA,IAC/C;AAGF,UAAM,aAAa,OAAO,OAAO,MAAM,QAAQ;AAExB,2BAAA,IAAI,CAAC,UAAU;AACpC,YAAM,WAAY,MAAM,UAAU,IAChC,MAAM,UAAU,KAAM,CAAC;AAEzB,YAAM,eAAgB,SAAS,eAAe,IAC5C,SAAS,eAAe,KAAM,CAAC;AAEjC,UAAI,oBAAoB,UAAU;AACnB,qBAAA,UAAU,OAAO,WAAW;AAC5B,qBAAA,UAAU,OAAO,WAAW;AAAA,iBAChC,iBAAiB;AACpB,cAAA,UAAU,SAAS,cAAc,eAAe;AACtD,YAAI,SAAS;AACE,uBAAA,UAAU,QAAQ,cAAc;AAChC,uBAAA,UAAU,QAAQ,aAAa;AAAA,QAAA;AAAA,MAC9C;AAGK,aAAA;AAAA,IAAA,CACR;AAAA,EACH;AAGI,MAAA,OAAO,aAAa,aAAa;AACnC,aAAS,iBAAiB,UAAU,SAAS,UAAU,GAAG,GAAG,IAAI;AAAA,EAAA;AAG5D,SAAA,UAAU,cAAc,CAAC,UAAU;AAGlC,UAAA,WAAW,OAAO,MAAM,UAAU;AAIpC,QAAA,CAAC,OAAO,iBAAiB;AAC3B,aAAO,kBAAkB;AACzB;AAAA,IAAA;AAGF;AAAA,MACE;AAAA,MACA;AAAA,MACA,OAAO,QAAQ,6BAA6B;AAAA,MAC5C,OAAO,qBAAqB;AAAA,MAC5B,OAAO,QAAQ,wBAAwB;AAAA,IACzC;AAEA,QAAI,OAAO,mBAAmB;AAEL,6BAAA,IAAI,CAAC,UAAU;AACpC,cAAM,QAAQ,IAAI,MAAM,QAAQ,KAAM,CAAC;AAEhC,eAAA;AAAA,MAAA,CACR;AAAA,IAAA;AAAA,EACH,CACD;AACH;AAUO,SAAS,iBAAiB,QAAmB;AAClD,MAAI,OAAO,aAAa,eAAgB,SAAiB,eAAe;AACtE,UAAM,4BACJ,OAAO,MAAM,SAAS,MAAM,+BAA+B;AAE7D,QAAI,6BAA6B,OAAO,MAAM,SAAS,SAAS,IAAI;AAClE,YAAM,KAAK,SAAS,eAAe,OAAO,MAAM,SAAS,IAAI;AAC7D,UAAI,IAAI;AACN,WAAG,eAAe,yBAAyB;AAAA,MAAA;AAAA,IAC7C;AAAA,EACF;AAEJ;;;;;;;;"}