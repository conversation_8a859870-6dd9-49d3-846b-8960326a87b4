{"version": 3, "file": "scroll-restoration.cjs", "sources": ["../../src/scroll-restoration.tsx"], "sourcesContent": ["import {\n  defaultGetScrollRestorationKey,\n  restoreScroll,\n  storageKey,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport { ScriptOnce } from './ScriptOnce'\n\nexport function ScrollRestoration() {\n  const router = useRouter()\n  const getKey =\n    router.options.getScrollRestorationKey || defaultGetScrollRestorationKey\n  const userKey = getKey(router.latestLocation)\n  const resolvedKey =\n    userKey !== defaultGetScrollRestorationKey(router.latestLocation)\n      ? userKey\n      : null\n\n  if (!router.isScrollRestoring || !router.isServer) {\n    return null\n  }\n\n  return (\n    <ScriptOnce\n      children={`(${restoreScroll.toString()})(${JSON.stringify(storageKey)},${JSON.stringify(resolvedKey)}, undefined, true)`}\n      log={false}\n    />\n  )\n}\n"], "names": ["useRouter", "defaultGetScrollRestorationKey", "jsx", "ScriptOnce", "restoreScroll", "storageKey"], "mappings": ";;;;;;AAQO,SAAS,oBAAoB;AAClC,QAAM,SAASA,UAAAA,UAAU;AACnB,QAAA,SACJ,OAAO,QAAQ,2BAA2BC,WAAA;AACtC,QAAA,UAAU,OAAO,OAAO,cAAc;AAC5C,QAAM,cACJ,YAAYA,WAAA,+BAA+B,OAAO,cAAc,IAC5D,UACA;AAEN,MAAI,CAAC,OAAO,qBAAqB,CAAC,OAAO,UAAU;AAC1C,WAAA;AAAA,EAAA;AAIP,SAAAC,2BAAA;AAAA,IAACC,WAAA;AAAA,IAAA;AAAA,MACC,UAAU,IAAIC,WAAAA,cAAc,SAAU,CAAA,KAAK,KAAK,UAAUC,WAAU,UAAA,CAAC,IAAI,KAAK,UAAU,WAAW,CAAC;AAAA,MACpG,KAAK;AAAA,IAAA;AAAA,EACP;AAEJ;;"}