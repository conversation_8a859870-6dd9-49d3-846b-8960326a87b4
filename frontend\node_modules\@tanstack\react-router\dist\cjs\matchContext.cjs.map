{"version": 3, "file": "matchContext.cjs", "sources": ["../../src/matchContext.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport const matchContext = React.createContext<string | undefined>(undefined)\n\n// N.B. this only exists so we can conditionally call useContext on it when we are not interested in the nearest match\nexport const dummyMatchContext = React.createContext<string | undefined>(\n  undefined,\n)\n"], "names": ["React"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEa,MAAA,eAAeA,iBAAM,cAAkC,MAAS;AAGtE,MAAM,oBAAoBA,iBAAM;AAAA,EACrC;AACF;;;"}