{"version": 3, "file": "store.cjs", "sources": ["../../src/store.ts"], "sourcesContent": ["import { __flush } from './scheduler'\nimport { isUpdaterFunction } from './types'\nimport type { AnyUpdater, Listener, Updater } from './types'\n\nexport interface StoreOptions<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  /**\n   * Replace the default update function with a custom one.\n   */\n  updateFn?: (previous: TState) => (updater: TUpdater) => TState\n  /**\n   * Called when a listener subscribes to the store.\n   *\n   * @return a function to unsubscribe the listener\n   */\n  onSubscribe?: (\n    listener: Listener<TState>,\n    store: Store<TState, TUpdater>,\n  ) => () => void\n  /**\n   * Called after the state has been updated, used to derive other state.\n   */\n  onUpdate?: () => void\n}\n\nexport class Store<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  listeners = new Set<Listener<TState>>()\n  state: TState\n  prevState: TState\n  options?: StoreOptions<TState, TUpdater>\n\n  constructor(initialState: TState, options?: StoreOptions<TState, TUpdater>) {\n    this.prevState = initialState\n    this.state = initialState\n    this.options = options\n  }\n\n  subscribe = (listener: Listener<TState>) => {\n    this.listeners.add(listener)\n    const unsub = this.options?.onSubscribe?.(listener, this)\n    return () => {\n      this.listeners.delete(listener)\n      unsub?.()\n    }\n  }\n\n  /**\n   * Update the store state safely with improved type checking\n   */\n  setState(updater: (prevState: TState) => TState): void\n  setState(updater: TState): void\n  setState(updater: TUpdater): void\n  setState(updater: Updater<TState> | TUpdater): void {\n    this.prevState = this.state\n\n    if (this.options?.updateFn) {\n      this.state = this.options.updateFn(this.prevState)(updater as TUpdater)\n    } else {\n      if (isUpdaterFunction(updater)) {\n        this.state = updater(this.prevState)\n      } else {\n        this.state = updater as TState\n      }\n    }\n\n    // Always run onUpdate, regardless of batching\n    this.options?.onUpdate?.()\n\n    // Attempt to flush\n    __flush(this as never)\n  }\n}\n"], "names": ["isUpdaterFunction", "__flush"], "mappings": ";;;;AA2BO,MAAM,MAGX;AAAA,EAMA,YAAY,cAAsB,SAA0C;AAL5E,SAAA,gCAAgB,IAAsB;AAWtC,SAAA,YAAY,CAAC,aAA+B;;AACrC,WAAA,UAAU,IAAI,QAAQ;AAC3B,YAAM,SAAQ,gBAAK,YAAL,mBAAc,gBAAd,4BAA4B,UAAU;AACpD,aAAO,MAAM;AACN,aAAA,UAAU,OAAO,QAAQ;AACtB;AAAA,MACV;AAAA,IACF;AAZE,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,EAAA;AAAA,EAkBjB,SAAS,SAA2C;;AAClD,SAAK,YAAY,KAAK;AAElB,SAAA,UAAK,YAAL,mBAAc,UAAU;AAC1B,WAAK,QAAQ,KAAK,QAAQ,SAAS,KAAK,SAAS,EAAE,OAAmB;AAAA,IAAA,OACjE;AACD,UAAAA,MAAAA,kBAAkB,OAAO,GAAG;AACzB,aAAA,QAAQ,QAAQ,KAAK,SAAS;AAAA,MAAA,OAC9B;AACL,aAAK,QAAQ;AAAA,MAAA;AAAA,IACf;AAIF,qBAAK,YAAL,mBAAc,aAAd;AAGAC,cAAAA,QAAQ,IAAa;AAAA,EAAA;AAEzB;;"}