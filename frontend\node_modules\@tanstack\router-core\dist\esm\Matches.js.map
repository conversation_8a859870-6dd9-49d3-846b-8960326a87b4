{"version": 3, "file": "Matches.js", "sources": ["../../src/Matches.ts"], "sourcesContent": ["import type { <PERSON><PERSON>out<PERSON>, StaticDataRouteOption } from './route'\nimport type {\n  AllContext,\n  AllLoaderData,\n  AllParams,\n  FullSearchSchema,\n  ParseRoute,\n  RouteById,\n  RouteIds,\n} from './routeInfo'\nimport type { AnyRouter, RegisteredRouter } from './router'\nimport type { Constrain, ControlledPromise } from './utils'\n\nexport type AnyMatchAndValue = { match: any; value: any }\n\nexport type FindValueByIndex<\n  TKey,\n  TValue extends ReadonlyArray<any>,\n> = T<PERSON><PERSON> extends `${infer TIndex extends number}` ? TValue[TIndex] : never\n\nexport type FindValueByKey<TKey, TValue> =\n  TValue extends ReadonlyArray<any>\n    ? FindValueByIndex<TKey, TValue>\n    : TValue[TKey & keyof TValue]\n\nexport type CreateMatchAndValue<TMatch, TValue> = TValue extends any\n  ? {\n      match: TMatch\n      value: TValue\n    }\n  : never\n\nexport type NextMatchAndValue<\n  TK<PERSON>,\n  TMatchAndValue extends AnyMatchAndValue,\n> = TMatchAndValue extends any\n  ? CreateMatchAndValue<\n      TMatchAndValue['match'],\n      FindValueByKey<TKey, TMatchAndValue['value']>\n    >\n  : never\n\nexport type IsMatchKeyOf<TValue> =\n  TValue extends ReadonlyArray<any>\n    ? number extends TValue['length']\n      ? `${number}`\n      : keyof TValue & `${number}`\n    : TValue extends object\n      ? keyof TValue & string\n      : never\n\nexport type IsMatchPath<\n  TParentPath extends string,\n  TMatchAndValue extends AnyMatchAndValue,\n> = `${TParentPath}${IsMatchKeyOf<TMatchAndValue['value']>}`\n\nexport type IsMatchResult<\n  TKey,\n  TMatchAndValue extends AnyMatchAndValue,\n> = TMatchAndValue extends any\n  ? TKey extends keyof TMatchAndValue['value']\n    ? TMatchAndValue['match']\n    : never\n  : never\n\nexport type IsMatchParse<\n  TPath,\n  TMatchAndValue extends AnyMatchAndValue,\n  TParentPath extends string = '',\n> = TPath extends `${string}.${string}`\n  ? TPath extends `${infer TFirst}.${infer TRest}`\n    ? IsMatchParse<\n        TRest,\n        NextMatchAndValue<TFirst, TMatchAndValue>,\n        `${TParentPath}${TFirst}.`\n      >\n    : never\n  : {\n      path: IsMatchPath<TParentPath, TMatchAndValue>\n      result: IsMatchResult<TPath, TMatchAndValue>\n    }\n\nexport type IsMatch<TMatch, TPath> = IsMatchParse<\n  TPath,\n  TMatch extends any ? { match: TMatch; value: TMatch } : never\n>\n\n/**\n * Narrows matches based on a path\n * @experimental\n */\nexport const isMatch = <TMatch, TPath extends string>(\n  match: TMatch,\n  path: Constrain<TPath, IsMatch<TMatch, TPath>['path']>,\n): match is IsMatch<TMatch, TPath>['result'] => {\n  const parts = (path as string).split('.')\n  let part\n  let value: any = match\n\n  while ((part = parts.shift()) != null && value != null) {\n    value = value[part]\n  }\n\n  return value != null\n}\n\nexport interface DefaultRouteMatchExtensions {\n  scripts?: unknown\n  links?: unknown\n  headScripts?: unknown\n  meta?: unknown\n}\n\nexport interface RouteMatchExtensions extends DefaultRouteMatchExtensions {}\n\nexport interface RouteMatch<\n  out TRouteId,\n  out TFullPath,\n  out TAllParams,\n  out TFullSearchSchema,\n  out TLoaderData,\n  out TAllContext,\n  out TLoaderDeps,\n> extends RouteMatchExtensions {\n  id: string\n  routeId: TRouteId\n  fullPath: TFullPath\n  index: number\n  pathname: string\n  params: TAllParams\n  _strictParams: TAllParams\n  status: 'pending' | 'success' | 'error' | 'redirected' | 'notFound'\n  isFetching: false | 'beforeLoad' | 'loader'\n  error: unknown\n  paramsError: unknown\n  searchError: unknown\n  updatedAt: number\n  loadPromise?: ControlledPromise<void>\n  beforeLoadPromise?: ControlledPromise<void>\n  loaderPromise?: ControlledPromise<void>\n  loaderData?: TLoaderData\n  __routeContext: Record<string, unknown>\n  __beforeLoadContext: Record<string, unknown>\n  context: TAllContext\n  search: TFullSearchSchema\n  _strictSearch: TFullSearchSchema\n  fetchCount: number\n  abortController: AbortController\n  cause: 'preload' | 'enter' | 'stay'\n  loaderDeps: TLoaderDeps\n  preload: boolean\n  invalid: boolean\n  headers?: Record<string, string>\n  globalNotFound?: boolean\n  staticData: StaticDataRouteOption\n  minPendingPromise?: ControlledPromise<void>\n  pendingTimeout?: ReturnType<typeof setTimeout>\n}\n\nexport type MakeRouteMatchFromRoute<TRoute extends AnyRoute> = RouteMatch<\n  TRoute['types']['id'],\n  TRoute['types']['fullPath'],\n  TRoute['types']['allParams'],\n  TRoute['types']['fullSearchSchema'],\n  TRoute['types']['loaderData'],\n  TRoute['types']['allContext'],\n  TRoute['types']['loaderDeps']\n>\n\nexport type MakeRouteMatch<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TRouteId = RouteIds<TRouteTree>,\n  TStrict extends boolean = true,\n> = RouteMatch<\n  TRouteId,\n  RouteById<TRouteTree, TRouteId>['types']['fullPath'],\n  TStrict extends false\n    ? AllParams<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['allParams'],\n  TStrict extends false\n    ? FullSearchSchema<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['fullSearchSchema'],\n  TStrict extends false\n    ? AllLoaderData<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['loaderData'],\n  TStrict extends false\n    ? AllContext<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['allContext'],\n  RouteById<TRouteTree, TRouteId>['types']['loaderDeps']\n>\n\nexport type AnyRouteMatch = RouteMatch<any, any, any, any, any, any, any>\n\nexport type MakeRouteMatchUnion<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TRoute extends AnyRoute = ParseRoute<TRouter['routeTree']>,\n> = TRoute extends any\n  ? RouteMatch<\n      TRoute['id'],\n      TRoute['fullPath'],\n      TRoute['types']['allParams'],\n      TRoute['types']['fullSearchSchema'],\n      TRoute['types']['loaderData'],\n      TRoute['types']['allContext'],\n      TRoute['types']['loaderDeps']\n    >\n  : never\n\n/**\n * The `MatchRouteOptions` type is used to describe the options that can be used when matching a route.\n *\n * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#matchrouteoptions-type)\n */\nexport interface MatchRouteOptions {\n  /**\n   * If `true`, will match against pending location instead of the current location.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#pending-property)\n   */\n  pending?: boolean\n  /**\n   * If `true`, will match against the current location with case sensitivity.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#casesensitive-property)\n   */\n  caseSensitive?: boolean\n  /**\n   * If `true`, will match against the current location's search params using a deep inclusive check. e.g. `{ a: 1 }` will match for a current location of `{ a: 1, b: 2 }`.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#includesearch-property)\n   */\n  includeSearch?: boolean\n  /**\n   * If `true`, will match against the current location using a fuzzy match. e.g. `/posts` will match for a current location of `/posts/123`.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#fuzzy-property)\n   */\n  fuzzy?: boolean\n}\n"], "names": [], "mappings": "AA2Fa,MAAA,UAAU,CACrB,OACA,SAC8C;AACxC,QAAA,QAAS,KAAgB,MAAM,GAAG;AACpC,MAAA;AACJ,MAAI,QAAa;AAEjB,UAAQ,OAAO,MAAM,MAAY,MAAA,QAAQ,SAAS,MAAM;AACtD,YAAQ,MAAM,IAAI;AAAA,EAAA;AAGpB,SAAO,SAAS;AAClB;"}