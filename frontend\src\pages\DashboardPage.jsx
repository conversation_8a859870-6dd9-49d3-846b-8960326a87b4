import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getUserUrls, deleteUrl } from "../api/urlShortener";
import { getErrorMessage } from "../utils/errorHandler";

const DashboardPage = ({ user }) => {
  const [copied, setCopied] = useState("");
  const [showMockData, setShowMockData] = useState(false);
  const queryClient = useQueryClient();

  // Mock data for testing
  const mockUrls = [
    {
      id: "1",
      originalUrl:
        "https://www.example.com/very-long-url-that-needs-shortening",
      shortUrl: "http://localhost:5000/abc123",
      createdAt: new Date().toISOString(),
      clicks: 15,
    },
    {
      id: "2",
      originalUrl: "https://github.com/user/repository",
      shortUrl: "http://localhost:5000/def456",
      createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      clicks: 8,
    },
    {
      id: "3",
      originalUrl: "https://docs.google.com/document/d/1234567890",
      shortUrl: "http://localhost:5000/ghi789",
      createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      clicks: 3,
    },
  ];

  // Fetch user's URLs
  const {
    data: urls = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["userUrls"],
    queryFn: async () => {
      console.log("Fetching user URLs...");
      try {
        const result = await getUserUrls();
        console.log("URLs fetched:", result);
        return result;
      } catch (err) {
        console.error("Error fetching URLs:", err);
        throw err;
      }
    },
    enabled: !!user, // Only fetch if user is logged in
    retry: 1, // Only retry once
    staleTime: 0, // Always fetch fresh data
  });

  // Delete URL mutation
  const deleteMutation = useMutation({
    mutationFn: deleteUrl,
    onSuccess: () => {
      queryClient.invalidateQueries(["userUrls"]);
    },
    onError: (error) => {
      console.error("Error deleting URL:", getErrorMessage(error));
    },
  });

  const handleCopy = async (url) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(url);
      setTimeout(() => setCopied(""), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  const handleDelete = (urlId) => {
    if (showMockData) {
      alert(
        "This is demo data - delete functionality is not available in demo mode."
      );
      return;
    }
    if (window.confirm("Are you sure you want to delete this URL?")) {
      deleteMutation.mutate(urlId);
    }
  };

  // Debug logging
  console.log("Dashboard state:", {
    showMockData,
    isLoading,
    error: error?.message,
    urlsLength: urls.length,
    mockUrlsLength: mockUrls.length,
    user: user?.name || user?.user?.name,
  });

  return (
    <div className="min-h-screen">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Your Dashboard
          </h2>
          <p className="text-xl text-gray-600">Manage your shortened URLs</p>
          {user && (
            <p className="text-sm text-gray-500 mt-2">
              Welcome back, {user.user?.name || user.name || "User"}!
            </p>
          )}

          {/* Debug info */}
          <div className="mt-4 text-xs text-gray-400">
            Demo Mode: {showMockData ? "ON" : "OFF"} | Loading:{" "}
            {isLoading ? "YES" : "NO"} | Error: {error ? "YES" : "NO"}
          </div>
        </div>

        {/* URLs List */}
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-2xl font-bold text-gray-900">
              Your Shortened URLs
            </h3>
            <div className="flex space-x-2">
              {showMockData && (
                <button
                  onClick={() => setShowMockData(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  Try Real Data
                </button>
              )}
              <button
                onClick={() => queryClient.invalidateQueries(["userUrls"])}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Refresh
              </button>
            </div>
          </div>

          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading your URLs...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-yellow-800 mb-2">
                  Unable to Load URL History
                </h4>
                <p className="text-yellow-700 mb-4">
                  Error: {getErrorMessage(error)}
                </p>
                <p className="text-sm text-yellow-600 mb-4">
                  This might be because the backend doesn't have a URL history
                  endpoint yet.
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    <strong>For now, you can:</strong>
                  </p>
                  <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                    <li>Go to Home page to create new short URLs</li>
                    <li>URLs will be created but history won't be saved yet</li>
                    <li>Backend needs to implement user URL storage</li>
                  </ul>
                </div>
                <button
                  onClick={() => {
                    console.log("Show Demo Data button clicked!");
                    setShowMockData(true);
                  }}
                  className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 cursor-pointer"
                  type="button"
                >
                  🎯 Show Demo Data
                </button>
              </div>
            </div>
          ) : (showMockData ? mockUrls : urls).length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600">
                You haven't created any short URLs yet.
              </p>
              <button
                onClick={() => {
                  console.log("Show Demo Data button clicked (empty state)!");
                  setShowMockData(true);
                }}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 cursor-pointer"
                type="button"
              >
                🎯 Show Demo Data
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {showMockData && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-blue-800 text-sm">
                    📝 <strong>Demo Mode:</strong> Showing sample data since the
                    backend URL history endpoint isn't available yet.
                  </p>
                </div>
              )}
              {(showMockData ? mockUrls : urls).map((urlData) => (
                <div
                  key={urlData.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {urlData.originalUrl}
                      </p>
                      <p className="text-sm text-blue-600 mt-1">
                        {urlData.shortUrl}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Created:{" "}
                        {new Date(urlData.createdAt).toLocaleDateString()}
                        {urlData.clicks !== undefined && (
                          <span className="ml-4">Clicks: {urlData.clicks}</span>
                        )}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleCopy(urlData.shortUrl)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                      >
                        {copied === urlData.shortUrl ? "Copied!" : "Copy"}
                      </button>
                      <button
                        onClick={() => handleDelete(urlData.id)}
                        disabled={deleteMutation.isPending}
                        className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm transition-colors"
                      >
                        {deleteMutation.isPending ? "..." : "Delete"}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
