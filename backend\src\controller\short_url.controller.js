import { createShortUrlServicesWithoutUser } from "../services/short_url.services.js";
import { generateNanoId } from "../utils/helper.js";
import { findUrlFromShortUrl } from "../dao/short_url.js";
import { BadRequestError } from "../utils/errorHandler.js";

export const createShortUrl = async (req, res, next) => {
  try {
    const { url } = req.body;

    // Validate URL
    if (!url) {
      throw new BadRequestError("URL is required");
    }

    console.log("Creating short URL for:", url);
if(req.user){
    const shortUrl = await createShortUrlServicesWithoutUser(url);
}else{
    const shortUrl = await createShortUrlServicesWithoutUser(url);
   
}
    res.status(200).json({
      success: true,
      data: process.env.APP_URL + shortUrl,
      message: "Short URL created successfully",
    });
  } catch (error) {
    next(error);
  }
};

/* export const createShortUrlAuth = async (req, res, next) => {

    const { url } = req.body;

    // Validate URL
  

    const shortUrl = await createShortUrlServicesWithoutUser(url);
    const fullShortUrl = process.env.APP_URL + shortUrl;

    res.status(200).json({
      success: true,
      data: fullShortUrl,
      message: "Short URL created successfully",
    });
  }; */
  


export const redirectFromShortUrl = async (req, res, next) => {
  try {
    const { id } = req.params;
    const url = await findUrlFromShortUrl(id);

    if (!url) {
      return res.status(404).json({
        success: false,
        message: "Short URL not found",
      });
    }

    res.redirect(url.full_url);
  } catch (err) {
    console.log(err);
    next(err);
  }
};

