import jwt from "jsonwebtoken";
import User from "../models/user.model.js";
import { findUserByEmail, createUser } from "../dao/user.dao.js";
import { ConflictError } from "../utils/errorHandler.js";
export const registerUser = async (name, email, password) => {
  const user = await findUserByEmail(email); // Pass email string, not object
  if (user) {
    throw new ConflictError("User already exists");
  }

  const newUser = await createUser(name, email, password);

  // Fix: signToken is not defined, import it
  // const token = signToken(newUser._id) // Use the helper function
  return newUser;
};
