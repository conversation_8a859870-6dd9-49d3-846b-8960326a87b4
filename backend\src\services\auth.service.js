import jwt from "jsonwebtoken";
import User from "../models/user.model.js";
import { findUserByEmail, createUser } from "../dao/user.dao.js";
import { ConflictError } from "../utils/errorHandler.js";
import { signToken } from "../utils/helper.js";
export const registerUser = async (name, email, password) => {
  const user = await findUserByEmail(email); // Pass email string, not object
  if (user) {
    throw new ConflictError("User already exists");
  }

  const newUser = await createUser(name, email, password);

  // Fix: signToken is not defined, import it
 const token = signToken(newUser._id) // Use the helper function
  return token;
};

export const loginUser = async (email, password) => {
  const user = await findUserByEmail(email);
  if (!user) {
    throw new Error("User not found");
  }
  if (user.password !== password) {
    throw new Error("Invalid password");
  }
  const token = signToken(user._id);
  return {token,user};
};
