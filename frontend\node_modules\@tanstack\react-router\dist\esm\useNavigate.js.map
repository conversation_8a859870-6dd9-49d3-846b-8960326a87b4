{"version": 3, "file": "useNavigate.js", "sources": ["../../src/useNavigate.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { useRouter } from './useRouter'\nimport type {\n  AnyRouter,\n  FromPathOption,\n  NavigateOptions,\n  RegisteredRouter,\n  UseNavigateResult,\n} from '@tanstack/router-core'\n\nexport function useNavigate<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDefaultFrom extends string = string,\n>(_defaultOpts?: {\n  from?: FromPathOption<TRouter, TDefaultFrom>\n}): UseNavigateResult<TDefaultFrom> {\n  const { navigate } = useRouter()\n\n  return React.useCallback(\n    (options: NavigateOptions) => {\n      return navigate({\n        from: _defaultOpts?.from,\n        ...options,\n      })\n    },\n    [_defaultOpts?.from, navigate],\n  ) as UseNavigateResult<TDefaultFrom>\n}\n\nexport function Navigate<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(props: NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): null {\n  const router = useRouter()\n\n  const previousPropsRef = React.useRef<NavigateOptions<\n    TRouter,\n    TFrom,\n    TTo,\n    TMaskFrom,\n    TMaskTo\n  > | null>(null)\n  React.useEffect(() => {\n    if (previousPropsRef.current !== props) {\n      router.navigate({\n        ...props,\n      })\n      previousPropsRef.current = props\n    }\n  }, [router, props])\n  return null\n}\n"], "names": [], "mappings": ";;AAUO,SAAS,YAGd,cAEkC;AAC5B,QAAA,EAAE,SAAS,IAAI,UAAU;AAE/B,SAAO,MAAM;AAAA,IACX,CAAC,YAA6B;AAC5B,aAAO,SAAS;AAAA,QACd,MAAM,6CAAc;AAAA,QACpB,GAAG;AAAA,MAAA,CACJ;AAAA,IACH;AAAA,IACA,CAAC,6CAAc,MAAM,QAAQ;AAAA,EAC/B;AACF;AAEO,SAAS,SAMd,OAAuE;AACvE,QAAM,SAAS,UAAU;AAEnB,QAAA,mBAAmB,MAAM,OAMrB,IAAI;AACd,QAAM,UAAU,MAAM;AAChB,QAAA,iBAAiB,YAAY,OAAO;AACtC,aAAO,SAAS;AAAA,QACd,GAAG;AAAA,MAAA,CACJ;AACD,uBAAiB,UAAU;AAAA,IAAA;AAAA,EAC7B,GACC,CAAC,QAAQ,KAAK,CAAC;AACX,SAAA;AACT;"}