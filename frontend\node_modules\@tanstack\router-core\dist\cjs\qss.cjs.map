{"version": 3, "file": "qss.cjs", "sources": ["../../src/qss.ts"], "sourcesContent": ["/**\n * Program is a reimplementation of the `qss` package:\n * Copyright (c) <PERSON> <EMAIL>, MIT License\n * https://github.com/lukeed/qss/blob/master/license.md\n *\n * This reimplementation uses modern browser APIs\n * (namely URLSearchParams) and TypeScript while still\n * maintaining the original functionality and interface.\n */\n\n/**\n * Encodes an object into a query string.\n * @param obj - The object to encode into a query string.\n * @param [pfx] - An optional prefix to add before the query string.\n * @returns The encoded query string.\n * @example\n * ```\n * // Example input: encode({ token: 'foo', key: 'value' })\n * // Expected output: \"token=foo&key=value\"\n * ```\n */\nexport function encode(obj: any, pfx?: string) {\n  const normalizedObject = Object.entries(obj).flatMap(([key, value]) => {\n    if (Array.isArray(value)) {\n      return value.map((v) => [key, String(v)])\n    } else {\n      return [[key, String(value)]]\n    }\n  })\n\n  const searchParams = new URLSearchParams(normalizedObject)\n\n  return (pfx || '') + searchParams.toString()\n}\n\n/**\n * Converts a string value to its appropriate type (string, number, boolean).\n * @param mix - The string value to convert.\n * @returns The converted value.\n * @example\n * // Example input: toValue(\"123\")\n * // Expected output: 123\n */\nfunction toValue(str: unknown) {\n  if (!str) return ''\n\n  if (str === 'false') return false\n  if (str === 'true') return true\n  return +str * 0 === 0 && +str + '' === str ? +str : str\n}\n\n/**\n * Decodes a query string into an object.\n * @param str - The query string to decode.\n * @param [pfx] - An optional prefix to filter out from the query string.\n * @returns The decoded key-value pairs in an object format.\n * @example\n * // Example input: decode(\"token=foo&key=value\")\n * // Expected output: { \"token\": \"foo\", \"key\": \"value\" }\n */\nexport function decode(str: any, pfx?: string): any {\n  const searchParamsPart = pfx ? str.slice(pfx.length) : str\n  const searchParams = new URLSearchParams(searchParamsPart)\n\n  const entries = [...searchParams.entries()]\n\n  return entries.reduce<Record<string, unknown>>((acc, [key, value]) => {\n    const previousValue = acc[key]\n    if (previousValue == null) {\n      acc[key] = toValue(value)\n    } else {\n      acc[key] = Array.isArray(previousValue)\n        ? [...previousValue, toValue(value)]\n        : [previousValue, toValue(value)]\n    }\n\n    return acc\n  }, {})\n}\n"], "names": [], "mappings": ";;AAqBgB,SAAA,OAAO,KAAU,KAAc;AACvC,QAAA,mBAAmB,OAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACjE,QAAA,MAAM,QAAQ,KAAK,GAAG;AACjB,aAAA,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,IAAA,OACnC;AACL,aAAO,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,IAAA;AAAA,EAC9B,CACD;AAEK,QAAA,eAAe,IAAI,gBAAgB,gBAAgB;AAEjD,UAAA,OAAO,MAAM,aAAa,SAAS;AAC7C;AAUA,SAAS,QAAQ,KAAc;AACzB,MAAA,CAAC,IAAY,QAAA;AAEb,MAAA,QAAQ,QAAgB,QAAA;AACxB,MAAA,QAAQ,OAAe,QAAA;AACpB,SAAA,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM;AACtD;AAWgB,SAAA,OAAO,KAAU,KAAmB;AAClD,QAAM,mBAAmB,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AACjD,QAAA,eAAe,IAAI,gBAAgB,gBAAgB;AAEzD,QAAM,UAAU,CAAC,GAAG,aAAa,SAAS;AAE1C,SAAO,QAAQ,OAAgC,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC9D,UAAA,gBAAgB,IAAI,GAAG;AAC7B,QAAI,iBAAiB,MAAM;AACrB,UAAA,GAAG,IAAI,QAAQ,KAAK;AAAA,IAAA,OACnB;AACL,UAAI,GAAG,IAAI,MAAM,QAAQ,aAAa,IAClC,CAAC,GAAG,eAAe,QAAQ,KAAK,CAAC,IACjC,CAAC,eAAe,QAAQ,KAAK,CAAC;AAAA,IAAA;AAG7B,WAAA;AAAA,EACT,GAAG,EAAE;AACP;;;"}