{"version": 3, "file": "useMatch.cjs", "sources": ["../../src/useMatch.tsx"], "sourcesContent": ["import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport { useRouterState } from './useRouterState'\nimport { dummyMatchContext, matchContext } from './matchContext'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyR<PERSON>er,\n  MakeRouteMatch,\n  MakeRouteMatchUnion,\n  RegisteredRouter,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n} from '@tanstack/router-core'\n\nexport interface UseMatchBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing extends boolean,\n> {\n  select?: (\n    match: MakeRouteMatch<TRouter['routeTree'], TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseMatchRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchBaseOptions<\n    TRouter,\n    TFrom,\n    true,\n    true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseMatchResult<TRouter, TFrom, true, TSelected>\n\nexport type UseMatchOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing extends boolean,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseMatchBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseMatchResult<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TSelected,\n> = unknown extends TSelected\n  ? TStrict extends true\n    ? MakeRouteMatch<TRouter['routeTree'], TFrom, TStrict>\n    : MakeRouteMatchUnion<TRouter>\n  : TSelected\n\nexport function useMatch<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseMatchOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<UseMatchResult<TRouter, TFrom, TStrict, TSelected>, TThrow> {\n  const nearestMatchId = React.useContext(\n    opts.from ? dummyMatchContext : matchContext,\n  )\n\n  const matchSelection = useRouterState({\n    select: (state: any) => {\n      const match = state.matches.find((d: any) =>\n        opts.from ? opts.from === d.routeId : d.id === nearestMatchId,\n      )\n      invariant(\n        !((opts.shouldThrow ?? true) && !match),\n        `Could not find ${opts.from ? `an active match from \"${opts.from}\"` : 'a nearest match!'}`,\n      )\n\n      if (match === undefined) {\n        return undefined\n      }\n\n      return opts.select ? opts.select(match) : match\n    },\n    structuralSharing: opts.structuralSharing,\n  } as any)\n\n  return matchSelection as any\n}\n"], "names": ["React", "dummyMatchContext", "matchContext", "useRouterState"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA6EO,SAAS,SAQd,MAQ6E;AAC7E,QAAM,iBAAiBA,iBAAM;AAAA,IAC3B,KAAK,OAAOC,iCAAoBC,aAAAA;AAAAA,EAClC;AAEA,QAAM,iBAAiBC,eAAAA,eAAe;AAAA,IACpC,QAAQ,CAAC,UAAe;AAChB,YAAA,QAAQ,MAAM,QAAQ;AAAA,QAAK,CAAC,MAChC,KAAK,OAAO,KAAK,SAAS,EAAE,UAAU,EAAE,OAAO;AAAA,MACjD;AACA;AAAA,QACE,GAAG,KAAK,eAAe,SAAS,CAAC;AAAA,QACjC,kBAAkB,KAAK,OAAO,yBAAyB,KAAK,IAAI,MAAM,kBAAkB;AAAA,MAC1F;AAEA,UAAI,UAAU,QAAW;AAChB,eAAA;AAAA,MAAA;AAGT,aAAO,KAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,IAC5C;AAAA,IACA,mBAAmB,KAAK;AAAA,EAAA,CAClB;AAED,SAAA;AACT;;"}