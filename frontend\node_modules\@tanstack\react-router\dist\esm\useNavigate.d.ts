import { AnyRouter, FromPathOption, NavigateOptions, RegisteredRouter, UseNavigateResult } from '@tanstack/router-core';
export declare function useNavigate<TRouter extends AnyRouter = RegisteredRouter, TDefaultFrom extends string = string>(_defaultOpts?: {
    from?: FromPathOption<TRouter, TDefaultFrom>;
}): UseNavigateResult<TDefaultFrom>;
export declare function Navigate<TRouter extends AnyRouter = RegisteredRouter, const TFrom extends string = string, const TTo extends string | undefined = undefined, const TMaskFrom extends string = TFrom, const TMaskTo extends string = ''>(props: NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): null;
