{"version": 3, "file": "utils.cjs", "sources": ["../../src/utils.ts"], "sourcesContent": ["import type { RouteIds } from './routeInfo'\nimport type { AnyRouter } from './router'\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\nexport type IsAny<TValue, TYesResult, TNoResult = TValue> = 1 extends 0 & TValue\n  ? TYesResult\n  : TNoResult\n\nexport type PickAsRequired<TValue, <PERSON><PERSON><PERSON> extends keyof TValue> = Omit<\n  TValue,\n  TKey\n> &\n  Required<Pick<TValue, TKey>>\n\nexport type PickRequired<T> = {\n  [K in keyof T as undefined extends T[K] ? never : K]: T[K]\n}\n\nexport type PickOptional<T> = {\n  [K in keyof T as undefined extends T[K] ? K : never]: T[K]\n}\n\n// from https://stackoverflow.com/a/76458160\nexport type WithoutEmpty<T> = T extends any ? ({} extends T ? never : T) : never\n\nexport type Expand<T> = T extends object\n  ? T extends infer O\n    ? O extends Function\n      ? O\n      : { [K in keyof O]: O[K] }\n    : never\n  : T\n\nexport type DeepPartial<T> = T extends object\n  ? {\n      [P in keyof T]?: DeepPartial<T[P]>\n    }\n  : T\n\nexport type MakeDifferenceOptional<TLeft, TRight> = keyof TLeft &\n  keyof TRight extends never\n  ? TRight\n  : Omit<TRight, keyof TLeft & keyof TRight> & {\n      [K in keyof TLeft & keyof TRight]?: TRight[K]\n    }\n\n// from https://stackoverflow.com/a/53955431\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport type IsUnion<T, U extends T = T> = (\n  T extends any ? (U extends T ? false : true) : never\n) extends false\n  ? false\n  : true\n\nexport type IsNonEmptyObject<T> = T extends object\n  ? keyof T extends never\n    ? false\n    : true\n  : false\n\nexport type Assign<TLeft, TRight> = TLeft extends any\n  ? TRight extends any\n    ? IsNonEmptyObject<TLeft> extends false\n      ? TRight\n      : IsNonEmptyObject<TRight> extends false\n        ? TLeft\n        : keyof TLeft & keyof TRight extends never\n          ? TLeft & TRight\n          : Omit<TLeft, keyof TRight> & TRight\n    : never\n  : never\n\nexport type IntersectAssign<TLeft, TRight> = TLeft extends any\n  ? TRight extends any\n    ? IsNonEmptyObject<TLeft> extends false\n      ? TRight\n      : IsNonEmptyObject<TRight> extends false\n        ? TLeft\n        : TRight & TLeft\n    : never\n  : never\n\nexport type Timeout = ReturnType<typeof setTimeout>\n\nexport type Updater<TPrevious, TResult = TPrevious> =\n  | TResult\n  | ((prev?: TPrevious) => TResult)\n\nexport type NonNullableUpdater<TPrevious, TResult = TPrevious> =\n  | TResult\n  | ((prev: TPrevious) => TResult)\n\nexport type ExtractObjects<TUnion> = TUnion extends MergeAllPrimitive\n  ? never\n  : TUnion\n\nexport type PartialMergeAllObject<TUnion> =\n  ExtractObjects<TUnion> extends infer TObj\n    ? [TObj] extends [never]\n      ? never\n      : {\n          [TKey in TObj extends any ? keyof TObj : never]?: TObj extends any\n            ? TKey extends keyof TObj\n              ? TObj[TKey]\n              : never\n            : never\n        }\n    : never\n\nexport type MergeAllPrimitive =\n  | ReadonlyArray<any>\n  | number\n  | string\n  | bigint\n  | boolean\n  | symbol\n  | undefined\n  | null\n\nexport type ExtractPrimitives<TUnion> = TUnion extends MergeAllPrimitive\n  ? TUnion\n  : TUnion extends object\n    ? never\n    : TUnion\n\nexport type PartialMergeAll<TUnion> =\n  | ExtractPrimitives<TUnion>\n  | PartialMergeAllObject<TUnion>\n\nexport type Constrain<T, TConstraint, TDefault = TConstraint> =\n  | (T extends TConstraint ? T : never)\n  | TDefault\n\nexport type ConstrainLiteral<T, TConstraint, TDefault = TConstraint> =\n  | (T & TConstraint)\n  | TDefault\n\n/**\n * To be added to router types\n */\nexport type UnionToIntersection<T> = (\n  T extends any ? (arg: T) => any : never\n) extends (arg: infer T) => any\n  ? T\n  : never\n\n/**\n * Merges everything in a union into one object.\n * This mapped type is homomorphic which means it preserves stuff! :)\n */\nexport type MergeAllObjects<\n  TUnion,\n  TIntersected = UnionToIntersection<ExtractObjects<TUnion>>,\n> = [keyof TIntersected] extends [never]\n  ? never\n  : {\n      [TKey in keyof TIntersected]: TUnion extends any\n        ? TUnion[TKey & keyof TUnion]\n        : never\n    }\n\nexport type MergeAll<TUnion> =\n  | MergeAllObjects<TUnion>\n  | ExtractPrimitives<TUnion>\n\nexport type ValidateJSON<T> = ((...args: Array<any>) => any) extends T\n  ? unknown extends T\n    ? never\n    : 'Function is not serializable'\n  : { [K in keyof T]: ValidateJSON<T[K]> }\n\nexport function last<T>(arr: Array<T>) {\n  return arr[arr.length - 1]\n}\n\nfunction isFunction(d: any): d is Function {\n  return typeof d === 'function'\n}\n\nexport function functionalUpdate<TPrevious, TResult = TPrevious>(\n  updater: Updater<TPrevious, TResult> | NonNullableUpdater<TPrevious, TResult>,\n  previous: TPrevious,\n): TResult {\n  if (isFunction(updater)) {\n    return updater(previous)\n  }\n\n  return updater\n}\n\nexport function pick<TValue, TKey extends keyof TValue>(\n  parent: TValue,\n  keys: Array<TKey>,\n): Pick<TValue, TKey> {\n  return keys.reduce((obj: any, key: TKey) => {\n    obj[key] = parent[key]\n    return obj\n  }, {} as any)\n}\n\n/**\n * This function returns `prev` if `_next` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between immutable JSON values for example.\n * Do not use this with signals\n */\nexport function replaceEqualDeep<T>(prev: any, _next: T): T {\n  if (prev === _next) {\n    return prev\n  }\n\n  const next = _next as any\n\n  const array = isPlainArray(prev) && isPlainArray(next)\n\n  if (array || (isSimplePlainObject(prev) && isSimplePlainObject(next))) {\n    const prevItems = array\n      ? prev\n      : (Object.keys(prev) as Array<unknown>).concat(\n          Object.getOwnPropertySymbols(prev),\n        )\n    const prevSize = prevItems.length\n    const nextItems = array\n      ? next\n      : (Object.keys(next) as Array<unknown>).concat(\n          Object.getOwnPropertySymbols(next),\n        )\n    const nextSize = nextItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < nextSize; i++) {\n      const key = array ? i : (nextItems[i] as any)\n      if (\n        ((!array && prevItems.includes(key)) || array) &&\n        prev[key] === undefined &&\n        next[key] === undefined\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(prev[key], next[key])\n        if (copy[key] === prev[key] && prev[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return prevSize === nextSize && equalItems === prevSize ? prev : copy\n  }\n\n  return next\n}\n\n/**\n * A wrapper around `isPlainObject` with additional checks to ensure that it is not\n * only a plain object, but also one that is \"clone-friendly\" (doesn't have any\n * non-enumerable properties).\n */\nfunction isSimplePlainObject(o: any) {\n  return (\n    // all the checks from isPlainObject are more likely to hit so we perform them first\n    isPlainObject(o) &&\n    Object.getOwnPropertyNames(o).length === Object.keys(o).length\n  )\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any) {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any) {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isPlainArray(value: unknown): value is Array<unknown> {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\nfunction getObjectKeys(obj: any, ignoreUndefined: boolean) {\n  let keys = Object.keys(obj)\n  if (ignoreUndefined) {\n    keys = keys.filter((key) => obj[key] !== undefined)\n  }\n  return keys\n}\n\nexport function deepEqual(\n  a: any,\n  b: any,\n  opts?: { partial?: boolean; ignoreUndefined?: boolean },\n): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (isPlainObject(a) && isPlainObject(b)) {\n    const ignoreUndefined = opts?.ignoreUndefined ?? true\n    const aKeys = getObjectKeys(a, ignoreUndefined)\n    const bKeys = getObjectKeys(b, ignoreUndefined)\n\n    if (!opts?.partial && aKeys.length !== bKeys.length) {\n      return false\n    }\n\n    return bKeys.every((key) => deepEqual(a[key], b[key], opts))\n  }\n\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) {\n      return false\n    }\n    return !a.some((item, index) => !deepEqual(item, b[index], opts))\n  }\n\n  return false\n}\n\nexport type StringLiteral<T> = T extends string\n  ? string extends T\n    ? string\n    : T\n  : never\n\nexport type ThrowOrOptional<T, TThrow extends boolean> = TThrow extends true\n  ? T\n  : T | undefined\n\nexport type StrictOrFrom<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean = true,\n> = TStrict extends false\n  ? {\n      from?: never\n      strict: TStrict\n    }\n  : {\n      from: ConstrainLiteral<TFrom, RouteIds<TRouter['routeTree']>>\n      strict?: TStrict\n    }\n\nexport type ThrowConstraint<\n  TStrict extends boolean,\n  TThrow extends boolean,\n> = TStrict extends false ? (TThrow extends true ? never : TThrow) : TThrow\n\nexport type ControlledPromise<T> = Promise<T> & {\n  resolve: (value: T) => void\n  reject: (value: any) => void\n  status: 'pending' | 'resolved' | 'rejected'\n  value?: T\n}\n\nexport function createControlledPromise<T>(onResolve?: (value: T) => void) {\n  let resolveLoadPromise!: (value: T) => void\n  let rejectLoadPromise!: (value: any) => void\n\n  const controlledPromise = new Promise<T>((resolve, reject) => {\n    resolveLoadPromise = resolve\n    rejectLoadPromise = reject\n  }) as ControlledPromise<T>\n\n  controlledPromise.status = 'pending'\n\n  controlledPromise.resolve = (value: T) => {\n    controlledPromise.status = 'resolved'\n    controlledPromise.value = value\n    resolveLoadPromise(value)\n    onResolve?.(value)\n  }\n\n  controlledPromise.reject = (e) => {\n    controlledPromise.status = 'rejected'\n    rejectLoadPromise(e)\n  }\n\n  return controlledPromise\n}\n\n/**\n *\n * @deprecated use `jsesc` instead\n */\nexport function escapeJSON(jsonString: string) {\n  return jsonString\n    .replace(/\\\\/g, '\\\\\\\\') // Escape backslashes\n    .replace(/'/g, \"\\\\'\") // Escape single quotes\n    .replace(/\"/g, '\\\\\"') // Escape double quotes\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (const item of keysA) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, item) ||\n      !Object.is(objA[item as keyof T], objB[item as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n"], "names": [], "mappings": ";;AA2KO,SAAS,KAAQ,KAAe;AAC9B,SAAA,IAAI,IAAI,SAAS,CAAC;AAC3B;AAEA,SAAS,WAAW,GAAuB;AACzC,SAAO,OAAO,MAAM;AACtB;AAEgB,SAAA,iBACd,SACA,UACS;AACL,MAAA,WAAW,OAAO,GAAG;AACvB,WAAO,QAAQ,QAAQ;AAAA,EAAA;AAGlB,SAAA;AACT;AAEgB,SAAA,KACd,QACA,MACoB;AACpB,SAAO,KAAK,OAAO,CAAC,KAAU,QAAc;AACtC,QAAA,GAAG,IAAI,OAAO,GAAG;AACd,WAAA;AAAA,EACT,GAAG,EAAS;AACd;AAQgB,SAAA,iBAAoB,MAAW,OAAa;AAC1D,MAAI,SAAS,OAAO;AACX,WAAA;AAAA,EAAA;AAGT,QAAM,OAAO;AAEb,QAAM,QAAQ,aAAa,IAAI,KAAK,aAAa,IAAI;AAErD,MAAI,SAAU,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,GAAI;AACrE,UAAM,YAAY,QACd,OACC,OAAO,KAAK,IAAI,EAAqB;AAAA,MACpC,OAAO,sBAAsB,IAAI;AAAA,IACnC;AACJ,UAAM,WAAW,UAAU;AAC3B,UAAM,YAAY,QACd,OACC,OAAO,KAAK,IAAI,EAAqB;AAAA,MACpC,OAAO,sBAAsB,IAAI;AAAA,IACnC;AACJ,UAAM,WAAW,UAAU;AAC3B,UAAM,OAAY,QAAQ,CAAA,IAAK,CAAC;AAEhC,QAAI,aAAa;AAEjB,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,YAAM,MAAM,QAAQ,IAAK,UAAU,CAAC;AACpC,WACI,CAAC,SAAS,UAAU,SAAS,GAAG,KAAM,UACxC,KAAK,GAAG,MAAM,UACd,KAAK,GAAG,MAAM,QACd;AACA,aAAK,GAAG,IAAI;AACZ;AAAA,MAAA,OACK;AACA,aAAA,GAAG,IAAI,iBAAiB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC;AAC7C,YAAA,KAAK,GAAG,MAAM,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,QAAW;AACtD;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAGF,WAAO,aAAa,YAAY,eAAe,WAAW,OAAO;AAAA,EAAA;AAG5D,SAAA;AACT;AAOA,SAAS,oBAAoB,GAAQ;AACnC;AAAA;AAAA,IAEE,cAAc,CAAC,KACf,OAAO,oBAAoB,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE;AAAA;AAE5D;AAGO,SAAS,cAAc,GAAQ;AAChC,MAAA,CAAC,mBAAmB,CAAC,GAAG;AACnB,WAAA;AAAA,EAAA;AAIT,QAAM,OAAO,EAAE;AACX,MAAA,OAAO,SAAS,aAAa;AACxB,WAAA;AAAA,EAAA;AAIT,QAAM,OAAO,KAAK;AACd,MAAA,CAAC,mBAAmB,IAAI,GAAG;AACtB,WAAA;AAAA,EAAA;AAIT,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AAClC,WAAA;AAAA,EAAA;AAIF,SAAA;AACT;AAEA,SAAS,mBAAmB,GAAQ;AAClC,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,aAAa,OAAyC;AAC7D,SAAA,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,OAAO,KAAK,KAAK,EAAE;AACrE;AAEA,SAAS,cAAc,KAAU,iBAA0B;AACrD,MAAA,OAAO,OAAO,KAAK,GAAG;AAC1B,MAAI,iBAAiB;AACnB,WAAO,KAAK,OAAO,CAAC,QAAQ,IAAI,GAAG,MAAM,MAAS;AAAA,EAAA;AAE7C,SAAA;AACT;AAEgB,SAAA,UACd,GACA,GACA,MACS;AACT,MAAI,MAAM,GAAG;AACJ,WAAA;AAAA,EAAA;AAGL,MAAA,OAAO,MAAM,OAAO,GAAG;AAClB,WAAA;AAAA,EAAA;AAGT,MAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AAClC,UAAA,mBAAkB,6BAAM,oBAAmB;AAC3C,UAAA,QAAQ,cAAc,GAAG,eAAe;AACxC,UAAA,QAAQ,cAAc,GAAG,eAAe;AAE9C,QAAI,EAAC,6BAAM,YAAW,MAAM,WAAW,MAAM,QAAQ;AAC5C,aAAA;AAAA,IAAA;AAGT,WAAO,MAAM,MAAM,CAAC,QAAQ,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;AAAA,EAAA;AAG7D,MAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACpC,QAAA,EAAE,WAAW,EAAE,QAAQ;AAClB,aAAA;AAAA,IAAA;AAET,WAAO,CAAC,EAAE,KAAK,CAAC,MAAM,UAAU,CAAC,UAAU,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC;AAAA,EAAA;AAG3D,SAAA;AACT;AAsCO,SAAS,wBAA2B,WAAgC;AACrE,MAAA;AACA,MAAA;AAEJ,QAAM,oBAAoB,IAAI,QAAW,CAAC,SAAS,WAAW;AACvC,yBAAA;AACD,wBAAA;AAAA,EAAA,CACrB;AAED,oBAAkB,SAAS;AAET,oBAAA,UAAU,CAAC,UAAa;AACxC,sBAAkB,SAAS;AAC3B,sBAAkB,QAAQ;AAC1B,uBAAmB,KAAK;AACxB,2CAAY;AAAA,EACd;AAEkB,oBAAA,SAAS,CAAC,MAAM;AAChC,sBAAkB,SAAS;AAC3B,sBAAkB,CAAC;AAAA,EACrB;AAEO,SAAA;AACT;AAMO,SAAS,WAAW,YAAoB;AACtC,SAAA,WACJ,QAAQ,OAAO,MAAM,EACrB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK;AACxB;AAEgB,SAAA,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AAClB,WAAA;AAAA,EAAA;AAIP,MAAA,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACO,WAAA;AAAA,EAAA;AAGH,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACtC,WAAA;AAAA,EAAA;AAGT,aAAW,QAAQ,OAAO;AACxB,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAChD,CAAC,OAAO,GAAG,KAAK,IAAe,GAAG,KAAK,IAAe,CAAC,GACvD;AACO,aAAA;AAAA,IAAA;AAAA,EACT;AAEK,SAAA;AACT;;;;;;;;;;;"}