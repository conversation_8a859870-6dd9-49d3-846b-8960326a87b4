{"version": 3, "file": "lazyRouteComponent.cjs", "sources": ["../../src/lazyRouteComponent.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { Outlet } from './Match'\nimport { <PERSON><PERSON>Only } from './ClientOnly'\nimport type { AsyncRouteComponent } from './route'\n\n// If the load fails due to module not found, it may mean a new version of\n// the build was deployed and the user's browser is still using an old version.\n// If this happens, the old version in the user's browser would have an outdated\n// URL to the lazy module.\n// In that case, we want to attempt one window refresh to get the latest.\nfunction isModuleNotFoundError(error: any): boolean {\n  // chrome: \"Failed to fetch dynamically imported module: http://localhost:5173/src/routes/posts.index.tsx?tsr-split\"\n  // firefox: \"error loading dynamically imported module: http://localhost:5173/src/routes/posts.index.tsx?tsr-split\"\n  // safari: \"Importing a module script failed.\"\n  if (typeof error?.message !== 'string') return false\n  return (\n    error.message.startsWith('Failed to fetch dynamically imported module') ||\n    error.message.startsWith('error loading dynamically imported module') ||\n    error.message.startsWith('Importing a module script failed')\n  )\n}\n\nexport function lazyRouteComponent<\n  T extends Record<string, any>,\n  <PERSON><PERSON><PERSON> extends keyof T = 'default',\n>(\n  importer: () => Promise<T>,\n  exportName?: TKey,\n  ssr?: () => boolean,\n): T[TKey] extends (props: infer TProps) => any\n  ? AsyncRouteComponent<TProps>\n  : never {\n  let loadPromise: Promise<any> | undefined\n  let comp: T[TKey] | T['default']\n  let error: any\n  let reload: boolean\n\n  const load = () => {\n    if (typeof document === 'undefined' && ssr?.() === false) {\n      comp = (() => null) as any\n      return Promise.resolve()\n    }\n    if (!loadPromise) {\n      loadPromise = importer()\n        .then((res) => {\n          loadPromise = undefined\n          comp = res[exportName ?? 'default']\n        })\n        .catch((err) => {\n          // We don't want an error thrown from preload in this case, because\n          // there's nothing we want to do about module not found during preload.\n          // Record the error, the rest is handled during the render path.\n          error = err\n          if (isModuleNotFoundError(error)) {\n            if (\n              error instanceof Error &&\n              typeof window !== 'undefined' &&\n              typeof sessionStorage !== 'undefined'\n            ) {\n              // Again, we want to reload one time on module not found error and not enter\n              // a reload loop if there is some other issue besides an old deploy.\n              // That's why we store our reload attempt in sessionStorage.\n              // Use error.message as key because it contains the module path that failed.\n              const storageKey = `tanstack_router_reload:${error.message}`\n              if (!sessionStorage.getItem(storageKey)) {\n                sessionStorage.setItem(storageKey, '1')\n                reload = true\n              }\n            }\n          }\n        })\n    }\n\n    return loadPromise\n  }\n\n  const lazyComp = function Lazy(props: any) {\n    // Now that we're out of preload and into actual render path,\n    if (reload) {\n      // If it was a module loading error,\n      // throw eternal suspense while we wait for window to reload\n      window.location.reload()\n      throw new Promise(() => {})\n    }\n    if (error) {\n      // Otherwise, just throw the error\n      throw error\n    }\n\n    if (!comp) {\n      throw load()\n    }\n\n    if (ssr?.() === false) {\n      return (\n        <ClientOnly fallback={<Outlet />}>\n          {React.createElement(comp, props)}\n        </ClientOnly>\n      )\n    }\n    return React.createElement(comp, props)\n  }\n\n  ;(lazyComp as any).preload = load\n\n  return lazyComp as any\n}\n"], "names": ["jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Outlet", "React"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,sBAAsB,OAAqB;AAIlD,MAAI,QAAO,+BAAO,aAAY,SAAiB,QAAA;AAC/C,SACE,MAAM,QAAQ,WAAW,6CAA6C,KACtE,MAAM,QAAQ,WAAW,2CAA2C,KACpE,MAAM,QAAQ,WAAW,kCAAkC;AAE/D;AAEgB,SAAA,mBAId,UACA,YACA,KAGQ;AACJ,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AAEJ,QAAM,OAAO,MAAM;AACjB,QAAI,OAAO,aAAa,gBAAe,kCAAY,OAAO;AACxD,aAAQ,MAAM;AACd,aAAO,QAAQ,QAAQ;AAAA,IAAA;AAEzB,QAAI,CAAC,aAAa;AAChB,oBAAc,SAAS,EACpB,KAAK,CAAC,QAAQ;AACC,sBAAA;AACP,eAAA,IAAI,cAAc,SAAS;AAAA,MAAA,CACnC,EACA,MAAM,CAAC,QAAQ;AAIN,gBAAA;AACJ,YAAA,sBAAsB,KAAK,GAAG;AAChC,cACE,iBAAiB,SACjB,OAAO,WAAW,eAClB,OAAO,mBAAmB,aAC1B;AAKM,kBAAA,aAAa,0BAA0B,MAAM,OAAO;AAC1D,gBAAI,CAAC,eAAe,QAAQ,UAAU,GAAG;AACxB,6BAAA,QAAQ,YAAY,GAAG;AAC7B,uBAAA;AAAA,YAAA;AAAA,UACX;AAAA,QACF;AAAA,MACF,CACD;AAAA,IAAA;AAGE,WAAA;AAAA,EACT;AAEM,QAAA,WAAW,SAAS,KAAK,OAAY;AAEzC,QAAI,QAAQ;AAGV,aAAO,SAAS,OAAO;AACjB,YAAA,IAAI,QAAQ,MAAM;AAAA,MAAA,CAAE;AAAA,IAAA;AAE5B,QAAI,OAAO;AAEH,YAAA;AAAA,IAAA;AAGR,QAAI,CAAC,MAAM;AACT,YAAM,KAAK;AAAA,IAAA;AAGT,SAAA,kCAAY,OAAO;AAEnB,aAAAA,2BAAA,IAACC,WAAW,YAAA,EAAA,UAAWD,2BAAAA,IAAAE,MAAAA,QAAA,CAAA,CAAO,GAC3B,UAAMC,iBAAA,cAAc,MAAM,KAAK,EAClC,CAAA;AAAA,IAAA;AAGG,WAAAA,iBAAM,cAAc,MAAM,KAAK;AAAA,EACxC;AAEE,WAAiB,UAAU;AAEtB,SAAA;AACT;;"}