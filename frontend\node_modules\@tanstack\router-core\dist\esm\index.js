import { TSR_DEFERRED_PROMISE, defer } from "./defer.js";
import { preloadWarning } from "./link.js";
import { isMatch } from "./Matches.js";
import { cleanPath, exactPathTest, interpolatePath, joinPaths, matchByPath, matchPathname, parsePathname, removeBasepath, removeTrailingSlash, resolvePath, trimPath, trimPathLeft, trimPathRight } from "./path.js";
import { decode, encode } from "./qss.js";
import { rootRouteId } from "./root.js";
import { BaseRootRoute, BaseRoute, BaseRouteApi } from "./route.js";
import { PathParamError, RouterCore, SearchParamError, componentTypes, defaultSerializeError, getInitialRouterState, getLocationChangeInfo, lazyFn } from "./router.js";
import { retainSearchParams, stripSearchParams } from "./searchMiddleware.js";
import { defaultParseSearch, defaultStringifySearch, parseSearchWith, stringifySearchWith } from "./searchParams.js";
import { createControlledPromise, deepEqual, escapeJSON, functionalUpdate, isPlainArray, isPlainObject, last, pick, replaceEqualDeep, shallow } from "./utils.js";
import { isRedirect, isResolvedRedirect, redirect } from "./redirect.js";
import { isNotFound, notFound } from "./not-found.js";
import { defaultGetScrollRestorationKey, getCssSelector, handleHashScroll, restoreScroll, scrollRestorationCache, setupScrollRestoration, storageKey } from "./scroll-restoration.js";
export {
  BaseRootRoute,
  BaseRoute,
  BaseRouteApi,
  PathParamError,
  RouterCore,
  SearchParamError,
  TSR_DEFERRED_PROMISE,
  cleanPath,
  componentTypes,
  createControlledPromise,
  decode,
  deepEqual,
  defaultGetScrollRestorationKey,
  defaultParseSearch,
  defaultSerializeError,
  defaultStringifySearch,
  defer,
  encode,
  escapeJSON,
  exactPathTest,
  functionalUpdate,
  getCssSelector,
  getInitialRouterState,
  getLocationChangeInfo,
  handleHashScroll,
  interpolatePath,
  isMatch,
  isNotFound,
  isPlainArray,
  isPlainObject,
  isRedirect,
  isResolvedRedirect,
  joinPaths,
  last,
  lazyFn,
  matchByPath,
  matchPathname,
  notFound,
  parsePathname,
  parseSearchWith,
  pick,
  preloadWarning,
  redirect,
  removeBasepath,
  removeTrailingSlash,
  replaceEqualDeep,
  resolvePath,
  restoreScroll,
  retainSearchParams,
  rootRouteId,
  scrollRestorationCache,
  setupScrollRestoration,
  shallow,
  storageKey,
  stringifySearchWith,
  stripSearchParams,
  trimPath,
  trimPathLeft,
  trimPathRight
};
//# sourceMappingURL=index.js.map
