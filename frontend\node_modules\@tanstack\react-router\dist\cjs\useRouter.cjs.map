{"version": 3, "file": "useRouter.cjs", "sources": ["../../src/useRouter.tsx"], "sourcesContent": ["import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { getRouterContext } from './routerContext'\nimport type { AnyRouter, RegisteredRouter } from '@tanstack/router-core'\n\nexport function useRouter<TRouter extends AnyRouter = RegisteredRouter>(opts?: {\n  warn?: boolean\n}): TRouter {\n  const value = React.useContext(getRouterContext())\n  warning(\n    !((opts?.warn ?? true) && !value),\n    'useRouter must be used inside a <RouterProvider> component!',\n  )\n  return value as any\n}\n"], "names": ["React", "getRouterContext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAKO,SAAS,UAAwD,MAE5D;AACV,QAAM,QAAQA,iBAAM,WAAWC,cAAA,iBAAA,CAAkB;AACjD;AAAA,IACE,IAAG,6BAAM,SAAQ,SAAS,CAAC;AAAA,IAC3B;AAAA,EACF;AACO,SAAA;AACT;;"}