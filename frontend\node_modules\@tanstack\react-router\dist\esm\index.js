import { default as default2 } from "tiny-invariant";
import { default as default3 } from "tiny-warning";
import { PathParamError, SearchParamError, TSR_DEFERRED_PROMISE, cleanPath, componentTypes, createControlledPromise, decode, deepEqual, defaultParseSearch, defaultSerializeError, defaultStringifySearch, defer, encode, escapeJSON, functionalUpdate, getInitialRouterState, interpolatePath, isMatch, isNotFound, isPlainArray, isPlainObject, isRedirect, joinPaths, lazyFn, matchByPath, matchPathname, notFound, parsePathname, parseSearchWith, pick, redirect, removeBasepath, replaceEqualDeep, resolvePath, retainSearchParams, rootRouteId, shallow, stringifySearchWith, stripSearchParams, trimPath, trimPathLeft, trimPathRight } from "@tanstack/router-core";
import { createBrowserHistory, createHashHistory, createHistory, createMemoryHistory } from "@tanstack/history";
import { Await, useAwaited } from "./awaited.js";
import { CatchBoundary, ErrorComponent } from "./CatchBoundary.js";
import { ClientOnly } from "./ClientOnly.js";
import { FileRoute, FileRouteLoader, LazyRoute, createFileRoute, createLazyFileRoute, createLazyRoute } from "./fileRoute.js";
import { lazyRouteComponent } from "./lazyRouteComponent.js";
import { Link, createLink, linkOptions, useLinkProps } from "./link.js";
import { MatchRoute, Matches, useChildMatches, useMatchRoute, useMatches, useParentMatches } from "./Matches.js";
import { matchContext } from "./matchContext.js";
import { Match, Outlet } from "./Match.js";
import { useMatch } from "./useMatch.js";
import { useLoaderDeps } from "./useLoaderDeps.js";
import { useLoaderData } from "./useLoaderData.js";
import { NotFoundRoute, RootRoute, Route, RouteApi, createRootRoute, createRootRouteWithContext, createRoute, createRouteMask, getRouteApi, rootRouteWithContext } from "./route.js";
import { Router, createRouter } from "./router.js";
import { RouterContextProvider, RouterProvider } from "./RouterProvider.js";
import { ScrollRestoration, useElementScrollRestoration } from "./ScrollRestoration.js";
import { Block, useBlocker } from "./useBlocker.js";
import { Navigate, useNavigate } from "./useNavigate.js";
import { useParams } from "./useParams.js";
import { useSearch } from "./useSearch.js";
import { getRouterContext } from "./routerContext.js";
import { useRouteContext } from "./useRouteContext.js";
import { useRouter } from "./useRouter.js";
import { useRouterState } from "./useRouterState.js";
import { useLocation } from "./useLocation.js";
import { useCanGoBack } from "./useCanGoBack.js";
import { useLayoutEffect, useStableCallback } from "./utils.js";
import { CatchNotFound, DefaultGlobalNotFound } from "./not-found.js";
import { ScriptOnce } from "./ScriptOnce.js";
import { Asset } from "./Asset.js";
import { HeadContent } from "./HeadContent.js";
import { Scripts } from "./Scripts.js";
export {
  Asset,
  Await,
  Block,
  CatchBoundary,
  CatchNotFound,
  ClientOnly,
  DefaultGlobalNotFound,
  ErrorComponent,
  FileRoute,
  FileRouteLoader,
  HeadContent,
  LazyRoute,
  Link,
  Match,
  MatchRoute,
  Matches,
  Navigate,
  NotFoundRoute,
  Outlet,
  PathParamError,
  RootRoute,
  Route,
  RouteApi,
  Router,
  RouterContextProvider,
  RouterProvider,
  ScriptOnce,
  Scripts,
  ScrollRestoration,
  SearchParamError,
  TSR_DEFERRED_PROMISE,
  cleanPath,
  componentTypes,
  createBrowserHistory,
  createControlledPromise,
  createFileRoute,
  createHashHistory,
  createHistory,
  createLazyFileRoute,
  createLazyRoute,
  createLink,
  createMemoryHistory,
  createRootRoute,
  createRootRouteWithContext,
  createRoute,
  createRouteMask,
  createRouter,
  decode,
  deepEqual,
  defaultParseSearch,
  defaultSerializeError,
  defaultStringifySearch,
  defer,
  encode,
  escapeJSON,
  functionalUpdate,
  getInitialRouterState,
  getRouteApi,
  getRouterContext,
  interpolatePath,
  default2 as invariant,
  isMatch,
  isNotFound,
  isPlainArray,
  isPlainObject,
  isRedirect,
  joinPaths,
  lazyFn,
  lazyRouteComponent,
  linkOptions,
  matchByPath,
  matchContext,
  matchPathname,
  notFound,
  parsePathname,
  parseSearchWith,
  pick,
  redirect,
  removeBasepath,
  replaceEqualDeep,
  resolvePath,
  retainSearchParams,
  rootRouteId,
  rootRouteWithContext,
  shallow,
  stringifySearchWith,
  stripSearchParams,
  trimPath,
  trimPathLeft,
  trimPathRight,
  useAwaited,
  useBlocker,
  useCanGoBack,
  useChildMatches,
  useElementScrollRestoration,
  useLayoutEffect,
  useLinkProps,
  useLoaderData,
  useLoaderDeps,
  useLocation,
  useMatch,
  useMatchRoute,
  useMatches,
  useNavigate,
  useParams,
  useParentMatches,
  useRouteContext,
  useRouter,
  useRouterState,
  useSearch,
  useStableCallback,
  default3 as warning
};
//# sourceMappingURL=index.js.map
