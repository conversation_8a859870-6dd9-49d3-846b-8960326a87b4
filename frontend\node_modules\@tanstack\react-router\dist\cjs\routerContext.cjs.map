{"version": 3, "file": "routerContext.cjs", "sources": ["../../src/routerContext.tsx"], "sourcesContent": ["import * as React from 'react'\nimport type { AnyRouter } from '@tanstack/router-core'\n\ndeclare global {\n  interface Window {\n    __TSR_ROUTER_CONTEXT__?: React.Context<AnyRouter>\n  }\n}\n\nconst routerContext = React.createContext<AnyRouter>(null!)\n\nexport function getRouterContext() {\n  if (typeof document === 'undefined') {\n    return routerContext\n  }\n\n  if (window.__TSR_ROUTER_CONTEXT__) {\n    return window.__TSR_ROUTER_CONTEXT__\n  }\n\n  window.__TSR_ROUTER_CONTEXT__ = routerContext as any\n\n  return routerContext\n}\n"], "names": ["React"], "mappings": ";;;;;;;;;;;;;;;;;;;;AASA,MAAM,gBAAgBA,iBAAM,cAAyB,IAAK;AAEnD,SAAS,mBAAmB;AAC7B,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;AAAA,EAAA;AAGT,MAAI,OAAO,wBAAwB;AACjC,WAAO,OAAO;AAAA,EAAA;AAGhB,SAAO,yBAAyB;AAEzB,SAAA;AACT;;"}