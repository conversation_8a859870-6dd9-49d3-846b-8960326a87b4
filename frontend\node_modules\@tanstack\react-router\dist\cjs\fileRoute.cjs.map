{"version": 3, "file": "fileRoute.cjs", "sources": ["../../src/fileRoute.ts"], "sourcesContent": ["import warning from 'tiny-warning'\nimport { createRoute } from './route'\n\nimport { useMatch } from './useMatch'\nimport { useLoaderDeps } from './useLoaderDeps'\nimport { useLoaderData } from './useLoaderData'\nimport { useSearch } from './useSearch'\nimport { useParams } from './useParams'\nimport { useNavigate } from './useNavigate'\nimport { useRouter } from './useRouter'\nimport type { UseParamsRoute } from './useParams'\nimport type { UseMatchRoute } from './useMatch'\nimport type { UseSearchRoute } from './useSearch'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouter,\n  Constrain,\n  ConstrainLiteral,\n  FileBaseRouteOptions,\n  FileRoutesByPath,\n  LazyRouteOptions,\n  RegisteredRouter,\n  ResolveParams,\n  Route,\n  RouteById,\n  RouteConstraints,\n  RouteIds,\n  RouteLoaderFn,\n  UpdatableRouteOptions,\n} from '@tanstack/router-core'\nimport type { UseLoaderDepsRoute } from './useLoaderDeps'\nimport type { UseLoaderDataRoute } from './useLoaderData'\nimport type { UseRouteContextRoute } from './useRouteContext'\n\nexport function createFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = FileRoutesByPath[TFilePath]['id'],\n  TPath extends RouteConstraints['TPath'] = FileRoutesByPath[TFilePath]['path'],\n  TFullPath extends\n    RouteConstraints['TFullPath'] = FileRoutesByPath[TFilePath]['fullPath'],\n>(\n  path: TFilePath,\n): FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>['createRoute'] {\n  return new FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>(path, {\n    silent: true,\n  }).createRoute\n}\n\n/** \n  @deprecated It's no longer recommended to use the `FileRoute` class directly.\n  Instead, use `createFileRoute('/path/to/file')(options)` to create a file route.\n*/\nexport class FileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = FileRoutesByPath[TFilePath]['id'],\n  TPath extends RouteConstraints['TPath'] = FileRoutesByPath[TFilePath]['path'],\n  TFullPath extends\n    RouteConstraints['TFullPath'] = FileRoutesByPath[TFilePath]['fullPath'],\n> {\n  silent?: boolean\n\n  constructor(\n    public path: TFilePath,\n    _opts?: { silent: boolean },\n  ) {\n    this.silent = _opts?.silent\n  }\n\n  createRoute = <\n    TSearchValidator = undefined,\n    TParams = ResolveParams<TPath>,\n    TRouteContextFn = AnyContext,\n    TBeforeLoadFn = AnyContext,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderFn = undefined,\n    TChildren = unknown,\n  >(\n    options?: FileBaseRouteOptions<\n      TParentRoute,\n      TId,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      AnyContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    > &\n      UpdatableRouteOptions<\n        TParentRoute,\n        TId,\n        TFullPath,\n        TParams,\n        TSearchValidator,\n        TLoaderFn,\n        TLoaderDeps,\n        AnyContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n  ): Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TFilePath,\n    TId,\n    TSearchValidator,\n    TParams,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    unknown\n  > => {\n    warning(\n      this.silent,\n      'FileRoute is deprecated and will be removed in the next major version. Use the createFileRoute(path)(options) function instead.',\n    )\n    const route = createRoute(options as any)\n    ;(route as any).isRoot = false\n    return route as any\n  }\n}\n\n/** \n  @deprecated It's recommended not to split loaders into separate files.\n  Instead, place the loader function in the the main route file, inside the\n  `createFileRoute('/path/to/file)(options)` options.\n*/\nexport function FileRouteLoader<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(\n  _path: TFilePath,\n): <TLoaderFn>(\n  loaderFn: Constrain<\n    TLoaderFn,\n    RouteLoaderFn<\n      TRoute['parentRoute'],\n      TRoute['types']['id'],\n      TRoute['types']['params'],\n      TRoute['types']['loaderDeps'],\n      TRoute['types']['routerContext'],\n      TRoute['types']['routeContextFn'],\n      TRoute['types']['beforeLoadFn']\n    >\n  >,\n) => TLoaderFn {\n  warning(\n    false,\n    `FileRouteLoader is deprecated and will be removed in the next major version. Please place the loader function in the the main route file, inside the \\`createFileRoute('/path/to/file')(options)\\` options`,\n  )\n  return (loaderFn) => loaderFn as any\n}\n\nexport class LazyRoute<TRoute extends AnyRoute> {\n  options: {\n    id: string\n  } & LazyRouteOptions\n\n  constructor(\n    opts: {\n      id: string\n    } & LazyRouteOptions,\n  ) {\n    this.options = opts\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<TRoute['id']> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.options.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TRoute['id']> = (opts) => {\n    return useMatch({\n      from: this.options.id,\n      select: (d: any) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TRoute['id']> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.options.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TRoute['id']> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.options.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TRoute['id']> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.options.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TRoute['id']> = (opts) => {\n    return useLoaderData({ ...opts, from: this.options.id } as any)\n  }\n\n  useNavigate = () => {\n    const router = useRouter()\n    return useNavigate({ from: router.routesById[this.options.id].fullPath })\n  }\n}\n\nexport function createLazyRoute<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TId extends string = string,\n  TRoute extends AnyRoute = RouteById<TRouter['routeTree'], TId>,\n>(id: ConstrainLiteral<TId, RouteIds<TRouter['routeTree']>>) {\n  return (opts: LazyRouteOptions) => {\n    return new LazyRoute<TRoute>({\n      id: id,\n      ...opts,\n    })\n  }\n}\n\nexport function createLazyFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(id: TFilePath) {\n  return (opts: LazyRouteOptions) => new LazyRoute<TRoute>({ id, ...opts })\n}\n"], "names": ["route", "createRoute", "opts", "useMatch", "useSearch", "useParams", "useLoaderDeps", "useLoaderData", "useRouter", "useNavigate"], "mappings": ";;;;;;;;;;;AAmCO,SAAS,gBAQd,MAC0E;AACnE,SAAA,IAAI,UAA0D,MAAM;AAAA,IACzE,QAAQ;AAAA,EACT,CAAA,EAAE;AACL;AAMO,MAAM,UAOX;AAAA,EAGA,YACS,MACP,OACA;AAFO,SAAA,OAAA;AAMT,SAAA,cAAc,CASZ,YAuCG;AACH;AAAA,QACE,KAAK;AAAA,QACL;AAAA,MACF;AACM,YAAAA,UAAQC,kBAAY,OAAc;AACtCD,cAAc,SAAS;AAClB,aAAAA;AAAAA,IACT;AA3DE,SAAK,SAAS,+BAAO;AAAA,EAAA;AA4DzB;AAOO,SAAS,gBAId,OAca;AACb;AAAA,IACE;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,aAAa;AACvB;AAEO,MAAM,UAAmC;AAAA,EAK9C,YACE,MAGA;AAKF,SAAA,WAAwC,CAACE,UAAS;AAChD,aAAOC,kBAAS;AAAA,QACd,QAAQD,SAAA,gBAAAA,MAAM;AAAA,QACd,MAAM,KAAK,QAAQ;AAAA,QACnB,mBAAmBA,SAAA,gBAAAA,MAAM;AAAA,MAAA,CACnB;AAAA,IACV;AAEA,SAAA,kBAAsD,CAACA,UAAS;AAC9D,aAAOC,kBAAS;AAAA,QACd,MAAM,KAAK,QAAQ;AAAA,QACnB,QAAQ,CAAC,OAAYD,SAAA,gBAAAA,MAAM,UAASA,MAAK,OAAO,EAAE,OAAO,IAAI,EAAE;AAAA,MAAA,CAChE;AAAA,IACH;AAEA,SAAA,YAA0C,CAACA,UAAS;AAElD,aAAOE,oBAAU;AAAA,QACf,QAAQF,SAAA,gBAAAA,MAAM;AAAA,QACd,mBAAmBA,SAAA,gBAAAA,MAAM;AAAA,QACzB,MAAM,KAAK,QAAQ;AAAA,MAAA,CACb;AAAA,IACV;AAEA,SAAA,YAA0C,CAACA,UAAS;AAElD,aAAOG,oBAAU;AAAA,QACf,QAAQH,SAAA,gBAAAA,MAAM;AAAA,QACd,mBAAmBA,SAAA,gBAAAA,MAAM;AAAA,QACzB,MAAM,KAAK,QAAQ;AAAA,MAAA,CACb;AAAA,IACV;AAEA,SAAA,gBAAkD,CAACA,UAAS;AACnD,aAAAI,cAAAA,cAAc,EAAE,GAAGJ,OAAM,MAAM,KAAK,QAAQ,IAAW;AAAA,IAChE;AAEA,SAAA,gBAAkD,CAACA,UAAS;AACnD,aAAAK,cAAAA,cAAc,EAAE,GAAGL,OAAM,MAAM,KAAK,QAAQ,IAAW;AAAA,IAChE;AAEA,SAAA,cAAc,MAAM;AAClB,YAAM,SAASM,UAAAA,UAAU;AAClB,aAAAC,YAAA,YAAY,EAAE,MAAM,OAAO,WAAW,KAAK,QAAQ,EAAE,EAAE,UAAU;AAAA,IAC1E;AAhDE,SAAK,UAAU;AACb,SAAa,WAAW,OAAO,IAAI,YAAY;AAAA,EAAA;AAgDrD;AAEO,SAAS,gBAId,IAA2D;AAC3D,SAAO,CAAC,SAA2B;AACjC,WAAO,IAAI,UAAkB;AAAA,MAC3B;AAAA,MACA,GAAG;AAAA,IAAA,CACJ;AAAA,EACH;AACF;AAEO,SAAS,oBAGd,IAAe;AACR,SAAA,CAAC,SAA2B,IAAI,UAAkB,EAAE,IAAI,GAAG,MAAM;AAC1E;;;;;;;"}