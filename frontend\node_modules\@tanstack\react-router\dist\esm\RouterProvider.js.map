{"version": 3, "file": "RouterProvider.js", "sources": ["../../src/RouterProvider.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { Matches } from './Matches'\nimport { getRouterContext } from './routerContext'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterOptions,\n} from '@tanstack/router-core'\n\nexport function RouterContextProvider<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({\n  router,\n  children,\n  ...rest\n}: RouterProps<TRouter, TDehydrated> & {\n  children: React.ReactNode\n}) {\n  // Allow the router to update options on the router instance\n  router.update({\n    ...router.options,\n    ...rest,\n    context: {\n      ...router.options.context,\n      ...rest.context,\n    },\n  } as any)\n\n  const routerContext = getRouterContext()\n\n  const provider = (\n    <routerContext.Provider value={router as AnyRouter}>\n      {children}\n    </routerContext.Provider>\n  )\n\n  if (router.options.Wrap) {\n    return <router.options.Wrap>{provider}</router.options.Wrap>\n  }\n\n  return provider\n}\n\nexport function RouterProvider<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({ router, ...rest }: RouterProps<TRouter, TDehydrated>) {\n  return (\n    <RouterContextProvider router={router} {...rest}>\n      <Matches />\n    </RouterContextProvider>\n  )\n}\n\nexport type RouterProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n> = Omit<\n  RouterOptions<\n    TRouter['routeTree'],\n    NonNullable<TRouter['options']['trailingSlash']>,\n    NonNullable<TRouter['options']['defaultStructuralSharing']>,\n    TRouter['history'],\n    TDehydrated\n  >,\n  'context'\n> & {\n  router: TRouter\n  context?: Partial<\n    RouterOptions<\n      TRouter['routeTree'],\n      NonNullable<TRouter['options']['trailingSlash']>,\n      NonNullable<TRouter['options']['defaultStructuralSharing']>,\n      TRouter['history'],\n      TDehydrated\n    >['context']\n  >\n}\n"], "names": [], "mappings": ";;;AASO,SAAS,sBAGd;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAEG;AAED,SAAO,OAAO;AAAA,IACZ,GAAG,OAAO;AAAA,IACV,GAAG;AAAA,IACH,SAAS;AAAA,MACP,GAAG,OAAO,QAAQ;AAAA,MAClB,GAAG,KAAK;AAAA,IAAA;AAAA,EACV,CACM;AAER,QAAM,gBAAgB,iBAAiB;AAEvC,QAAM,WACH,oBAAA,cAAc,UAAd,EAAuB,OAAO,QAC5B,UACH;AAGE,MAAA,OAAO,QAAQ,MAAM;AACvB,WAAQ,oBAAA,OAAO,QAAQ,MAAf,EAAqB,UAAS,UAAA;AAAA,EAAA;AAGjC,SAAA;AACT;AAEO,SAAS,eAGd,EAAE,QAAQ,GAAG,QAA2C;AACxD,6BACG,uBAAsB,EAAA,QAAiB,GAAG,MACzC,UAAA,oBAAC,UAAQ,CAAA,GACX;AAEJ;"}