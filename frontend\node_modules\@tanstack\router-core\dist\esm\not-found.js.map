{"version": 3, "file": "not-found.js", "sources": ["../../src/not-found.ts"], "sourcesContent": ["import type { RouteIds } from './routeInfo'\nimport type { RegisteredRouter } from './router'\n\nexport type NotFoundError = {\n  /**\n    @deprecated\n    Use `routeId: rootRouteId` instead\n  */\n  global?: boolean\n  /**\n    @private\n    Do not use this. It's used internally to indicate a path matching error\n  */\n  _global?: boolean\n  data?: any\n  throw?: boolean\n  routeId?: RouteIds<RegisteredRouter['routeTree']>\n  headers?: HeadersInit\n}\n\nexport function notFound(options: NotFoundError = {}) {\n  ;(options as any).isNotFound = true\n  if (options.throw) throw options\n  return options\n}\n\nexport function isNotFound(obj: any): obj is NotFoundError {\n  return !!obj?.isNotFound\n}\n"], "names": [], "mappings": "AAoBgB,SAAA,SAAS,UAAyB,IAAI;AAClD,UAAgB,aAAa;AAC3B,MAAA,QAAQ,MAAa,OAAA;AAClB,SAAA;AACT;AAEO,SAAS,WAAW,KAAgC;AAClD,SAAA,CAAC,EAAC,2BAAK;AAChB;"}