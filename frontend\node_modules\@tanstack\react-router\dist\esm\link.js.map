{"version": 3, "file": "link.js", "sources": ["../../src/link.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  deepEqual,\n  exactPathTest,\n  functionalUpdate,\n  preloadWarning,\n  removeTrailingSlash,\n} from '@tanstack/router-core'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\n\nimport {\n  useForwardedRef,\n  useIntersectionObserver,\n  useLayoutEffect,\n} from './utils'\n\nimport { useMatches } from './Matches'\nimport type {\n  AnyRouter,\n  Constrain,\n  LinkCurrentTargetElement,\n  LinkOptions,\n  RegisteredRouter,\n  RoutePaths,\n} from '@tanstack/router-core'\nimport type { ReactNode } from 'react'\nimport type {\n  ValidateLinkOptions,\n  ValidateLinkOptionsArray,\n} from './typePrimitives'\n\nexport function useLinkProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  options: UseLinkPropsOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n  forwardedRef?: React.ForwardedRef<Element>,\n): React.ComponentPropsWithRef<'a'> {\n  const router = useRouter()\n  const [isTransitioning, setIsTransitioning] = React.useState(false)\n  const hasRenderFetched = React.useRef(false)\n  const innerRef = useForwardedRef(forwardedRef)\n\n  const {\n    // custom props\n    activeProps = () => ({ className: 'active' }),\n    inactiveProps = () => ({}),\n    activeOptions,\n    to,\n    preload: userPreload,\n    preloadDelay: userPreloadDelay,\n    hashScrollIntoView,\n    replace,\n    startTransition,\n    resetScroll,\n    viewTransition,\n    // element props\n    children,\n    target,\n    disabled,\n    style,\n    className,\n    onClick,\n    onFocus,\n    onMouseEnter,\n    onMouseLeave,\n    onTouchStart,\n    ignoreBlocker,\n    ...rest\n  } = options\n\n  const {\n    // prevent these from being returned\n    params: _params,\n    search: _search,\n    hash: _hash,\n    state: _state,\n    mask: _mask,\n    reloadDocument: _reloadDocument,\n    ...propsSafeToSpread\n  } = rest\n\n  // If this link simply reloads the current route,\n  // make sure it has a new key so it will trigger a data refresh\n\n  // If this `to` is a valid external URL, return\n  // null for LinkUtils\n\n  const type: 'internal' | 'external' = React.useMemo(() => {\n    try {\n      new URL(`${to}`)\n      return 'external'\n    } catch {}\n    return 'internal'\n  }, [to])\n\n  // subscribe to search params to re-build location if it changes\n  const currentSearch = useRouterState({\n    select: (s) => s.location.search,\n    structuralSharing: true as any,\n  })\n\n  // when `from` is not supplied, use the leaf route of the current matches as the `from` location\n  // so relative routing works as expected\n  const from = useMatches({\n    select: (matches) => options.from ?? matches[matches.length - 1]?.fullPath,\n  })\n  // Use it as the default `from` location\n  const _options = React.useMemo(() => ({ ...options, from }), [options, from])\n\n  const next = React.useMemo(\n    () => router.buildLocation(_options as any),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [router, _options, currentSearch],\n  )\n\n  const preload = React.useMemo(() => {\n    if (_options.reloadDocument) {\n      return false\n    }\n    return userPreload ?? router.options.defaultPreload\n  }, [router.options.defaultPreload, userPreload, _options.reloadDocument])\n  const preloadDelay =\n    userPreloadDelay ?? router.options.defaultPreloadDelay ?? 0\n\n  const isActive = useRouterState({\n    select: (s) => {\n      if (activeOptions?.exact) {\n        const testExact = exactPathTest(\n          s.location.pathname,\n          next.pathname,\n          router.basepath,\n        )\n        if (!testExact) {\n          return false\n        }\n      } else {\n        const currentPathSplit = removeTrailingSlash(\n          s.location.pathname,\n          router.basepath,\n        ).split('/')\n        const nextPathSplit = removeTrailingSlash(\n          next.pathname,\n          router.basepath,\n        ).split('/')\n\n        const pathIsFuzzyEqual = nextPathSplit.every(\n          (d, i) => d === currentPathSplit[i],\n        )\n        if (!pathIsFuzzyEqual) {\n          return false\n        }\n      }\n\n      if (activeOptions?.includeSearch ?? true) {\n        const searchTest = deepEqual(s.location.search, next.search, {\n          partial: !activeOptions?.exact,\n          ignoreUndefined: !activeOptions?.explicitUndefined,\n        })\n        if (!searchTest) {\n          return false\n        }\n      }\n\n      if (activeOptions?.includeHash) {\n        return s.location.hash === next.hash\n      }\n      return true\n    },\n  })\n\n  const doPreload = React.useCallback(() => {\n    router.preloadRoute(_options as any).catch((err) => {\n      console.warn(err)\n      console.warn(preloadWarning)\n    })\n  }, [_options, router])\n\n  const preloadViewportIoCallback = React.useCallback(\n    (entry: IntersectionObserverEntry | undefined) => {\n      if (entry?.isIntersecting) {\n        doPreload()\n      }\n    },\n    [doPreload],\n  )\n\n  useIntersectionObserver(\n    innerRef,\n    preloadViewportIoCallback,\n    { rootMargin: '100px' },\n    { disabled: !!disabled || !(preload === 'viewport') },\n  )\n\n  useLayoutEffect(() => {\n    if (hasRenderFetched.current) {\n      return\n    }\n    if (!disabled && preload === 'render') {\n      doPreload()\n      hasRenderFetched.current = true\n    }\n  }, [disabled, doPreload, preload])\n\n  if (type === 'external') {\n    return {\n      ...propsSafeToSpread,\n      ref: innerRef as React.ComponentPropsWithRef<'a'>['ref'],\n      type,\n      href: to,\n      ...(children && { children }),\n      ...(target && { target }),\n      ...(disabled && { disabled }),\n      ...(style && { style }),\n      ...(className && { className }),\n      ...(onClick && { onClick }),\n      ...(onFocus && { onFocus }),\n      ...(onMouseEnter && { onMouseEnter }),\n      ...(onMouseLeave && { onMouseLeave }),\n      ...(onTouchStart && { onTouchStart }),\n    }\n  }\n\n  // The click handler\n  const handleClick = (e: MouseEvent) => {\n    if (\n      !disabled &&\n      !isCtrlEvent(e) &&\n      !e.defaultPrevented &&\n      (!target || target === '_self') &&\n      e.button === 0\n    ) {\n      e.preventDefault()\n\n      flushSync(() => {\n        setIsTransitioning(true)\n      })\n\n      const unsub = router.subscribe('onResolved', () => {\n        unsub()\n        setIsTransitioning(false)\n      })\n\n      // All is well? Navigate!\n      // N.B. we don't call `router.commitLocation(next) here because we want to run `validateSearch` before committing\n      return router.navigate({\n        ..._options,\n        replace,\n        resetScroll,\n        hashScrollIntoView,\n        startTransition,\n        viewTransition,\n        ignoreBlocker,\n      } as any)\n    }\n  }\n\n  // The click handler\n  const handleFocus = (_: MouseEvent) => {\n    if (disabled) return\n    if (preload) {\n      doPreload()\n    }\n  }\n\n  const handleTouchStart = handleFocus\n\n  const handleEnter = (e: MouseEvent) => {\n    if (disabled) return\n    const eventTarget = (e.target || {}) as LinkCurrentTargetElement\n\n    if (preload) {\n      if (eventTarget.preloadTimeout) {\n        return\n      }\n\n      eventTarget.preloadTimeout = setTimeout(() => {\n        eventTarget.preloadTimeout = null\n        doPreload()\n      }, preloadDelay)\n    }\n  }\n\n  const handleLeave = (e: MouseEvent) => {\n    if (disabled) return\n    const eventTarget = (e.target || {}) as LinkCurrentTargetElement\n\n    if (eventTarget.preloadTimeout) {\n      clearTimeout(eventTarget.preloadTimeout)\n      eventTarget.preloadTimeout = null\n    }\n  }\n\n  const composeHandlers =\n    (handlers: Array<undefined | ((e: any) => void)>) =>\n    (e: { persist?: () => void; defaultPrevented: boolean }) => {\n      e.persist?.()\n      handlers.filter(Boolean).forEach((handler) => {\n        if (e.defaultPrevented) return\n        handler!(e)\n      })\n    }\n\n  // Get the active props\n  const resolvedActiveProps: React.HTMLAttributes<HTMLAnchorElement> = isActive\n    ? (functionalUpdate(activeProps as any, {}) ?? {})\n    : {}\n\n  // Get the inactive props\n  const resolvedInactiveProps: React.HTMLAttributes<HTMLAnchorElement> =\n    isActive ? {} : functionalUpdate(inactiveProps, {})\n\n  const resolvedClassName = [\n    className,\n    resolvedActiveProps.className,\n    resolvedInactiveProps.className,\n  ]\n    .filter(Boolean)\n    .join(' ')\n\n  const resolvedStyle = {\n    ...style,\n    ...resolvedActiveProps.style,\n    ...resolvedInactiveProps.style,\n  }\n\n  return {\n    ...propsSafeToSpread,\n    ...resolvedActiveProps,\n    ...resolvedInactiveProps,\n    href: disabled\n      ? undefined\n      : next.maskedLocation\n        ? router.history.createHref(next.maskedLocation.href)\n        : router.history.createHref(next.href),\n    ref: innerRef as React.ComponentPropsWithRef<'a'>['ref'],\n    onClick: composeHandlers([onClick, handleClick]),\n    onFocus: composeHandlers([onFocus, handleFocus]),\n    onMouseEnter: composeHandlers([onMouseEnter, handleEnter]),\n    onMouseLeave: composeHandlers([onMouseLeave, handleLeave]),\n    onTouchStart: composeHandlers([onTouchStart, handleTouchStart]),\n    disabled: !!disabled,\n    target,\n    ...(Object.keys(resolvedStyle).length && { style: resolvedStyle }),\n    ...(resolvedClassName && { className: resolvedClassName }),\n    ...(disabled && {\n      role: 'link',\n      'aria-disabled': true,\n    }),\n    ...(isActive && { 'data-status': 'active', 'aria-current': 'page' }),\n    ...(isTransitioning && { 'data-transitioning': 'transitioning' }),\n  }\n}\n\ntype UseLinkReactProps<TComp> = TComp extends keyof React.JSX.IntrinsicElements\n  ? React.JSX.IntrinsicElements[TComp]\n  : React.PropsWithoutRef<\n      TComp extends React.ComponentType<infer TProps> ? TProps : never\n    > &\n      React.RefAttributes<\n        TComp extends\n          | React.FC<{ ref: React.Ref<infer TRef> }>\n          | React.Component<{ ref: React.Ref<infer TRef> }>\n          ? TRef\n          : never\n      >\n\nexport type UseLinkPropsOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends RoutePaths<TRouter['routeTree']> | string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends RoutePaths<TRouter['routeTree']> | string = TFrom,\n  TMaskTo extends string = '.',\n> = ActiveLinkOptions<'a', TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  UseLinkReactProps<'a'>\n\nexport type ActiveLinkOptions<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = LinkOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  ActiveLinkOptionProps<TComp>\n\ntype ActiveLinkProps<TComp> = Partial<\n  LinkComponentReactProps<TComp> & {\n    [key: `data-${string}`]: unknown\n  }\n>\n\nexport interface ActiveLinkOptionProps<TComp = 'a'> {\n  /**\n   * A function that returns additional props for the `active` state of this link.\n   * These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n   */\n  activeProps?: ActiveLinkProps<TComp> | (() => ActiveLinkProps<TComp>)\n  /**\n   * A function that returns additional props for the `inactive` state of this link.\n   * These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n   */\n  inactiveProps?: ActiveLinkProps<TComp> | (() => ActiveLinkProps<TComp>)\n}\n\nexport type LinkProps<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ActiveLinkOptions<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  LinkPropsChildren\n\nexport interface LinkPropsChildren {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | React.ReactNode\n    | ((state: {\n        isActive: boolean\n        isTransitioning: boolean\n      }) => React.ReactNode)\n}\n\ntype LinkComponentReactProps<TComp> = Omit<\n  UseLinkReactProps<TComp>,\n  keyof CreateLinkProps\n>\n\nexport type LinkComponentProps<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = LinkComponentReactProps<TComp> &\n  LinkProps<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo>\n\nexport type CreateLinkProps = LinkProps<\n  any,\n  any,\n  string,\n  string,\n  string,\n  string\n>\n\nexport type LinkComponent<\n  in out TComp,\n  in out TDefaultFrom extends string = string,\n> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = TDefaultFrom,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  props: LinkComponentProps<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n) => React.ReactElement\n\nexport interface LinkComponentRoute<\n  in out TDefaultFrom extends string = string,\n> {\n  defaultFrom: TDefaultFrom\n  <\n    TRouter extends AnyRouter = RegisteredRouter,\n    const TTo extends string | undefined = undefined,\n    const TMaskTo extends string = '',\n  >(\n    props: LinkComponentProps<\n      'a',\n      TRouter,\n      this['defaultFrom'],\n      TTo,\n      this['defaultFrom'],\n      TMaskTo\n    >,\n  ): React.ReactElement\n}\n\nexport function createLink<const TComp>(\n  Comp: Constrain<TComp, any, (props: CreateLinkProps) => ReactNode>,\n): LinkComponent<TComp> {\n  return React.forwardRef(function CreatedLink(props, ref) {\n    return <Link {...(props as any)} _asChild={Comp} ref={ref} />\n  }) as any\n}\n\nexport const Link: LinkComponent<'a'> = React.forwardRef<Element, any>(\n  (props, ref) => {\n    const { _asChild, ...rest } = props\n    const {\n      type: _type,\n      ref: innerRef,\n      ...linkProps\n    } = useLinkProps(rest as any, ref)\n\n    const children =\n      typeof rest.children === 'function'\n        ? rest.children({\n            isActive: (linkProps as any)['data-status'] === 'active',\n          })\n        : rest.children\n\n    if (typeof _asChild === 'undefined') {\n      // the ReturnType of useLinkProps returns the correct type for a <a> element, not a general component that has a disabled prop\n      // @ts-expect-error\n      delete linkProps.disabled\n    }\n\n    return React.createElement(\n      _asChild ? _asChild : 'a',\n      {\n        ...linkProps,\n        ref: innerRef,\n      },\n      children,\n    )\n  },\n) as any\n\nfunction isCtrlEvent(e: MouseEvent) {\n  return !!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n}\n\nexport type LinkOptionsFnOptions<\n  TOptions,\n  TComp,\n  TRouter extends AnyRouter = RegisteredRouter,\n> =\n  TOptions extends ReadonlyArray<any>\n    ? ValidateLinkOptionsArray<TRouter, TOptions, string, TComp>\n    : ValidateLinkOptions<TRouter, TOptions, string, TComp>\n\nexport type LinkOptionsFn<TComp> = <\n  const TOptions,\n  TRouter extends AnyRouter = RegisteredRouter,\n>(\n  options: LinkOptionsFnOptions<TOptions, TComp, TRouter>,\n) => TOptions\n\nexport const linkOptions: LinkOptionsFn<'a'> = (options) => {\n  return options as any\n}\n"], "names": [], "mappings": ";;;;;;;;AAiCgB,SAAA,aAOd,SACA,cACkC;AAClC,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,iBAAiB,kBAAkB,IAAI,MAAM,SAAS,KAAK;AAC5D,QAAA,mBAAmB,MAAM,OAAO,KAAK;AACrC,QAAA,WAAW,gBAAgB,YAAY;AAEvC,QAAA;AAAA;AAAA,IAEJ,cAAc,OAAO,EAAE,WAAW;IAClC,gBAAgB,OAAO,CAAA;AAAA,IACvB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EAAA,IACD;AAEE,QAAA;AAAA;AAAA,IAEJ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,GAAG;AAAA,EAAA,IACD;AAQE,QAAA,OAAgC,MAAM,QAAQ,MAAM;AACpD,QAAA;AACE,UAAA,IAAI,GAAG,EAAE,EAAE;AACR,aAAA;AAAA,IAAA,QACD;AAAA,IAAA;AACD,WAAA;AAAA,EAAA,GACN,CAAC,EAAE,CAAC;AAGP,QAAM,gBAAgB,eAAe;AAAA,IACnC,QAAQ,CAAC,MAAM,EAAE,SAAS;AAAA,IAC1B,mBAAmB;AAAA,EAAA,CACpB;AAID,QAAM,OAAO,WAAW;AAAA,IACtB,QAAQ,CAAC,YAAY;;AAAA,qBAAQ,UAAQ,aAAQ,QAAQ,SAAS,CAAC,MAA1B,mBAA6B;AAAA;AAAA,EAAA,CACnE;AAED,QAAM,WAAW,MAAM,QAAQ,OAAO,EAAE,GAAG,SAAS,SAAS,CAAC,SAAS,IAAI,CAAC;AAE5E,QAAM,OAAO,MAAM;AAAA,IACjB,MAAM,OAAO,cAAc,QAAe;AAAA;AAAA,IAE1C,CAAC,QAAQ,UAAU,aAAa;AAAA,EAClC;AAEM,QAAA,UAAU,MAAM,QAAQ,MAAM;AAClC,QAAI,SAAS,gBAAgB;AACpB,aAAA;AAAA,IAAA;AAEF,WAAA,eAAe,OAAO,QAAQ;AAAA,EAAA,GACpC,CAAC,OAAO,QAAQ,gBAAgB,aAAa,SAAS,cAAc,CAAC;AACxE,QAAM,eACJ,oBAAoB,OAAO,QAAQ,uBAAuB;AAE5D,QAAM,WAAW,eAAe;AAAA,IAC9B,QAAQ,CAAC,MAAM;AACb,UAAI,+CAAe,OAAO;AACxB,cAAM,YAAY;AAAA,UAChB,EAAE,SAAS;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,QACT;AACA,YAAI,CAAC,WAAW;AACP,iBAAA;AAAA,QAAA;AAAA,MACT,OACK;AACL,cAAM,mBAAmB;AAAA,UACvB,EAAE,SAAS;AAAA,UACX,OAAO;AAAA,QAAA,EACP,MAAM,GAAG;AACX,cAAM,gBAAgB;AAAA,UACpB,KAAK;AAAA,UACL,OAAO;AAAA,QAAA,EACP,MAAM,GAAG;AAEX,cAAM,mBAAmB,cAAc;AAAA,UACrC,CAAC,GAAG,MAAM,MAAM,iBAAiB,CAAC;AAAA,QACpC;AACA,YAAI,CAAC,kBAAkB;AACd,iBAAA;AAAA,QAAA;AAAA,MACT;AAGE,WAAA,+CAAe,kBAAiB,MAAM;AACxC,cAAM,aAAa,UAAU,EAAE,SAAS,QAAQ,KAAK,QAAQ;AAAA,UAC3D,SAAS,EAAC,+CAAe;AAAA,UACzB,iBAAiB,EAAC,+CAAe;AAAA,QAAA,CAClC;AACD,YAAI,CAAC,YAAY;AACR,iBAAA;AAAA,QAAA;AAAA,MACT;AAGF,UAAI,+CAAe,aAAa;AACvB,eAAA,EAAE,SAAS,SAAS,KAAK;AAAA,MAAA;AAE3B,aAAA;AAAA,IAAA;AAAA,EACT,CACD;AAEK,QAAA,YAAY,MAAM,YAAY,MAAM;AACxC,WAAO,aAAa,QAAe,EAAE,MAAM,CAAC,QAAQ;AAClD,cAAQ,KAAK,GAAG;AAChB,cAAQ,KAAK,cAAc;AAAA,IAAA,CAC5B;AAAA,EAAA,GACA,CAAC,UAAU,MAAM,CAAC;AAErB,QAAM,4BAA4B,MAAM;AAAA,IACtC,CAAC,UAAiD;AAChD,UAAI,+BAAO,gBAAgB;AACf,kBAAA;AAAA,MAAA;AAAA,IAEd;AAAA,IACA,CAAC,SAAS;AAAA,EACZ;AAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA,EAAE,YAAY,QAAQ;AAAA,IACtB,EAAE,UAAU,CAAC,CAAC,YAAY,EAAE,YAAY,YAAY;AAAA,EACtD;AAEA,kBAAgB,MAAM;AACpB,QAAI,iBAAiB,SAAS;AAC5B;AAAA,IAAA;AAEE,QAAA,CAAC,YAAY,YAAY,UAAU;AAC3B,gBAAA;AACV,uBAAiB,UAAU;AAAA,IAAA;AAAA,EAE5B,GAAA,CAAC,UAAU,WAAW,OAAO,CAAC;AAEjC,MAAI,SAAS,YAAY;AAChB,WAAA;AAAA,MACL,GAAG;AAAA,MACH,KAAK;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,GAAI,YAAY,EAAE,SAAS;AAAA,MAC3B,GAAI,UAAU,EAAE,OAAO;AAAA,MACvB,GAAI,YAAY,EAAE,SAAS;AAAA,MAC3B,GAAI,SAAS,EAAE,MAAM;AAAA,MACrB,GAAI,aAAa,EAAE,UAAU;AAAA,MAC7B,GAAI,WAAW,EAAE,QAAQ;AAAA,MACzB,GAAI,WAAW,EAAE,QAAQ;AAAA,MACzB,GAAI,gBAAgB,EAAE,aAAa;AAAA,MACnC,GAAI,gBAAgB,EAAE,aAAa;AAAA,MACnC,GAAI,gBAAgB,EAAE,aAAa;AAAA,IACrC;AAAA,EAAA;AAII,QAAA,cAAc,CAAC,MAAkB;AACrC,QACE,CAAC,YACD,CAAC,YAAY,CAAC,KACd,CAAC,EAAE,qBACF,CAAC,UAAU,WAAW,YACvB,EAAE,WAAW,GACb;AACA,QAAE,eAAe;AAEjB,gBAAU,MAAM;AACd,2BAAmB,IAAI;AAAA,MAAA,CACxB;AAED,YAAM,QAAQ,OAAO,UAAU,cAAc,MAAM;AAC3C,cAAA;AACN,2BAAmB,KAAK;AAAA,MAAA,CACzB;AAID,aAAO,OAAO,SAAS;AAAA,QACrB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACM;AAAA,IAAA;AAAA,EAEZ;AAGM,QAAA,cAAc,CAAC,MAAkB;AACrC,QAAI,SAAU;AACd,QAAI,SAAS;AACD,gBAAA;AAAA,IAAA;AAAA,EAEd;AAEA,QAAM,mBAAmB;AAEnB,QAAA,cAAc,CAAC,MAAkB;AACrC,QAAI,SAAU;AACR,UAAA,cAAe,EAAE,UAAU,CAAC;AAElC,QAAI,SAAS;AACX,UAAI,YAAY,gBAAgB;AAC9B;AAAA,MAAA;AAGU,kBAAA,iBAAiB,WAAW,MAAM;AAC5C,oBAAY,iBAAiB;AACnB,kBAAA;AAAA,SACT,YAAY;AAAA,IAAA;AAAA,EAEnB;AAEM,QAAA,cAAc,CAAC,MAAkB;AACrC,QAAI,SAAU;AACR,UAAA,cAAe,EAAE,UAAU,CAAC;AAElC,QAAI,YAAY,gBAAgB;AAC9B,mBAAa,YAAY,cAAc;AACvC,kBAAY,iBAAiB;AAAA,IAAA;AAAA,EAEjC;AAEA,QAAM,kBACJ,CAAC,aACD,CAAC,MAA2D;;AAC1D,YAAE,YAAF;AACA,aAAS,OAAO,OAAO,EAAE,QAAQ,CAAC,YAAY;AAC5C,UAAI,EAAE,iBAAkB;AACxB,cAAS,CAAC;AAAA,IAAA,CACX;AAAA,EACH;AAGI,QAAA,sBAA+D,WAChE,iBAAiB,aAAoB,CAAE,CAAA,KAAK,CAAA,IAC7C,CAAC;AAGL,QAAM,wBACJ,WAAW,CAAA,IAAK,iBAAiB,eAAe,CAAA,CAAE;AAEpD,QAAM,oBAAoB;AAAA,IACxB;AAAA,IACA,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,EAErB,EAAA,OAAO,OAAO,EACd,KAAK,GAAG;AAEX,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG,oBAAoB;AAAA,IACvB,GAAG,sBAAsB;AAAA,EAC3B;AAEO,SAAA;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM,WACF,SACA,KAAK,iBACH,OAAO,QAAQ,WAAW,KAAK,eAAe,IAAI,IAClD,OAAO,QAAQ,WAAW,KAAK,IAAI;AAAA,IACzC,KAAK;AAAA,IACL,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;AAAA,IAC/C,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;AAAA,IAC/C,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;AAAA,IACzD,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;AAAA,IACzD,cAAc,gBAAgB,CAAC,cAAc,gBAAgB,CAAC;AAAA,IAC9D,UAAU,CAAC,CAAC;AAAA,IACZ;AAAA,IACA,GAAI,OAAO,KAAK,aAAa,EAAE,UAAU,EAAE,OAAO,cAAc;AAAA,IAChE,GAAI,qBAAqB,EAAE,WAAW,kBAAkB;AAAA,IACxD,GAAI,YAAY;AAAA,MACd,MAAM;AAAA,MACN,iBAAiB;AAAA,IACnB;AAAA,IACA,GAAI,YAAY,EAAE,eAAe,UAAU,gBAAgB,OAAO;AAAA,IAClE,GAAI,mBAAmB,EAAE,sBAAsB,gBAAgB;AAAA,EACjE;AACF;AAkIO,SAAS,WACd,MACsB;AACtB,SAAO,MAAM,WAAW,SAAS,YAAY,OAAO,KAAK;AACvD,+BAAQ,MAAM,EAAA,GAAI,OAAe,UAAU,MAAM,KAAU;AAAA,EAAA,CAC5D;AACH;AAEO,MAAM,OAA2B,MAAM;AAAA,EAC5C,CAAC,OAAO,QAAQ;AACd,UAAM,EAAE,UAAU,GAAG,KAAA,IAAS;AACxB,UAAA;AAAA,MACJ,MAAM;AAAA,MACN,KAAK;AAAA,MACL,GAAG;AAAA,IAAA,IACD,aAAa,MAAa,GAAG;AAEjC,UAAM,WACJ,OAAO,KAAK,aAAa,aACrB,KAAK,SAAS;AAAA,MACZ,UAAW,UAAkB,aAAa,MAAM;AAAA,IAAA,CACjD,IACD,KAAK;AAEP,QAAA,OAAO,aAAa,aAAa;AAGnC,aAAO,UAAU;AAAA,IAAA;AAGnB,WAAO,MAAM;AAAA,MACX,WAAW,WAAW;AAAA,MACtB;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAEJ;AAEA,SAAS,YAAY,GAAe;AAC3B,SAAA,CAAC,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACpD;AAkBa,MAAA,cAAkC,CAAC,YAAY;AACnD,SAAA;AACT;"}