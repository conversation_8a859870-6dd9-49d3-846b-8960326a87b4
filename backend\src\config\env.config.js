// Environment configuration management

const getConfig = () => {
  return {
    // Server Configuration
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseInt(process.env.PORT || '5000', 10),
    
    // Application URLs
    APP_URL: process.env.NODE_ENV === 'production'
      ? (process.env.APP_URL || 'https://url-shortner-qnp4.onrender.com/')
      : `http://localhost:${process.env.PORT || 5000}/`,
    
    FRONTEND_URL: process.env.NODE_ENV === 'production'
      ? (process.env.FRONTEND_URL || 'https://url-shortner-1prr.vercel.app')
      : 'http://localhost:5173',
    
    // Database Configuration
    MONGO_URI: process.env.MONGO_URI,
    
    // Security
    JWT_SECRET: process.env.JWT_SECRET || 'fallback_secret_key_do_not_use_in_production'
  };
};

// Validate required environment variables
const validateConfig = (config) => {
  const requiredVars = ['MONGO_URI'];
  const missingVars = requiredVars.filter(key => !config[key]);
  
  if (config.NODE_ENV === 'production') {
    if (!config.APP_URL) {
      throw new Error('APP_URL is required in production environment');
    }
    if (!config.JWT_SECRET || config.JWT_SECRET === 'fallback_secret_key_do_not_use_in_production') {
      throw new Error('JWT_SECRET must be set in production environment');
    }
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
};

// Export configuration with validation
const config = getConfig();
validateConfig(config);

export default config;