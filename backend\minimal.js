import express from "express";
import cors from "cors";

const app = express();
const PORT = process.env.PORT || 5000;

console.log("Starting minimal server...");

app.use(cors());
app.use(express.json());

app.get("/", (req, res) => {
  res.send("Minimal server is running!");
});

app.get("/test/:id", (req, res) => {
  const { id } = req.params;
  console.log("Test route hit with ID:", id);
  res.json({ message: `Test successful with ID: ${id}` });
});

app.listen(PORT, () => {
  console.log(`✅ Minimal server running on port ${PORT}`);
});
