{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\nimport type { Derived, Store } from '@tanstack/store'\n\nexport * from '@tanstack/store'\n\n/**\n * @private\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Derived<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any> | Derived<TState, any>,\n  selector: (state: NoInfer<TState>) => TSelected = (d) => d as any,\n): TSelected {\n  const slice = useSyncExternalStoreWithSelector(\n    store.subscribe,\n    () => store.state,\n    () => store.state,\n    selector,\n    shallow,\n  )\n\n  return slice\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false\n    for (const v of objA) {\n      if (!objB.has(v)) return false\n    }\n    return true\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n"], "names": [], "mappings": ";;AAkBO,SAAS,SACd,OACA,WAAkD,CAAC,MAAM,GAC9C;AACX,QAAM,QAAQ;AAAA,IACZ,MAAM;AAAA,IACN,MAAM,MAAM;AAAA,IACZ,MAAM,MAAM;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AAEO,SAAA;AACT;AAEgB,SAAA,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AAClB,WAAA;AAAA,EAAA;AAIP,MAAA,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACO,WAAA;AAAA,EAAA;AAGL,MAAA,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAa,QAAA;AACpC,eAAW,CAAC,GAAG,CAAC,KAAK,MAAM;AACzB,UAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,EAAU,QAAA;AAAA,IAAA;AAElD,WAAA;AAAA,EAAA;AAGL,MAAA,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAa,QAAA;AACpC,eAAW,KAAK,MAAM;AACpB,UAAI,CAAC,KAAK,IAAI,CAAC,EAAU,QAAA;AAAA,IAAA;AAEpB,WAAA;AAAA,EAAA;AAGH,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACtC,WAAA;AAAA,EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAEnC,QAAA,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACO,aAAA;AAAA,IAAA;AAAA,EACT;AAEK,SAAA;AACT;"}