{"version": 3, "file": "Asset.js", "sources": ["../../src/Asset.tsx"], "sourcesContent": ["import type { RouterManagedTag } from '@tanstack/router-core'\n\nexport function Asset({ tag, attrs, children }: RouterManagedTag): any {\n  switch (tag) {\n    case 'title':\n      return (\n        <title {...attrs} suppressHydrationWarning>\n          {children}\n        </title>\n      )\n    case 'meta':\n      return <meta {...attrs} suppressHydrationWarning />\n    case 'link':\n      return <link {...attrs} suppressHydrationWarning />\n    case 'style':\n      return (\n        <style\n          {...attrs}\n          dangerouslySetInnerHTML={{ __html: children as any }}\n        />\n      )\n    case 'script':\n      if ((attrs as any) && (attrs as any).src) {\n        return <script {...attrs} suppressHydrationWarning />\n      }\n      if (typeof children === 'string')\n        return (\n          <script\n            {...attrs}\n            dangerouslySetInnerHTML={{\n              __html: children,\n            }}\n            suppressHydrationWarning\n          />\n        )\n      return null\n    default:\n      return null\n  }\n}\n"], "names": [], "mappings": ";AAEO,SAAS,MAAM,EAAE,KAAK,OAAO,YAAmC;AACrE,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,iCACG,SAAO,EAAA,GAAG,OAAO,0BAAwB,MACvC,UACH;AAAA,IAEJ,KAAK;AACH,aAAQ,oBAAA,QAAA,EAAM,GAAG,OAAO,0BAAwB,MAAC;AAAA,IACnD,KAAK;AACH,aAAQ,oBAAA,QAAA,EAAM,GAAG,OAAO,0BAAwB,MAAC;AAAA,IACnD,KAAK;AAED,aAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACE,GAAG;AAAA,UACJ,yBAAyB,EAAE,QAAQ,SAAgB;AAAA,QAAA;AAAA,MACrD;AAAA,IAEJ,KAAK;AACE,UAAA,SAAkB,MAAc,KAAK;AACxC,eAAQ,oBAAA,UAAA,EAAQ,GAAG,OAAO,0BAAwB,MAAC;AAAA,MAAA;AAErD,UAAI,OAAO,aAAa;AAEpB,eAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACE,GAAG;AAAA,YACJ,yBAAyB;AAAA,cACvB,QAAQ;AAAA,YACV;AAAA,YACA,0BAAwB;AAAA,UAAA;AAAA,QAC1B;AAEG,aAAA;AAAA,IACT;AACS,aAAA;AAAA,EAAA;AAEb;"}