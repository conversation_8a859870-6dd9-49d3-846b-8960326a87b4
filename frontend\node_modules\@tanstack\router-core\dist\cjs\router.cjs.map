{"version": 3, "file": "router.cjs", "sources": ["../../src/router.ts"], "sourcesContent": ["import { Store, batch } from '@tanstack/store'\nimport {\n  createBrowserHistory,\n  createMemoryHistory,\n  parseHref,\n} from '@tanstack/history'\nimport invariant from 'tiny-invariant'\nimport {\n  createControlledPromise,\n  deepEqual,\n  functionalUpdate,\n  last,\n  pick,\n  replaceEqualDeep,\n} from './utils'\nimport {\n  cleanPath,\n  interpolatePath,\n  joinPaths,\n  matchPathname,\n  parsePathname,\n  resolvePath,\n  trimPath,\n  trimPathLeft,\n  trimPathRight,\n} from './path'\nimport { isNotFound } from './not-found'\nimport { setupScrollRestoration } from './scroll-restoration'\nimport { defaultParseSearch, defaultStringifySearch } from './searchParams'\nimport { rootRouteId } from './root'\nimport { isRedirect, isResolvedRedirect } from './redirect'\nimport type { SearchParser, SearchSerializer } from './searchParams'\nimport type { AnyRedirect, ResolvedRedirect } from './redirect'\nimport type {\n  HistoryLocation,\n  HistoryState,\n  ParsedHistoryState,\n  RouterHistory,\n} from '@tanstack/history'\nimport type {\n  ControlledPromise,\n  NoInfer,\n  NonNullableUpdater,\n  PickAsRequired,\n  Updater,\n} from './utils'\nimport type { ParsedLocation } from './location'\nimport type { DeferredPromiseState } from './defer'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouteWithContext,\n  BeforeLoadContextOptions,\n  LoaderFnContext,\n  MakeRemountDepsOptionsUnion,\n  RouteContextOptions,\n  RouteMask,\n  SearchMiddleware,\n} from './route'\nimport type {\n  FullSearchSchema,\n  RouteById,\n  RoutePaths,\n  RoutesById,\n  RoutesByPath,\n} from './routeInfo'\nimport type {\n  AnyRouteMatch,\n  MakeRouteMatch,\n  MakeRouteMatchUnion,\n  MatchRouteOptions,\n} from './Matches'\nimport type {\n  BuildLocationFn,\n  CommitLocationOptions,\n  NavigateFn,\n} from './RouterProvider'\nimport type { Manifest } from './manifest'\nimport type { StartSerializer } from './serializer'\nimport type { AnySchema, AnyValidator } from './validators'\nimport type { NavigateOptions, ResolveRelativePath, ToOptions } from './link'\nimport type { NotFoundError } from './not-found'\n\ndeclare global {\n  interface Window {\n    __TSR_ROUTER__?: AnyRouter\n  }\n}\n\nexport type ControllablePromise<T = any> = Promise<T> & {\n  resolve: (value: T) => void\n  reject: (value?: any) => void\n}\n\nexport type InjectedHtmlEntry = Promise<string>\n\nexport interface DefaultRegister {\n  router: AnyRouter\n}\n\nexport interface Register extends DefaultRegister {\n  // router: Router\n}\n\nexport type RegisteredRouter = Register['router']\n\nexport type DefaultRemountDepsFn<TRouteTree extends AnyRoute> = (\n  opts: MakeRemountDepsOptionsUnion<TRouteTree>,\n) => any\n\nexport interface DefaultRouterOptionsExtensions {}\n\nexport interface RouterOptionsExtensions\n  extends DefaultRouterOptionsExtensions {}\n\nexport interface RouterOptions<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean = false,\n  TRouterHistory extends RouterHistory = RouterHistory,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n> extends RouterOptionsExtensions {\n  /**\n   * The history object that will be used to manage the browser history.\n   *\n   * If not provided, a new createBrowserHistory instance will be created and used.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#history-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/history-types)\n   */\n  history?: TRouterHistory\n  /**\n   * A function that will be used to stringify search params when generating links.\n   *\n   * @default defaultStringifySearch\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#stringifysearch-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/custom-search-param-serialization)\n   */\n  stringifySearch?: SearchSerializer\n  /**\n   * A function that will be used to parse search params when parsing the current location.\n   *\n   * @default defaultParseSearch\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#parsesearch-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/custom-search-param-serialization)\n   */\n  parseSearch?: SearchParser\n  /**\n   * If `false`, routes will not be preloaded by default in any way.\n   *\n   * If `'intent'`, routes will be preloaded by default when the user hovers over a link or a `touchstart` event is detected on a `<Link>`.\n   *\n   * If `'viewport'`, routes will be preloaded by default when they are within the viewport.\n   *\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreload-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading)\n   */\n  defaultPreload?: false | 'intent' | 'viewport' | 'render'\n  /**\n   * The delay in milliseconds that a route must be hovered over or touched before it is preloaded.\n   *\n   * @default 50\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreloaddelay-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading#preload-delay)\n   */\n  defaultPreloadDelay?: number\n  /**\n   * The default `pendingMs` a route should use if no pendingMs is provided.\n   *\n   * @default 1000\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpendingms-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#avoiding-pending-component-flash)\n   */\n  defaultPendingMs?: number\n  /**\n   * The default `pendingMinMs` a route should use if no pendingMinMs is provided.\n   *\n   * @default 500\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpendingminms-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#avoiding-pending-component-flash)\n   */\n  defaultPendingMinMs?: number\n  /**\n   * The default `staleTime` a route should use if no staleTime is provided. This is the time in milliseconds that a route will be considered fresh.\n   *\n   * @default 0\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultstaletime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#key-options)\n   */\n  defaultStaleTime?: number\n  /**\n   * The default `preloadStaleTime` a route should use if no preloadStaleTime is provided.\n   *\n   * @default 30_000 `(30 seconds)`\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreloadstaletime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading)\n   */\n  defaultPreloadStaleTime?: number\n  /**\n   * The default `defaultPreloadGcTime` a route should use if no preloadGcTime is provided.\n   *\n   * @default 1_800_000 `(30 minutes)`\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreloadgctime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading)\n   */\n  defaultPreloadGcTime?: number\n  /**\n   * If `true`, route navigations will called using `document.startViewTransition()`.\n   *\n   * If the browser does not support this api, this option will be ignored.\n   *\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Document/startViewTransition) for more information on how this function works.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultviewtransition-property)\n   */\n  defaultViewTransition?: boolean | ViewTransitionOptions\n  /**\n   * The default `hashScrollIntoView` a route should use if no hashScrollIntoView is provided while navigating\n   *\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView) for more information on `ScrollIntoViewOptions`.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaulthashscrollintoview-property)\n   */\n  defaultHashScrollIntoView?: boolean | ScrollIntoViewOptions\n  /**\n   * @default 'fuzzy'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#notfoundmode-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/not-found-errors#the-notfoundmode-option)\n   */\n  notFoundMode?: 'root' | 'fuzzy'\n  /**\n   * The default `gcTime` a route should use if no gcTime is provided.\n   *\n   * @default 1_800_000 `(30 minutes)`\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultgctime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#key-options)\n   */\n  defaultGcTime?: number\n  /**\n   * If `true`, all routes will be matched as case-sensitive.\n   *\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#casesensitive-property)\n   */\n  caseSensitive?: boolean\n  /**\n   *\n   * The route tree that will be used to configure the router instance.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#routetree-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/routing/route-trees)\n   */\n  routeTree?: TRouteTree\n  /**\n   * The basepath for then entire router. This is useful for mounting a router instance at a subpath.\n   *\n   * @default '/'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#basepath-property)\n   */\n  basepath?: string\n  /**\n   * The root context that will be provided to all routes in the route tree.\n   *\n   * This can be used to provide a context to all routes in the tree without having to provide it to each route individually.\n   *\n   * Optional or required if the root route was created with [`createRootRouteWithContext()`](https://tanstack.com/router/latest/docs/framework/react/api/router/createRootRouteWithContextFunction).\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#context-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/router-context)\n   */\n  context?: InferRouterContext<TRouteTree>\n  /**\n   * A function that will be called when the router is dehydrated.\n   *\n   * The return value of this function will be serialized and stored in the router's dehydrated state.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#dehydrate-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/external-data-loading#critical-dehydrationhydration)\n   */\n  dehydrate?: () => TDehydrated\n  /**\n   * A function that will be called when the router is hydrated.\n   *\n   * The return value of this function will be serialized and stored in the router's dehydrated state.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#hydrate-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/external-data-loading#critical-dehydrationhydration)\n   */\n  hydrate?: (dehydrated: TDehydrated) => void\n  /**\n   * An array of route masks that will be used to mask routes in the route tree.\n   *\n   * Route masking is when you display a route at a different path than the one it is configured to match, like a modal popup that when shared will unmask to the modal's content instead of the modal's context.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#routemasks-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/route-masking)\n   */\n  routeMasks?: Array<RouteMask<TRouteTree>>\n  /**\n   * If `true`, route masks will, by default, be removed when the page is reloaded.\n   *\n   * This can be overridden on a per-mask basis by setting the `unmaskOnReload` option on the mask, or on a per-navigation basis by setting the `unmaskOnReload` option in the `Navigate` options.\n   *\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#unmaskonreload-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/route-masking#unmasking-on-page-reload)\n   */\n  unmaskOnReload?: boolean\n\n  /**\n   * Use `notFoundComponent` instead.\n   *\n   * @deprecated\n   * See https://tanstack.com/router/v1/docs/guide/not-found-errors#migrating-from-notfoundroute for more info.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#notfoundroute-property)\n   */\n  notFoundRoute?: AnyRoute\n  /**\n   * Configures how trailing slashes are treated.\n   *\n   * - `'always'` will add a trailing slash if not present\n   * - `'never'` will remove the trailing slash if present\n   * - `'preserve'` will not modify the trailing slash.\n   *\n   * @default 'never'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#trailingslash-property)\n   */\n  trailingSlash?: TTrailingSlashOption\n  /**\n   * While usually automatic, sometimes it can be useful to force the router into a server-side state, e.g. when using the router in a non-browser environment that has access to a global.document object.\n   *\n   * @default typeof document !== 'undefined'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#isserver-property)\n   */\n  isServer?: boolean\n\n  defaultSsr?: boolean\n\n  search?: {\n    /**\n     * Configures how unknown search params (= not returned by any `validateSearch`) are treated.\n     *\n     * @default false\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#search.strict-property)\n     */\n    strict?: boolean\n  }\n\n  /**\n   * Configures whether structural sharing is enabled by default for fine-grained selectors.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultstructuralsharing-property)\n   */\n  defaultStructuralSharing?: TDefaultStructuralSharingOption\n\n  /**\n   * Configures which URI characters are allowed in path params that would ordinarily be escaped by encodeURIComponent.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#pathparamsallowedcharacters-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/path-params#allowed-characters)\n   */\n  pathParamsAllowedCharacters?: Array<\n    ';' | ':' | '@' | '&' | '=' | '+' | '$' | ','\n  >\n\n  defaultRemountDeps?: DefaultRemountDepsFn<TRouteTree>\n\n  /**\n   * If `true`, scroll restoration will be enabled\n   *\n   * @default false\n   */\n  scrollRestoration?: boolean\n\n  /**\n   * A function that will be called to get the key for the scroll restoration cache.\n   *\n   * @default (location) => location.href\n   */\n  getScrollRestorationKey?: (location: ParsedLocation) => string\n  /**\n   * The default behavior for scroll restoration.\n   *\n   * @default 'auto'\n   */\n  scrollRestorationBehavior?: ScrollBehavior\n  /**\n   * An array of selectors that will be used to scroll to the top of the page in addition to `window`\n   *\n   * @default ['window']\n   */\n  scrollToTopSelectors?: Array<string | (() => Element | null | undefined)>\n}\n\nexport interface RouterState<\n  in out TRouteTree extends AnyRoute = AnyRoute,\n  in out TRouteMatch = MakeRouteMatchUnion,\n> {\n  status: 'pending' | 'idle'\n  loadedAt: number\n  isLoading: boolean\n  isTransitioning: boolean\n  matches: Array<TRouteMatch>\n  pendingMatches?: Array<TRouteMatch>\n  cachedMatches: Array<TRouteMatch>\n  location: ParsedLocation<FullSearchSchema<TRouteTree>>\n  resolvedLocation?: ParsedLocation<FullSearchSchema<TRouteTree>>\n  statusCode: number\n  redirect?: ResolvedRedirect\n}\n\nexport interface BuildNextOptions {\n  to?: string | number | null\n  params?: true | Updater<unknown>\n  search?: true | Updater<unknown>\n  hash?: true | Updater<string>\n  state?: true | NonNullableUpdater<ParsedHistoryState, HistoryState>\n  mask?: {\n    to?: string | number | null\n    params?: true | Updater<unknown>\n    search?: true | Updater<unknown>\n    hash?: true | Updater<string>\n    state?: true | NonNullableUpdater<ParsedHistoryState, HistoryState>\n    unmaskOnReload?: boolean\n  }\n  from?: string\n  _fromLocation?: ParsedLocation\n  href?: string\n}\n\ntype NavigationEventInfo = {\n  fromLocation?: ParsedLocation\n  toLocation: ParsedLocation\n  pathChanged: boolean\n  hrefChanged: boolean\n  hashChanged: boolean\n}\n\nexport type RouterEvents = {\n  onBeforeNavigate: {\n    type: 'onBeforeNavigate'\n  } & NavigationEventInfo\n  onBeforeLoad: {\n    type: 'onBeforeLoad'\n  } & NavigationEventInfo\n  onLoad: {\n    type: 'onLoad'\n  } & NavigationEventInfo\n  onResolved: {\n    type: 'onResolved'\n  } & NavigationEventInfo\n  onBeforeRouteMount: {\n    type: 'onBeforeRouteMount'\n  } & NavigationEventInfo\n  onInjectedHtml: {\n    type: 'onInjectedHtml'\n    promise: Promise<string>\n  }\n  onRendered: {\n    type: 'onRendered'\n  } & NavigationEventInfo\n}\n\nexport type RouterEvent = RouterEvents[keyof RouterEvents]\n\nexport type ListenerFn<TEvent extends RouterEvent> = (event: TEvent) => void\n\nexport type RouterListener<TRouterEvent extends RouterEvent> = {\n  eventType: TRouterEvent['type']\n  fn: ListenerFn<TRouterEvent>\n}\n\nexport interface MatchRoutesOpts {\n  preload?: boolean\n  throwOnError?: boolean\n  _buildLocation?: boolean\n  dest?: BuildNextOptions\n}\n\nexport type InferRouterContext<TRouteTree extends AnyRoute> =\n  TRouteTree['types']['routerContext']\n\nexport type RouterContextOptions<TRouteTree extends AnyRoute> =\n  AnyContext extends InferRouterContext<TRouteTree>\n    ? {\n        context?: InferRouterContext<TRouteTree>\n      }\n    : {\n        context: InferRouterContext<TRouteTree>\n      }\n\nexport type RouterConstructorOptions<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n  TDehydrated extends Record<string, any>,\n> = Omit<\n  RouterOptions<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory,\n    TDehydrated\n  >,\n  'context'\n> &\n  RouterContextOptions<TRouteTree>\n\nexport interface RouterErrorSerializer<TSerializedError> {\n  serialize: (err: unknown) => TSerializedError\n  deserialize: (err: TSerializedError) => unknown\n}\n\nexport interface MatchedRoutesResult {\n  matchedRoutes: Array<AnyRoute>\n  routeParams: Record<string, string>\n}\n\nexport type PreloadRouteFn<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n> = <\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>(\n  opts: NavigateOptions<\n    RouterCore<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory\n    >,\n    TFrom,\n    TTo,\n    TMaskFrom,\n    TMaskTo\n  >,\n) => Promise<Array<AnyRouteMatch> | undefined>\n\nexport type MatchRouteFn<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n> = <\n  TFrom extends RoutePaths<TRouteTree> = '/',\n  TTo extends string | undefined = undefined,\n  TResolved = ResolveRelativePath<TFrom, NoInfer<TTo>>,\n>(\n  location: ToOptions<\n    RouterCore<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory\n    >,\n    TFrom,\n    TTo\n  >,\n  opts?: MatchRouteOptions,\n) => false | RouteById<TRouteTree, TResolved>['types']['allParams']\n\nexport type UpdateFn<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n  TDehydrated extends Record<string, any>,\n> = (\n  newOptions: RouterConstructorOptions<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory,\n    TDehydrated\n  >,\n) => void\n\nexport type InvalidateFn<TRouter extends AnyRouter> = (opts?: {\n  filter?: (d: MakeRouteMatchUnion<TRouter>) => boolean\n  sync?: boolean\n}) => Promise<void>\n\nexport type ParseLocationFn<TRouteTree extends AnyRoute> = (\n  previousLocation?: ParsedLocation<FullSearchSchema<TRouteTree>>,\n  locationToParse?: HistoryLocation,\n) => ParsedLocation<FullSearchSchema<TRouteTree>>\n\nexport type GetMatchRoutesFn = (\n  next: ParsedLocation,\n  dest?: BuildNextOptions,\n) => {\n  matchedRoutes: Array<AnyRoute>\n  routeParams: Record<string, string>\n  foundRoute: AnyRoute | undefined\n}\n\nexport type EmitFn = (routerEvent: RouterEvent) => void\n\nexport type LoadFn = (opts?: { sync?: boolean }) => Promise<void>\n\nexport type CommitLocationFn = ({\n  viewTransition,\n  ignoreBlocker,\n  ...next\n}: ParsedLocation & CommitLocationOptions) => Promise<void>\n\nexport type StartTransitionFn = (fn: () => void) => void\n\nexport type SubscribeFn = <TType extends keyof RouterEvents>(\n  eventType: TType,\n  fn: ListenerFn<RouterEvents[TType]>,\n) => () => void\n\nexport interface MatchRoutesFn {\n  (\n    pathname: string,\n    locationSearch: AnySchema,\n    opts?: MatchRoutesOpts,\n  ): Array<AnyRouteMatch>\n  (next: ParsedLocation, opts?: MatchRoutesOpts): Array<AnyRouteMatch>\n  (\n    pathnameOrNext: string | ParsedLocation,\n    locationSearchOrOpts?: AnySchema | MatchRoutesOpts,\n    opts?: MatchRoutesOpts,\n  ): Array<AnyRouteMatch>\n}\n\nexport type GetMatchFn = (matchId: string) => AnyRouteMatch | undefined\n\nexport type UpdateMatchFn = (\n  id: string,\n  updater: (match: AnyRouteMatch) => AnyRouteMatch,\n) => AnyRouteMatch\n\nexport type LoadRouteChunkFn = (route: AnyRoute) => Promise<Array<void>>\n\nexport type ResolveRedirect = (err: AnyRedirect) => ResolvedRedirect\n\nexport type ClearCacheFn<TRouter extends AnyRouter> = (opts?: {\n  filter?: (d: MakeRouteMatchUnion<TRouter>) => boolean\n}) => void\n\nexport interface ServerSrr {\n  injectedHtml: Array<InjectedHtmlEntry>\n  injectHtml: (getHtml: () => string | Promise<string>) => Promise<void>\n  injectScript: (\n    getScript: () => string | Promise<string>,\n    opts?: { logScript?: boolean },\n  ) => Promise<void>\n  streamValue: (key: string, value: any) => void\n  streamedKeys: Set<string>\n  onMatchSettled: (opts: { router: AnyRouter; match: AnyRouteMatch }) => any\n}\n\nexport type AnyRouterWithContext<TContext> = RouterCore<\n  AnyRouteWithContext<TContext>,\n  any,\n  any,\n  any,\n  any\n>\n\nexport type AnyRouter = RouterCore<any, any, any, any, any>\n\nexport interface ViewTransitionOptions {\n  types:\n    | Array<string>\n    | ((locationChangeInfo: {\n        fromLocation?: ParsedLocation\n        toLocation: ParsedLocation\n        pathChanged: boolean\n        hrefChanged: boolean\n        hashChanged: boolean\n      }) => Array<string>)\n}\n\nexport function defaultSerializeError(err: unknown) {\n  if (err instanceof Error) {\n    const obj = {\n      name: err.name,\n      message: err.message,\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      ;(obj as any).stack = err.stack\n    }\n\n    return obj\n  }\n\n  return {\n    data: err,\n  }\n}\nexport interface ExtractedBaseEntry {\n  dataType: '__beforeLoadContext' | 'loaderData'\n  type: string\n  path: Array<string>\n  id: number\n  matchIndex: number\n}\n\nexport interface ExtractedStream extends ExtractedBaseEntry {\n  type: 'stream'\n  streamState: StreamState\n}\n\nexport interface ExtractedPromise extends ExtractedBaseEntry {\n  type: 'promise'\n  promiseState: DeferredPromiseState<any>\n}\n\nexport type ExtractedEntry = ExtractedStream | ExtractedPromise\n\nexport type StreamState = {\n  promises: Array<ControlledPromise<string | null>>\n}\n\nexport type TrailingSlashOption = 'always' | 'never' | 'preserve'\n\nexport function getLocationChangeInfo(routerState: {\n  resolvedLocation?: ParsedLocation\n  location: ParsedLocation\n}) {\n  const fromLocation = routerState.resolvedLocation\n  const toLocation = routerState.location\n  const pathChanged = fromLocation?.pathname !== toLocation.pathname\n  const hrefChanged = fromLocation?.href !== toLocation.href\n  const hashChanged = fromLocation?.hash !== toLocation.hash\n  return { fromLocation, toLocation, pathChanged, hrefChanged, hashChanged }\n}\n\nexport type CreateRouterFn = <\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption = 'never',\n  TDefaultStructuralSharingOption extends boolean = false,\n  TRouterHistory extends RouterHistory = RouterHistory,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>(\n  options: undefined extends number\n    ? 'strictNullChecks must be enabled in tsconfig.json'\n    : RouterConstructorOptions<\n        TRouteTree,\n        TTrailingSlashOption,\n        TDefaultStructuralSharingOption,\n        TRouterHistory,\n        TDehydrated\n      >,\n) => RouterCore<\n  TRouteTree,\n  TTrailingSlashOption,\n  TDefaultStructuralSharingOption,\n  TRouterHistory,\n  TDehydrated\n>\n\nexport class RouterCore<\n  in out TRouteTree extends AnyRoute,\n  in out TTrailingSlashOption extends TrailingSlashOption,\n  in out TDefaultStructuralSharingOption extends boolean,\n  in out TRouterHistory extends RouterHistory = RouterHistory,\n  in out TDehydrated extends Record<string, any> = Record<string, any>,\n> {\n  // Option-independent properties\n  tempLocationKey: string | undefined = `${Math.round(\n    Math.random() * 10000000,\n  )}`\n  resetNextScroll = true\n  shouldViewTransition?: boolean | ViewTransitionOptions = undefined\n  isViewTransitionTypesSupported?: boolean = undefined\n  subscribers = new Set<RouterListener<RouterEvent>>()\n  viewTransitionPromise?: ControlledPromise<true>\n  isScrollRestoring = false\n  isScrollRestorationSetup = false\n\n  // Must build in constructor\n  __store!: Store<RouterState<TRouteTree>>\n  options!: PickAsRequired<\n    RouterOptions<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >,\n    'stringifySearch' | 'parseSearch' | 'context'\n  >\n  history!: TRouterHistory\n  latestLocation!: ParsedLocation<FullSearchSchema<TRouteTree>>\n  basepath!: string\n  routeTree!: TRouteTree\n  routesById!: RoutesById<TRouteTree>\n  routesByPath!: RoutesByPath<TRouteTree>\n  flatRoutes!: Array<AnyRoute>\n  isServer!: boolean\n  pathParamsDecodeCharMap?: Map<string, string>\n\n  /**\n   * @deprecated Use the `createRouter` function instead\n   */\n  constructor(\n    options: RouterConstructorOptions<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >,\n  ) {\n    this.update({\n      defaultPreloadDelay: 50,\n      defaultPendingMs: 1000,\n      defaultPendingMinMs: 500,\n      context: undefined!,\n      ...options,\n      caseSensitive: options.caseSensitive ?? false,\n      notFoundMode: options.notFoundMode ?? 'fuzzy',\n      stringifySearch: options.stringifySearch ?? defaultStringifySearch,\n      parseSearch: options.parseSearch ?? defaultParseSearch,\n    })\n\n    if (typeof document !== 'undefined') {\n      ;(window as any).__TSR_ROUTER__ = this\n    }\n  }\n\n  // These are default implementations that can optionally be overridden\n  // by the router provider once rendered. We provide these so that the\n  // router can be used in a non-react environment if necessary\n  startTransition: StartTransitionFn = (fn) => fn()\n\n  update: UpdateFn<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory,\n    TDehydrated\n  > = (newOptions) => {\n    if (newOptions.notFoundRoute) {\n      console.warn(\n        'The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.',\n      )\n    }\n\n    const previousOptions = this.options\n    this.options = {\n      ...this.options,\n      ...newOptions,\n    }\n\n    this.isServer = this.options.isServer ?? typeof document === 'undefined'\n\n    this.pathParamsDecodeCharMap = this.options.pathParamsAllowedCharacters\n      ? new Map(\n          this.options.pathParamsAllowedCharacters.map((char) => [\n            encodeURIComponent(char),\n            char,\n          ]),\n        )\n      : undefined\n\n    if (\n      !this.basepath ||\n      (newOptions.basepath && newOptions.basepath !== previousOptions.basepath)\n    ) {\n      if (\n        newOptions.basepath === undefined ||\n        newOptions.basepath === '' ||\n        newOptions.basepath === '/'\n      ) {\n        this.basepath = '/'\n      } else {\n        this.basepath = `/${trimPath(newOptions.basepath)}`\n      }\n    }\n\n    if (\n      !this.history ||\n      (this.options.history && this.options.history !== this.history)\n    ) {\n      this.history =\n        this.options.history ??\n        ((this.isServer\n          ? createMemoryHistory({\n              initialEntries: [this.basepath || '/'],\n            })\n          : createBrowserHistory()) as TRouterHistory)\n      this.latestLocation = this.parseLocation()\n    }\n\n    if (this.options.routeTree !== this.routeTree) {\n      this.routeTree = this.options.routeTree as TRouteTree\n      this.buildRouteTree()\n    }\n\n    if (!this.__store) {\n      this.__store = new Store(getInitialRouterState(this.latestLocation), {\n        onUpdate: () => {\n          this.__store.state = {\n            ...this.state,\n            cachedMatches: this.state.cachedMatches.filter(\n              (d) => !['redirected'].includes(d.status),\n            ),\n          }\n        },\n      })\n\n      setupScrollRestoration(this)\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      'CSS' in window &&\n      typeof window.CSS?.supports === 'function'\n    ) {\n      this.isViewTransitionTypesSupported = window.CSS.supports(\n        'selector(:active-view-transition-type(a)',\n      )\n    }\n  }\n\n  get state() {\n    return this.__store.state\n  }\n\n  buildRouteTree = () => {\n    this.routesById = {} as RoutesById<TRouteTree>\n    this.routesByPath = {} as RoutesByPath<TRouteTree>\n\n    const notFoundRoute = this.options.notFoundRoute\n    if (notFoundRoute) {\n      notFoundRoute.init({\n        originalIndex: 99999999999,\n        defaultSsr: this.options.defaultSsr,\n      })\n      ;(this.routesById as any)[notFoundRoute.id] = notFoundRoute\n    }\n\n    const recurseRoutes = (childRoutes: Array<AnyRoute>) => {\n      childRoutes.forEach((childRoute, i) => {\n        childRoute.init({\n          originalIndex: i,\n          defaultSsr: this.options.defaultSsr,\n        })\n\n        const existingRoute = (this.routesById as any)[childRoute.id]\n\n        invariant(\n          !existingRoute,\n          `Duplicate routes found with id: ${String(childRoute.id)}`,\n        )\n        ;(this.routesById as any)[childRoute.id] = childRoute\n\n        if (!childRoute.isRoot && childRoute.path) {\n          const trimmedFullPath = trimPathRight(childRoute.fullPath)\n          if (\n            !(this.routesByPath as any)[trimmedFullPath] ||\n            childRoute.fullPath.endsWith('/')\n          ) {\n            ;(this.routesByPath as any)[trimmedFullPath] = childRoute\n          }\n        }\n\n        const children = childRoute.children\n\n        if (children?.length) {\n          recurseRoutes(children)\n        }\n      })\n    }\n\n    recurseRoutes([this.routeTree])\n\n    const scoredRoutes: Array<{\n      child: AnyRoute\n      trimmed: string\n      parsed: ReturnType<typeof parsePathname>\n      index: number\n      scores: Array<number>\n    }> = []\n\n    const routes: Array<AnyRoute> = Object.values(this.routesById)\n\n    routes.forEach((d, i) => {\n      if (d.isRoot || !d.path) {\n        return\n      }\n\n      const trimmed = trimPathLeft(d.fullPath)\n      const parsed = parsePathname(trimmed)\n\n      while (parsed.length > 1 && parsed[0]?.value === '/') {\n        parsed.shift()\n      }\n\n      const scores = parsed.map((segment) => {\n        if (segment.value === '/') {\n          return 0.75\n        }\n\n        if (segment.type === 'param') {\n          return 0.5\n        }\n\n        if (segment.type === 'wildcard') {\n          return 0.25\n        }\n\n        return 1\n      })\n\n      scoredRoutes.push({ child: d, trimmed, parsed, index: i, scores })\n    })\n\n    this.flatRoutes = scoredRoutes\n      .sort((a, b) => {\n        const minLength = Math.min(a.scores.length, b.scores.length)\n\n        // Sort by min available score\n        for (let i = 0; i < minLength; i++) {\n          if (a.scores[i] !== b.scores[i]) {\n            return b.scores[i]! - a.scores[i]!\n          }\n        }\n\n        // Sort by length of score\n        if (a.scores.length !== b.scores.length) {\n          return b.scores.length - a.scores.length\n        }\n\n        // Sort by min available parsed value\n        for (let i = 0; i < minLength; i++) {\n          if (a.parsed[i]!.value !== b.parsed[i]!.value) {\n            return a.parsed[i]!.value > b.parsed[i]!.value ? 1 : -1\n          }\n        }\n\n        // Sort by original index\n        return a.index - b.index\n      })\n      .map((d, i) => {\n        d.child.rank = i\n        return d.child\n      })\n  }\n\n  subscribe: SubscribeFn = (eventType, fn) => {\n    const listener: RouterListener<any> = {\n      eventType,\n      fn,\n    }\n\n    this.subscribers.add(listener)\n\n    return () => {\n      this.subscribers.delete(listener)\n    }\n  }\n\n  emit: EmitFn = (routerEvent) => {\n    this.subscribers.forEach((listener) => {\n      if (listener.eventType === routerEvent.type) {\n        listener.fn(routerEvent)\n      }\n    })\n  }\n\n  parseLocation: ParseLocationFn<TRouteTree> = (\n    previousLocation,\n    locationToParse,\n  ) => {\n    const parse = ({\n      pathname,\n      search,\n      hash,\n      state,\n    }: HistoryLocation): ParsedLocation<FullSearchSchema<TRouteTree>> => {\n      const parsedSearch = this.options.parseSearch(search)\n      const searchStr = this.options.stringifySearch(parsedSearch)\n\n      return {\n        pathname,\n        searchStr,\n        search: replaceEqualDeep(previousLocation?.search, parsedSearch) as any,\n        hash: hash.split('#').reverse()[0] ?? '',\n        href: `${pathname}${searchStr}${hash}`,\n        state: replaceEqualDeep(previousLocation?.state, state),\n      }\n    }\n\n    const location = parse(locationToParse ?? this.history.location)\n\n    const { __tempLocation, __tempKey } = location.state\n\n    if (__tempLocation && (!__tempKey || __tempKey === this.tempLocationKey)) {\n      // Sync up the location keys\n      const parsedTempLocation = parse(__tempLocation) as any\n      parsedTempLocation.state.key = location.state.key\n\n      delete parsedTempLocation.state.__tempLocation\n\n      return {\n        ...parsedTempLocation,\n        maskedLocation: location,\n      }\n    }\n\n    return location\n  }\n\n  resolvePathWithBase = (from: string, path: string) => {\n    const resolvedPath = resolvePath({\n      basepath: this.basepath,\n      base: from,\n      to: cleanPath(path),\n      trailingSlash: this.options.trailingSlash,\n      caseSensitive: this.options.caseSensitive,\n    })\n    return resolvedPath\n  }\n\n  get looseRoutesById() {\n    return this.routesById as Record<string, AnyRoute>\n  }\n\n  /**\n  @deprecated use the following signature instead\n  ```ts\n  matchRoutes (\n    next: ParsedLocation,\n    opts?: { preload?: boolean; throwOnError?: boolean },\n  ): Array<AnyRouteMatch>;\n  ```\n*/\n  matchRoutes: MatchRoutesFn = (\n    pathnameOrNext: string | ParsedLocation,\n    locationSearchOrOpts?: AnySchema | MatchRoutesOpts,\n    opts?: MatchRoutesOpts,\n  ) => {\n    if (typeof pathnameOrNext === 'string') {\n      return this.matchRoutesInternal(\n        {\n          pathname: pathnameOrNext,\n          search: locationSearchOrOpts,\n        } as ParsedLocation,\n        opts,\n      )\n    } else {\n      return this.matchRoutesInternal(pathnameOrNext, locationSearchOrOpts)\n    }\n  }\n\n  private matchRoutesInternal(\n    next: ParsedLocation,\n    opts?: MatchRoutesOpts,\n  ): Array<AnyRouteMatch> {\n    const { foundRoute, matchedRoutes, routeParams } = this.getMatchedRoutes(\n      next,\n      opts?.dest,\n    )\n    let isGlobalNotFound = false\n\n    // Check to see if the route needs a 404 entry\n    if (\n      // If we found a route, and it's not an index route and we have left over path\n      foundRoute\n        ? foundRoute.path !== '/' && routeParams['**']\n        : // Or if we didn't find a route and we have left over path\n          trimPathRight(next.pathname)\n    ) {\n      // If the user has defined an (old) 404 route, use it\n      if (this.options.notFoundRoute) {\n        matchedRoutes.push(this.options.notFoundRoute)\n      } else {\n        // If there is no routes found during path matching\n        isGlobalNotFound = true\n      }\n    }\n\n    const globalNotFoundRouteId = (() => {\n      if (!isGlobalNotFound) {\n        return undefined\n      }\n\n      if (this.options.notFoundMode !== 'root') {\n        for (let i = matchedRoutes.length - 1; i >= 0; i--) {\n          const route = matchedRoutes[i]!\n          if (route.children) {\n            return route.id\n          }\n        }\n      }\n\n      return rootRouteId\n    })()\n\n    const parseErrors = matchedRoutes.map((route) => {\n      let parsedParamsError\n\n      const parseParams =\n        route.options.params?.parse ?? route.options.parseParams\n\n      if (parseParams) {\n        try {\n          const parsedParams = parseParams(routeParams)\n          // Add the parsed params to the accumulated params bag\n          Object.assign(routeParams, parsedParams)\n        } catch (err: any) {\n          parsedParamsError = new PathParamError(err.message, {\n            cause: err,\n          })\n\n          if (opts?.throwOnError) {\n            throw parsedParamsError\n          }\n\n          return parsedParamsError\n        }\n      }\n\n      return\n    })\n\n    const matches: Array<AnyRouteMatch> = []\n\n    const getParentContext = (parentMatch?: AnyRouteMatch) => {\n      const parentMatchId = parentMatch?.id\n\n      const parentContext = !parentMatchId\n        ? ((this.options.context as any) ?? {})\n        : (parentMatch.context ?? this.options.context ?? {})\n\n      return parentContext\n    }\n\n    matchedRoutes.forEach((route, index) => {\n      // Take each matched route and resolve + validate its search params\n      // This has to happen serially because each route's search params\n      // can depend on the parent route's search params\n      // It must also happen before we create the match so that we can\n      // pass the search params to the route's potential key function\n      // which is used to uniquely identify the route match in state\n\n      const parentMatch = matches[index - 1]\n\n      const [preMatchSearch, strictMatchSearch, searchError]: [\n        Record<string, any>,\n        Record<string, any>,\n        any,\n      ] = (() => {\n        // Validate the search params and stabilize them\n        const parentSearch = parentMatch?.search ?? next.search\n        const parentStrictSearch = parentMatch?._strictSearch ?? {}\n\n        try {\n          const strictSearch =\n            validateSearch(route.options.validateSearch, { ...parentSearch }) ??\n            {}\n\n          return [\n            {\n              ...parentSearch,\n              ...strictSearch,\n            },\n            { ...parentStrictSearch, ...strictSearch },\n            undefined,\n          ]\n        } catch (err: any) {\n          let searchParamError = err\n          if (!(err instanceof SearchParamError)) {\n            searchParamError = new SearchParamError(err.message, {\n              cause: err,\n            })\n          }\n\n          if (opts?.throwOnError) {\n            throw searchParamError\n          }\n\n          return [parentSearch, {}, searchParamError]\n        }\n      })()\n\n      // This is where we need to call route.options.loaderDeps() to get any additional\n      // deps that the route's loader function might need to run. We need to do this\n      // before we create the match so that we can pass the deps to the route's\n      // potential key function which is used to uniquely identify the route match in state\n\n      const loaderDeps =\n        route.options.loaderDeps?.({\n          search: preMatchSearch,\n        }) ?? ''\n\n      const loaderDepsHash = loaderDeps ? JSON.stringify(loaderDeps) : ''\n\n      const { usedParams, interpolatedPath } = interpolatePath({\n        path: route.fullPath,\n        params: routeParams,\n        decodeCharMap: this.pathParamsDecodeCharMap,\n      })\n\n      const matchId =\n        interpolatePath({\n          path: route.id,\n          params: routeParams,\n          leaveWildcards: true,\n          decodeCharMap: this.pathParamsDecodeCharMap,\n        }).interpolatedPath + loaderDepsHash\n\n      // Waste not, want not. If we already have a match for this route,\n      // reuse it. This is important for layout routes, which might stick\n      // around between navigation actions that only change leaf routes.\n\n      // Existing matches are matches that are already loaded along with\n      // pending matches that are still loading\n      const existingMatch = this.getMatch(matchId)\n\n      const previousMatch = this.state.matches.find(\n        (d) => d.routeId === route.id,\n      )\n\n      const cause = previousMatch ? 'stay' : 'enter'\n\n      let match: AnyRouteMatch\n\n      if (existingMatch) {\n        match = {\n          ...existingMatch,\n          cause,\n          params: previousMatch\n            ? replaceEqualDeep(previousMatch.params, routeParams)\n            : routeParams,\n          _strictParams: usedParams,\n          search: previousMatch\n            ? replaceEqualDeep(previousMatch.search, preMatchSearch)\n            : replaceEqualDeep(existingMatch.search, preMatchSearch),\n          _strictSearch: strictMatchSearch,\n        }\n      } else {\n        const status =\n          route.options.loader ||\n          route.options.beforeLoad ||\n          route.lazyFn ||\n          routeNeedsPreload(route)\n            ? 'pending'\n            : 'success'\n\n        match = {\n          id: matchId,\n          index,\n          routeId: route.id,\n          params: previousMatch\n            ? replaceEqualDeep(previousMatch.params, routeParams)\n            : routeParams,\n          _strictParams: usedParams,\n          pathname: joinPaths([this.basepath, interpolatedPath]),\n          updatedAt: Date.now(),\n          search: previousMatch\n            ? replaceEqualDeep(previousMatch.search, preMatchSearch)\n            : preMatchSearch,\n          _strictSearch: strictMatchSearch,\n          searchError: undefined,\n          status,\n          isFetching: false,\n          error: undefined,\n          paramsError: parseErrors[index],\n          __routeContext: {},\n          __beforeLoadContext: {},\n          context: {},\n          abortController: new AbortController(),\n          fetchCount: 0,\n          cause,\n          loaderDeps: previousMatch\n            ? replaceEqualDeep(previousMatch.loaderDeps, loaderDeps)\n            : loaderDeps,\n          invalid: false,\n          preload: false,\n          links: undefined,\n          scripts: undefined,\n          headScripts: undefined,\n          meta: undefined,\n          staticData: route.options.staticData || {},\n          loadPromise: createControlledPromise(),\n          fullPath: route.fullPath,\n        }\n      }\n\n      if (!opts?.preload) {\n        // If we have a global not found, mark the right match as global not found\n        match.globalNotFound = globalNotFoundRouteId === route.id\n      }\n\n      // update the searchError if there is one\n      match.searchError = searchError\n\n      const parentContext = getParentContext(parentMatch)\n\n      match.context = {\n        ...parentContext,\n        ...match.__routeContext,\n        ...match.__beforeLoadContext,\n      }\n\n      matches.push(match)\n    })\n\n    matches.forEach((match, index) => {\n      const route = this.looseRoutesById[match.routeId]!\n      const existingMatch = this.getMatch(match.id)\n\n      // only execute `context` if we are not just building a location\n      if (!existingMatch && opts?._buildLocation !== true) {\n        const parentMatch = matches[index - 1]\n        const parentContext = getParentContext(parentMatch)\n\n        // Update the match's context\n        const contextFnContext: RouteContextOptions<any, any, any, any> = {\n          deps: match.loaderDeps,\n          params: match.params,\n          context: parentContext,\n          location: next,\n          navigate: (opts: any) =>\n            this.navigate({ ...opts, _fromLocation: next }),\n          buildLocation: this.buildLocation,\n          cause: match.cause,\n          abortController: match.abortController,\n          preload: !!match.preload,\n          matches,\n        }\n\n        // Get the route context\n        match.__routeContext = route.options.context?.(contextFnContext) ?? {}\n\n        match.context = {\n          ...parentContext,\n          ...match.__routeContext,\n          ...match.__beforeLoadContext,\n        }\n      }\n    })\n\n    return matches\n  }\n\n  getMatchedRoutes: GetMatchRoutesFn = (next, dest) => {\n    let routeParams: Record<string, string> = {}\n    const trimmedPath = trimPathRight(next.pathname)\n    const getMatchedParams = (route: AnyRoute) => {\n      const result = matchPathname(this.basepath, trimmedPath, {\n        to: route.fullPath,\n        caseSensitive:\n          route.options.caseSensitive ?? this.options.caseSensitive,\n        fuzzy: true,\n      })\n      return result\n    }\n\n    let foundRoute: AnyRoute | undefined =\n      dest?.to !== undefined ? this.routesByPath[dest.to!] : undefined\n    if (foundRoute) {\n      routeParams = getMatchedParams(foundRoute)!\n    } else {\n      foundRoute = this.flatRoutes.find((route) => {\n        const matchedParams = getMatchedParams(route)\n\n        if (matchedParams) {\n          routeParams = matchedParams\n          return true\n        }\n\n        return false\n      })\n    }\n\n    let routeCursor: AnyRoute =\n      foundRoute || (this.routesById as any)[rootRouteId]\n\n    const matchedRoutes: Array<AnyRoute> = [routeCursor]\n\n    while (routeCursor.parentRoute) {\n      routeCursor = routeCursor.parentRoute\n      matchedRoutes.unshift(routeCursor)\n    }\n\n    return { matchedRoutes, routeParams, foundRoute }\n  }\n\n  cancelMatch = (id: string) => {\n    const match = this.getMatch(id)\n\n    if (!match) return\n\n    match.abortController.abort()\n    clearTimeout(match.pendingTimeout)\n  }\n\n  cancelMatches = () => {\n    this.state.pendingMatches?.forEach((match) => {\n      this.cancelMatch(match.id)\n    })\n  }\n\n  buildLocation: BuildLocationFn = (opts) => {\n    const build = (\n      dest: BuildNextOptions & {\n        unmaskOnReload?: boolean\n      } = {},\n      matchedRoutesResult?: MatchedRoutesResult,\n    ): ParsedLocation => {\n      const fromMatches = dest._fromLocation\n        ? this.matchRoutes(dest._fromLocation, { _buildLocation: true })\n        : this.state.matches\n\n      const fromMatch =\n        dest.from != null\n          ? fromMatches.find((d) =>\n              matchPathname(this.basepath, trimPathRight(d.pathname), {\n                to: dest.from,\n                caseSensitive: false,\n                fuzzy: false,\n              }),\n            )\n          : undefined\n\n      const fromPath = fromMatch?.pathname || this.latestLocation.pathname\n\n      invariant(\n        dest.from == null || fromMatch != null,\n        'Could not find match for from: ' + dest.from,\n      )\n\n      const fromSearch = this.state.pendingMatches?.length\n        ? last(this.state.pendingMatches)?.search\n        : last(fromMatches)?.search || this.latestLocation.search\n\n      const stayingMatches = matchedRoutesResult?.matchedRoutes.filter((d) =>\n        fromMatches.find((e) => e.routeId === d.id),\n      )\n      let pathname: string\n      if (dest.to) {\n        const resolvePathTo =\n          fromMatch?.fullPath ||\n          last(fromMatches)?.fullPath ||\n          this.latestLocation.pathname\n        pathname = this.resolvePathWithBase(resolvePathTo, `${dest.to}`)\n      } else {\n        const fromRouteByFromPathRouteId =\n          this.routesById[\n            stayingMatches?.find((route) => {\n              const interpolatedPath = interpolatePath({\n                path: route.fullPath,\n                params: matchedRoutesResult?.routeParams ?? {},\n                decodeCharMap: this.pathParamsDecodeCharMap,\n              }).interpolatedPath\n              const pathname = joinPaths([this.basepath, interpolatedPath])\n              return pathname === fromPath\n            })?.id as keyof this['routesById']\n          ]\n        pathname = this.resolvePathWithBase(\n          fromPath,\n          fromRouteByFromPathRouteId?.to ?? fromPath,\n        )\n      }\n\n      const prevParams = { ...last(fromMatches)?.params }\n\n      let nextParams =\n        (dest.params ?? true) === true\n          ? prevParams\n          : {\n              ...prevParams,\n              ...functionalUpdate(dest.params as any, prevParams),\n            }\n\n      if (Object.keys(nextParams).length > 0) {\n        matchedRoutesResult?.matchedRoutes\n          .map((route) => {\n            return (\n              route.options.params?.stringify ?? route.options.stringifyParams\n            )\n          })\n          .filter(Boolean)\n          .forEach((fn) => {\n            nextParams = { ...nextParams!, ...fn!(nextParams) }\n          })\n      }\n\n      pathname = interpolatePath({\n        path: pathname,\n        params: nextParams ?? {},\n        leaveWildcards: false,\n        leaveParams: opts.leaveParams,\n        decodeCharMap: this.pathParamsDecodeCharMap,\n      }).interpolatedPath\n\n      let search = fromSearch\n      if (opts._includeValidateSearch && this.options.search?.strict) {\n        let validatedSearch = {}\n        matchedRoutesResult?.matchedRoutes.forEach((route) => {\n          try {\n            if (route.options.validateSearch) {\n              validatedSearch = {\n                ...validatedSearch,\n                ...(validateSearch(route.options.validateSearch, {\n                  ...validatedSearch,\n                  ...search,\n                }) ?? {}),\n              }\n            }\n          } catch {\n            // ignore errors here because they are already handled in matchRoutes\n          }\n        })\n        search = validatedSearch\n      }\n\n      const applyMiddlewares = (search: any) => {\n        const allMiddlewares =\n          matchedRoutesResult?.matchedRoutes.reduce(\n            (acc, route) => {\n              const middlewares: Array<SearchMiddleware<any>> = []\n              if ('search' in route.options) {\n                if (route.options.search?.middlewares) {\n                  middlewares.push(...route.options.search.middlewares)\n                }\n              }\n              // TODO remove preSearchFilters and postSearchFilters in v2\n              else if (\n                route.options.preSearchFilters ||\n                route.options.postSearchFilters\n              ) {\n                const legacyMiddleware: SearchMiddleware<any> = ({\n                  search,\n                  next,\n                }) => {\n                  let nextSearch = search\n                  if (\n                    'preSearchFilters' in route.options &&\n                    route.options.preSearchFilters\n                  ) {\n                    nextSearch = route.options.preSearchFilters.reduce(\n                      (prev, next) => next(prev),\n                      search,\n                    )\n                  }\n                  const result = next(nextSearch)\n                  if (\n                    'postSearchFilters' in route.options &&\n                    route.options.postSearchFilters\n                  ) {\n                    return route.options.postSearchFilters.reduce(\n                      (prev, next) => next(prev),\n                      result,\n                    )\n                  }\n                  return result\n                }\n                middlewares.push(legacyMiddleware)\n              }\n              if (opts._includeValidateSearch && route.options.validateSearch) {\n                const validate: SearchMiddleware<any> = ({ search, next }) => {\n                  const result = next(search)\n                  try {\n                    const validatedSearch = {\n                      ...result,\n                      ...(validateSearch(\n                        route.options.validateSearch,\n                        result,\n                      ) ?? {}),\n                    }\n                    return validatedSearch\n                  } catch {\n                    // ignore errors here because they are already handled in matchRoutes\n                    return result\n                  }\n                }\n                middlewares.push(validate)\n              }\n              return acc.concat(middlewares)\n            },\n            [] as Array<SearchMiddleware<any>>,\n          ) ?? []\n\n        // the chain ends here since `next` is not called\n        const final: SearchMiddleware<any> = ({ search }) => {\n          if (!dest.search) {\n            return {}\n          }\n          if (dest.search === true) {\n            return search\n          }\n          return functionalUpdate(dest.search, search)\n        }\n        allMiddlewares.push(final)\n\n        const applyNext = (index: number, currentSearch: any): any => {\n          // no more middlewares left, return the current search\n          if (index >= allMiddlewares.length) {\n            return currentSearch\n          }\n\n          const middleware = allMiddlewares[index]!\n\n          const next = (newSearch: any): any => {\n            return applyNext(index + 1, newSearch)\n          }\n\n          return middleware({ search: currentSearch, next })\n        }\n\n        // Start applying middlewares\n        return applyNext(0, search)\n      }\n\n      search = applyMiddlewares(search)\n\n      search = replaceEqualDeep(fromSearch, search)\n      const searchStr = this.options.stringifySearch(search)\n\n      const hash =\n        dest.hash === true\n          ? this.latestLocation.hash\n          : dest.hash\n            ? functionalUpdate(dest.hash, this.latestLocation.hash)\n            : undefined\n\n      const hashStr = hash ? `#${hash}` : ''\n\n      let nextState =\n        dest.state === true\n          ? this.latestLocation.state\n          : dest.state\n            ? functionalUpdate(dest.state, this.latestLocation.state)\n            : {}\n\n      nextState = replaceEqualDeep(this.latestLocation.state, nextState)\n\n      return {\n        pathname,\n        search,\n        searchStr,\n        state: nextState as any,\n        hash: hash ?? '',\n        href: `${pathname}${searchStr}${hashStr}`,\n        unmaskOnReload: dest.unmaskOnReload,\n      }\n    }\n\n    const buildWithMatches = (\n      dest: BuildNextOptions = {},\n      maskedDest?: BuildNextOptions,\n    ) => {\n      const next = build(dest)\n      let maskedNext = maskedDest ? build(maskedDest) : undefined\n\n      if (!maskedNext) {\n        let params = {}\n\n        const foundMask = this.options.routeMasks?.find((d) => {\n          const match = matchPathname(this.basepath, next.pathname, {\n            to: d.from,\n            caseSensitive: false,\n            fuzzy: false,\n          })\n\n          if (match) {\n            params = match\n            return true\n          }\n\n          return false\n        })\n\n        if (foundMask) {\n          const { from: _from, ...maskProps } = foundMask\n          maskedDest = {\n            ...pick(opts, ['from']),\n            ...maskProps,\n            params,\n          }\n          maskedNext = build(maskedDest)\n        }\n      }\n\n      const nextMatches = this.getMatchedRoutes(next, dest)\n      const final = build(dest, nextMatches)\n\n      if (maskedNext) {\n        const maskedMatches = this.getMatchedRoutes(maskedNext, maskedDest)\n        const maskedFinal = build(maskedDest, maskedMatches)\n        final.maskedLocation = maskedFinal\n      }\n\n      return final\n    }\n\n    if (opts.mask) {\n      return buildWithMatches(opts, {\n        ...pick(opts, ['from']),\n        ...opts.mask,\n      })\n    }\n\n    return buildWithMatches(opts)\n  }\n\n  commitLocationPromise: undefined | ControlledPromise<void>\n\n  commitLocation: CommitLocationFn = ({\n    viewTransition,\n    ignoreBlocker,\n    ...next\n  }) => {\n    const isSameState = () => {\n      // the following props are ignored but may still be provided when navigating,\n      // temporarily add the previous values to the next state so they don't affect\n      // the comparison\n      const ignoredProps = [\n        'key',\n        '__TSR_index',\n        '__hashScrollIntoViewOptions',\n      ] as const\n      ignoredProps.forEach((prop) => {\n        ;(next.state as any)[prop] = this.latestLocation.state[prop]\n      })\n      const isEqual = deepEqual(next.state, this.latestLocation.state)\n      ignoredProps.forEach((prop) => {\n        delete next.state[prop]\n      })\n      return isEqual\n    }\n\n    const isSameUrl = this.latestLocation.href === next.href\n\n    const previousCommitPromise = this.commitLocationPromise\n    this.commitLocationPromise = createControlledPromise<void>(() => {\n      previousCommitPromise?.resolve()\n    })\n\n    // Don't commit to history if nothing changed\n    if (isSameUrl && isSameState()) {\n      this.load()\n    } else {\n      // eslint-disable-next-line prefer-const\n      let { maskedLocation, hashScrollIntoView, ...nextHistory } = next\n\n      if (maskedLocation) {\n        nextHistory = {\n          ...maskedLocation,\n          state: {\n            ...maskedLocation.state,\n            __tempKey: undefined,\n            __tempLocation: {\n              ...nextHistory,\n              search: nextHistory.searchStr,\n              state: {\n                ...nextHistory.state,\n                __tempKey: undefined!,\n                __tempLocation: undefined!,\n                key: undefined!,\n              },\n            },\n          },\n        }\n\n        if (\n          nextHistory.unmaskOnReload ??\n          this.options.unmaskOnReload ??\n          false\n        ) {\n          nextHistory.state.__tempKey = this.tempLocationKey\n        }\n      }\n\n      nextHistory.state.__hashScrollIntoViewOptions =\n        hashScrollIntoView ?? this.options.defaultHashScrollIntoView ?? true\n\n      this.shouldViewTransition = viewTransition\n\n      this.history[next.replace ? 'replace' : 'push'](\n        nextHistory.href,\n        nextHistory.state,\n        { ignoreBlocker },\n      )\n    }\n\n    this.resetNextScroll = next.resetScroll ?? true\n\n    if (!this.history.subscribers.size) {\n      this.load()\n    }\n\n    return this.commitLocationPromise\n  }\n\n  buildAndCommitLocation = ({\n    replace,\n    resetScroll,\n    hashScrollIntoView,\n    viewTransition,\n    ignoreBlocker,\n    href,\n    ...rest\n  }: BuildNextOptions & CommitLocationOptions = {}) => {\n    if (href) {\n      const currentIndex = this.history.location.state.__TSR_index\n      const parsed = parseHref(href, {\n        __TSR_index: replace ? currentIndex : currentIndex + 1,\n      })\n      rest.to = parsed.pathname\n      rest.search = this.options.parseSearch(parsed.search)\n      // remove the leading `#` from the hash\n      rest.hash = parsed.hash.slice(1)\n    }\n\n    const location = this.buildLocation({\n      ...(rest as any),\n      _includeValidateSearch: true,\n    })\n    return this.commitLocation({\n      ...location,\n      viewTransition,\n      replace,\n      resetScroll,\n      hashScrollIntoView,\n      ignoreBlocker,\n    })\n  }\n\n  navigate: NavigateFn = ({ to, reloadDocument, href, ...rest }) => {\n    if (reloadDocument) {\n      if (!href) {\n        const location = this.buildLocation({ to, ...rest } as any)\n        href = this.history.createHref(location.href)\n      }\n      if (rest.replace) {\n        window.location.replace(href)\n      } else {\n        window.location.href = href\n      }\n      return\n    }\n\n    return this.buildAndCommitLocation({\n      ...rest,\n      href,\n      to: to as string,\n    })\n  }\n\n  latestLoadPromise: undefined | Promise<void>\n\n  load: LoadFn = async (opts?: { sync?: boolean }): Promise<void> => {\n    this.latestLocation = this.parseLocation(this.latestLocation)\n\n    let redirect: ResolvedRedirect | undefined\n    let notFound: NotFoundError | undefined\n\n    let loadPromise: Promise<void>\n\n    // eslint-disable-next-line prefer-const\n    loadPromise = new Promise<void>((resolve) => {\n      this.startTransition(async () => {\n        try {\n          const next = this.latestLocation\n          const prevLocation = this.state.resolvedLocation\n\n          // Cancel any pending matches\n          this.cancelMatches()\n\n          let pendingMatches!: Array<AnyRouteMatch>\n\n          batch(() => {\n            // this call breaks a route context of destination route after a redirect\n            // we should be fine not eagerly calling this since we call it later\n            // this.clearExpiredCache()\n\n            // Match the routes\n            pendingMatches = this.matchRoutes(next)\n\n            // Ingest the new matches\n            this.__store.setState((s) => ({\n              ...s,\n              status: 'pending',\n              isLoading: true,\n              location: next,\n              pendingMatches,\n              // If a cached moved to pendingMatches, remove it from cachedMatches\n              cachedMatches: s.cachedMatches.filter((d) => {\n                return !pendingMatches.find((e) => e.id === d.id)\n              }),\n            }))\n          })\n\n          if (!this.state.redirect) {\n            this.emit({\n              type: 'onBeforeNavigate',\n              ...getLocationChangeInfo({\n                resolvedLocation: prevLocation,\n                location: next,\n              }),\n            })\n          }\n\n          this.emit({\n            type: 'onBeforeLoad',\n            ...getLocationChangeInfo({\n              resolvedLocation: prevLocation,\n              location: next,\n            }),\n          })\n\n          await this.loadMatches({\n            sync: opts?.sync,\n            matches: pendingMatches,\n            location: next,\n            // eslint-disable-next-line @typescript-eslint/require-await\n            onReady: async () => {\n              // eslint-disable-next-line @typescript-eslint/require-await\n              this.startViewTransition(async () => {\n                // this.viewTransitionPromise = createControlledPromise<true>()\n\n                // Commit the pending matches. If a previous match was\n                // removed, place it in the cachedMatches\n                let exitingMatches!: Array<AnyRouteMatch>\n                let enteringMatches!: Array<AnyRouteMatch>\n                let stayingMatches!: Array<AnyRouteMatch>\n\n                batch(() => {\n                  this.__store.setState((s) => {\n                    const previousMatches = s.matches\n                    const newMatches = s.pendingMatches || s.matches\n\n                    exitingMatches = previousMatches.filter(\n                      (match) => !newMatches.find((d) => d.id === match.id),\n                    )\n                    enteringMatches = newMatches.filter(\n                      (match) =>\n                        !previousMatches.find((d) => d.id === match.id),\n                    )\n                    stayingMatches = previousMatches.filter((match) =>\n                      newMatches.find((d) => d.id === match.id),\n                    )\n\n                    return {\n                      ...s,\n                      isLoading: false,\n                      loadedAt: Date.now(),\n                      matches: newMatches,\n                      pendingMatches: undefined,\n                      cachedMatches: [\n                        ...s.cachedMatches,\n                        ...exitingMatches.filter((d) => d.status !== 'error'),\n                      ],\n                    }\n                  })\n                  this.clearExpiredCache()\n                })\n\n                //\n                ;(\n                  [\n                    [exitingMatches, 'onLeave'],\n                    [enteringMatches, 'onEnter'],\n                    [stayingMatches, 'onStay'],\n                  ] as const\n                ).forEach(([matches, hook]) => {\n                  matches.forEach((match) => {\n                    this.looseRoutesById[match.routeId]!.options[hook]?.(match)\n                  })\n                })\n              })\n            },\n          })\n        } catch (err) {\n          if (isResolvedRedirect(err)) {\n            redirect = err\n            if (!this.isServer) {\n              this.navigate({\n                ...redirect,\n                replace: true,\n                ignoreBlocker: true,\n              })\n            }\n          } else if (isNotFound(err)) {\n            notFound = err\n          }\n\n          this.__store.setState((s) => ({\n            ...s,\n            statusCode: redirect\n              ? redirect.statusCode\n              : notFound\n                ? 404\n                : s.matches.some((d) => d.status === 'error')\n                  ? 500\n                  : 200,\n            redirect,\n          }))\n        }\n\n        if (this.latestLoadPromise === loadPromise) {\n          this.commitLocationPromise?.resolve()\n          this.latestLoadPromise = undefined\n          this.commitLocationPromise = undefined\n        }\n        resolve()\n      })\n    })\n\n    this.latestLoadPromise = loadPromise\n\n    await loadPromise\n\n    while (\n      (this.latestLoadPromise as any) &&\n      loadPromise !== this.latestLoadPromise\n    ) {\n      await this.latestLoadPromise\n    }\n\n    if (this.hasNotFoundMatch()) {\n      this.__store.setState((s) => ({\n        ...s,\n        statusCode: 404,\n      }))\n    }\n  }\n\n  startViewTransition = (fn: () => Promise<void>) => {\n    // Determine if we should start a view transition from the navigation\n    // or from the router default\n    const shouldViewTransition =\n      this.shouldViewTransition ?? this.options.defaultViewTransition\n\n    // Reset the view transition flag\n    delete this.shouldViewTransition\n    // Attempt to start a view transition (or just apply the changes if we can't)\n    if (\n      shouldViewTransition &&\n      typeof document !== 'undefined' &&\n      'startViewTransition' in document &&\n      typeof document.startViewTransition === 'function'\n    ) {\n      // lib.dom.ts doesn't support viewTransition types variant yet.\n      // TODO: Fix this when dom types are updated\n      let startViewTransitionParams: any\n\n      if (\n        typeof shouldViewTransition === 'object' &&\n        this.isViewTransitionTypesSupported\n      ) {\n        const next = this.latestLocation\n        const prevLocation = this.state.resolvedLocation\n\n        const resolvedViewTransitionTypes =\n          typeof shouldViewTransition.types === 'function'\n            ? shouldViewTransition.types(\n                getLocationChangeInfo({\n                  resolvedLocation: prevLocation,\n                  location: next,\n                }),\n              )\n            : shouldViewTransition.types\n\n        startViewTransitionParams = {\n          update: fn,\n          types: resolvedViewTransitionTypes,\n        }\n      } else {\n        startViewTransitionParams = fn\n      }\n\n      document.startViewTransition(startViewTransitionParams)\n    } else {\n      fn()\n    }\n  }\n\n  updateMatch: UpdateMatchFn = (id, updater) => {\n    let updated!: AnyRouteMatch\n    const isPending = this.state.pendingMatches?.find((d) => d.id === id)\n    const isMatched = this.state.matches.find((d) => d.id === id)\n    const isCached = this.state.cachedMatches.find((d) => d.id === id)\n\n    const matchesKey = isPending\n      ? 'pendingMatches'\n      : isMatched\n        ? 'matches'\n        : isCached\n          ? 'cachedMatches'\n          : ''\n\n    if (matchesKey) {\n      this.__store.setState((s) => ({\n        ...s,\n        [matchesKey]: s[matchesKey]?.map((d) =>\n          d.id === id ? (updated = updater(d)) : d,\n        ),\n      }))\n    }\n\n    return updated\n  }\n\n  getMatch: GetMatchFn = (matchId: string) => {\n    return [\n      ...this.state.cachedMatches,\n      ...(this.state.pendingMatches ?? []),\n      ...this.state.matches,\n    ].find((d) => d.id === matchId)\n  }\n\n  loadMatches = async ({\n    location,\n    matches,\n    preload: allPreload,\n    onReady,\n    updateMatch = this.updateMatch,\n    sync,\n  }: {\n    location: ParsedLocation\n    matches: Array<AnyRouteMatch>\n    preload?: boolean\n    onReady?: () => Promise<void>\n    updateMatch?: (\n      id: string,\n      updater: (match: AnyRouteMatch) => AnyRouteMatch,\n    ) => void\n    getMatch?: (matchId: string) => AnyRouteMatch | undefined\n    sync?: boolean\n  }): Promise<Array<MakeRouteMatch>> => {\n    let firstBadMatchIndex: number | undefined\n    let rendered = false\n\n    const triggerOnReady = async () => {\n      if (!rendered) {\n        rendered = true\n        await onReady?.()\n      }\n    }\n\n    const resolvePreload = (matchId: string) => {\n      return !!(allPreload && !this.state.matches.find((d) => d.id === matchId))\n    }\n\n    const handleRedirectAndNotFound = (match: AnyRouteMatch, err: any) => {\n      if (isResolvedRedirect(err)) {\n        if (!err.reloadDocument) {\n          throw err\n        }\n      }\n\n      if (isRedirect(err) || isNotFound(err)) {\n        updateMatch(match.id, (prev) => ({\n          ...prev,\n          status: isRedirect(err)\n            ? 'redirected'\n            : isNotFound(err)\n              ? 'notFound'\n              : 'error',\n          isFetching: false,\n          error: err,\n          beforeLoadPromise: undefined,\n          loaderPromise: undefined,\n        }))\n\n        if (!(err as any).routeId) {\n          ;(err as any).routeId = match.routeId\n        }\n\n        match.beforeLoadPromise?.resolve()\n        match.loaderPromise?.resolve()\n        match.loadPromise?.resolve()\n\n        if (isRedirect(err)) {\n          rendered = true\n          err = this.resolveRedirect({ ...err, _fromLocation: location })\n          throw err\n        } else if (isNotFound(err)) {\n          this._handleNotFound(matches, err, {\n            updateMatch,\n          })\n          this.serverSsr?.onMatchSettled({\n            router: this,\n            match: this.getMatch(match.id)!,\n          })\n          throw err\n        }\n      }\n    }\n\n    try {\n      await new Promise<void>((resolveAll, rejectAll) => {\n        ;(async () => {\n          try {\n            const handleSerialError = (\n              index: number,\n              err: any,\n              routerCode: string,\n            ) => {\n              const { id: matchId, routeId } = matches[index]!\n              const route = this.looseRoutesById[routeId]!\n\n              // Much like suspense, we use a promise here to know if\n              // we've been outdated by a new loadMatches call and\n              // should abort the current async operation\n              if (err instanceof Promise) {\n                throw err\n              }\n\n              err.routerCode = routerCode\n              firstBadMatchIndex = firstBadMatchIndex ?? index\n              handleRedirectAndNotFound(this.getMatch(matchId)!, err)\n\n              try {\n                route.options.onError?.(err)\n              } catch (errorHandlerErr) {\n                err = errorHandlerErr\n                handleRedirectAndNotFound(this.getMatch(matchId)!, err)\n              }\n\n              updateMatch(matchId, (prev) => {\n                prev.beforeLoadPromise?.resolve()\n                prev.loadPromise?.resolve()\n\n                return {\n                  ...prev,\n                  error: err,\n                  status: 'error',\n                  isFetching: false,\n                  updatedAt: Date.now(),\n                  abortController: new AbortController(),\n                  beforeLoadPromise: undefined,\n                }\n              })\n            }\n\n            for (const [index, { id: matchId, routeId }] of matches.entries()) {\n              const existingMatch = this.getMatch(matchId)!\n              const parentMatchId = matches[index - 1]?.id\n\n              const route = this.looseRoutesById[routeId]!\n\n              const pendingMs =\n                route.options.pendingMs ?? this.options.defaultPendingMs\n\n              const shouldPending = !!(\n                onReady &&\n                !this.isServer &&\n                !resolvePreload(matchId) &&\n                (route.options.loader ||\n                  route.options.beforeLoad ||\n                  routeNeedsPreload(route)) &&\n                typeof pendingMs === 'number' &&\n                pendingMs !== Infinity &&\n                (route.options.pendingComponent ??\n                  (this.options as any)?.defaultPendingComponent)\n              )\n\n              let executeBeforeLoad = true\n              if (\n                // If we are in the middle of a load, either of these will be present\n                // (not to be confused with `loadPromise`, which is always defined)\n                existingMatch.beforeLoadPromise ||\n                existingMatch.loaderPromise\n              ) {\n                if (shouldPending) {\n                  setTimeout(() => {\n                    try {\n                      // Update the match and prematurely resolve the loadMatches promise so that\n                      // the pending component can start rendering\n                      triggerOnReady()\n                    } catch {}\n                  }, pendingMs)\n                }\n\n                // Wait for the beforeLoad to resolve before we continue\n                await existingMatch.beforeLoadPromise\n                executeBeforeLoad = this.getMatch(matchId)!.status !== 'success'\n              }\n              if (executeBeforeLoad) {\n                // If we are not in the middle of a load OR the previous load failed, start it\n                try {\n                  updateMatch(matchId, (prev) => {\n                    // explicitly capture the previous loadPromise\n                    const prevLoadPromise = prev.loadPromise\n                    return {\n                      ...prev,\n                      loadPromise: createControlledPromise<void>(() => {\n                        prevLoadPromise?.resolve()\n                      }),\n                      beforeLoadPromise: createControlledPromise<void>(),\n                    }\n                  })\n                  const abortController = new AbortController()\n\n                  let pendingTimeout: ReturnType<typeof setTimeout>\n\n                  if (shouldPending) {\n                    // If we might show a pending component, we need to wait for the\n                    // pending promise to resolve before we start showing that state\n                    pendingTimeout = setTimeout(() => {\n                      try {\n                        // Update the match and prematurely resolve the loadMatches promise so that\n                        // the pending component can start rendering\n                        triggerOnReady()\n                      } catch {}\n                    }, pendingMs)\n                  }\n\n                  const { paramsError, searchError } = this.getMatch(matchId)!\n\n                  if (paramsError) {\n                    handleSerialError(index, paramsError, 'PARSE_PARAMS')\n                  }\n\n                  if (searchError) {\n                    handleSerialError(index, searchError, 'VALIDATE_SEARCH')\n                  }\n\n                  const getParentMatchContext = () =>\n                    parentMatchId\n                      ? this.getMatch(parentMatchId)!.context\n                      : (this.options.context ?? {})\n\n                  updateMatch(matchId, (prev) => ({\n                    ...prev,\n                    isFetching: 'beforeLoad',\n                    fetchCount: prev.fetchCount + 1,\n                    abortController,\n                    pendingTimeout,\n                    context: {\n                      ...getParentMatchContext(),\n                      ...prev.__routeContext,\n                    },\n                  }))\n\n                  const { search, params, context, cause } =\n                    this.getMatch(matchId)!\n\n                  const preload = resolvePreload(matchId)\n\n                  const beforeLoadFnContext: BeforeLoadContextOptions<\n                    any,\n                    any,\n                    any,\n                    any,\n                    any\n                  > = {\n                    search,\n                    abortController,\n                    params,\n                    preload,\n                    context,\n                    location,\n                    navigate: (opts: any) =>\n                      this.navigate({ ...opts, _fromLocation: location }),\n                    buildLocation: this.buildLocation,\n                    cause: preload ? 'preload' : cause,\n                    matches,\n                  }\n\n                  const beforeLoadContext =\n                    (await route.options.beforeLoad?.(beforeLoadFnContext)) ??\n                    {}\n\n                  if (\n                    isRedirect(beforeLoadContext) ||\n                    isNotFound(beforeLoadContext)\n                  ) {\n                    handleSerialError(index, beforeLoadContext, 'BEFORE_LOAD')\n                  }\n\n                  updateMatch(matchId, (prev) => {\n                    return {\n                      ...prev,\n                      __beforeLoadContext: beforeLoadContext,\n                      context: {\n                        ...getParentMatchContext(),\n                        ...prev.__routeContext,\n                        ...beforeLoadContext,\n                      },\n                      abortController,\n                    }\n                  })\n                } catch (err) {\n                  handleSerialError(index, err, 'BEFORE_LOAD')\n                }\n\n                updateMatch(matchId, (prev) => {\n                  prev.beforeLoadPromise?.resolve()\n\n                  return {\n                    ...prev,\n                    beforeLoadPromise: undefined,\n                    isFetching: false,\n                  }\n                })\n              }\n            }\n\n            const validResolvedMatches = matches.slice(0, firstBadMatchIndex)\n            const matchPromises: Array<Promise<AnyRouteMatch>> = []\n\n            validResolvedMatches.forEach(({ id: matchId, routeId }, index) => {\n              matchPromises.push(\n                (async () => {\n                  const { loaderPromise: prevLoaderPromise } =\n                    this.getMatch(matchId)!\n\n                  let loaderShouldRunAsync = false\n                  let loaderIsRunningAsync = false\n\n                  if (prevLoaderPromise) {\n                    await prevLoaderPromise\n                    const match = this.getMatch(matchId)!\n                    if (match.error) {\n                      handleRedirectAndNotFound(match, match.error)\n                    }\n                  } else {\n                    const parentMatchPromise = matchPromises[index - 1] as any\n                    const route = this.looseRoutesById[routeId]!\n\n                    const getLoaderContext = (): LoaderFnContext => {\n                      const {\n                        params,\n                        loaderDeps,\n                        abortController,\n                        context,\n                        cause,\n                      } = this.getMatch(matchId)!\n\n                      const preload = resolvePreload(matchId)\n\n                      return {\n                        params,\n                        deps: loaderDeps,\n                        preload: !!preload,\n                        parentMatchPromise,\n                        abortController: abortController,\n                        context,\n                        location,\n                        navigate: (opts) =>\n                          this.navigate({ ...opts, _fromLocation: location }),\n                        cause: preload ? 'preload' : cause,\n                        route,\n                      }\n                    }\n\n                    // This is where all of the stale-while-revalidate magic happens\n                    const age = Date.now() - this.getMatch(matchId)!.updatedAt\n\n                    const preload = resolvePreload(matchId)\n\n                    const staleAge = preload\n                      ? (route.options.preloadStaleTime ??\n                        this.options.defaultPreloadStaleTime ??\n                        30_000) // 30 seconds for preloads by default\n                      : (route.options.staleTime ??\n                        this.options.defaultStaleTime ??\n                        0)\n\n                    const shouldReloadOption = route.options.shouldReload\n\n                    // Default to reloading the route all the time\n                    // Allow shouldReload to get the last say,\n                    // if provided.\n                    const shouldReload =\n                      typeof shouldReloadOption === 'function'\n                        ? shouldReloadOption(getLoaderContext())\n                        : shouldReloadOption\n\n                    updateMatch(matchId, (prev) => ({\n                      ...prev,\n                      loaderPromise: createControlledPromise<void>(),\n                      preload:\n                        !!preload &&\n                        !this.state.matches.find((d) => d.id === matchId),\n                    }))\n\n                    const executeHead = () => {\n                      const match = this.getMatch(matchId)\n                      // in case of a redirecting match during preload, the match does not exist\n                      if (!match) {\n                        return\n                      }\n                      const assetContext = {\n                        matches,\n                        match,\n                        params: match.params,\n                        loaderData: match.loaderData,\n                      }\n                      const headFnContent = route.options.head?.(assetContext)\n                      const meta = headFnContent?.meta\n                      const links = headFnContent?.links\n                      const headScripts = headFnContent?.scripts\n\n                      const scripts = route.options.scripts?.(assetContext)\n                      const headers = route.options.headers?.(assetContext)\n                      updateMatch(matchId, (prev) => ({\n                        ...prev,\n                        meta,\n                        links,\n                        headScripts,\n                        headers,\n                        scripts,\n                      }))\n                    }\n\n                    const runLoader = async () => {\n                      try {\n                        // If the Matches component rendered\n                        // the pending component and needs to show it for\n                        // a minimum duration, we''ll wait for it to resolve\n                        // before committing to the match and resolving\n                        // the loadPromise\n                        const potentialPendingMinPromise = async () => {\n                          const latestMatch = this.getMatch(matchId)!\n\n                          if (latestMatch.minPendingPromise) {\n                            await latestMatch.minPendingPromise\n                          }\n                        }\n\n                        // Actually run the loader and handle the result\n                        try {\n                          this.loadRouteChunk(route)\n\n                          updateMatch(matchId, (prev) => ({\n                            ...prev,\n                            isFetching: 'loader',\n                          }))\n\n                          // Kick off the loader!\n                          const loaderData =\n                            await route.options.loader?.(getLoaderContext())\n\n                          handleRedirectAndNotFound(\n                            this.getMatch(matchId)!,\n                            loaderData,\n                          )\n\n                          // Lazy option can modify the route options,\n                          // so we need to wait for it to resolve before\n                          // we can use the options\n                          await route._lazyPromise\n\n                          await potentialPendingMinPromise()\n\n                          // Last but not least, wait for the the components\n                          // to be preloaded before we resolve the match\n                          await route._componentsPromise\n\n                          batch(() => {\n                            updateMatch(matchId, (prev) => ({\n                              ...prev,\n                              error: undefined,\n                              status: 'success',\n                              isFetching: false,\n                              updatedAt: Date.now(),\n                              loaderData,\n                            }))\n                            executeHead()\n                          })\n                        } catch (e) {\n                          let error = e\n\n                          await potentialPendingMinPromise()\n\n                          handleRedirectAndNotFound(this.getMatch(matchId)!, e)\n\n                          try {\n                            route.options.onError?.(e)\n                          } catch (onErrorError) {\n                            error = onErrorError\n                            handleRedirectAndNotFound(\n                              this.getMatch(matchId)!,\n                              onErrorError,\n                            )\n                          }\n\n                          batch(() => {\n                            updateMatch(matchId, (prev) => ({\n                              ...prev,\n                              error,\n                              status: 'error',\n                              isFetching: false,\n                            }))\n                            executeHead()\n                          })\n                        }\n\n                        this.serverSsr?.onMatchSettled({\n                          router: this,\n                          match: this.getMatch(matchId)!,\n                        })\n                      } catch (err) {\n                        batch(() => {\n                          updateMatch(matchId, (prev) => ({\n                            ...prev,\n                            loaderPromise: undefined,\n                          }))\n                          executeHead()\n                        })\n                        handleRedirectAndNotFound(this.getMatch(matchId)!, err)\n                      }\n                    }\n\n                    // If the route is successful and still fresh, just resolve\n                    const { status, invalid } = this.getMatch(matchId)!\n                    loaderShouldRunAsync =\n                      status === 'success' &&\n                      (invalid || (shouldReload ?? age > staleAge))\n                    if (preload && route.options.preload === false) {\n                      // Do nothing\n                    } else if (loaderShouldRunAsync && !sync) {\n                      loaderIsRunningAsync = true\n                      ;(async () => {\n                        try {\n                          await runLoader()\n                          const { loaderPromise, loadPromise } =\n                            this.getMatch(matchId)!\n                          loaderPromise?.resolve()\n                          loadPromise?.resolve()\n                          updateMatch(matchId, (prev) => ({\n                            ...prev,\n                            loaderPromise: undefined,\n                          }))\n                        } catch (err) {\n                          if (isResolvedRedirect(err)) {\n                            await this.navigate(err)\n                          }\n                        }\n                      })()\n                    } else if (\n                      status !== 'success' ||\n                      (loaderShouldRunAsync && sync)\n                    ) {\n                      await runLoader()\n                    } else {\n                      // if the loader did not run, still update head.\n                      // reason: parent's beforeLoad may have changed the route context\n                      // and only now do we know the route context (and that the loader would not run)\n                      executeHead()\n                    }\n                  }\n                  if (!loaderIsRunningAsync) {\n                    const { loaderPromise, loadPromise } =\n                      this.getMatch(matchId)!\n                    loaderPromise?.resolve()\n                    loadPromise?.resolve()\n                  }\n\n                  updateMatch(matchId, (prev) => ({\n                    ...prev,\n                    isFetching: loaderIsRunningAsync ? prev.isFetching : false,\n                    loaderPromise: loaderIsRunningAsync\n                      ? prev.loaderPromise\n                      : undefined,\n                    invalid: false,\n                  }))\n                  return this.getMatch(matchId)!\n                })(),\n              )\n            })\n\n            await Promise.all(matchPromises)\n\n            resolveAll()\n          } catch (err) {\n            rejectAll(err)\n          }\n        })()\n      })\n      await triggerOnReady()\n    } catch (err) {\n      if (isRedirect(err) || isNotFound(err)) {\n        if (isNotFound(err) && !allPreload) {\n          await triggerOnReady()\n        }\n\n        throw err\n      }\n    }\n\n    return matches\n  }\n\n  invalidate: InvalidateFn<\n    RouterCore<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >\n  > = (opts) => {\n    const invalidate = (d: MakeRouteMatch<TRouteTree>) => {\n      if (opts?.filter?.(d as MakeRouteMatchUnion<this>) ?? true) {\n        return {\n          ...d,\n          invalid: true,\n          ...(d.status === 'error'\n            ? ({ status: 'pending', error: undefined } as const)\n            : {}),\n        }\n      }\n      return d\n    }\n\n    this.__store.setState((s) => ({\n      ...s,\n      matches: s.matches.map(invalidate),\n      cachedMatches: s.cachedMatches.map(invalidate),\n      pendingMatches: s.pendingMatches?.map(invalidate),\n    }))\n\n    return this.load({ sync: opts?.sync })\n  }\n\n  resolveRedirect = (err: AnyRedirect): ResolvedRedirect => {\n    const redirect = err as ResolvedRedirect\n\n    if (!redirect.href) {\n      redirect.href = this.buildLocation(redirect as any).href\n    }\n\n    return redirect\n  }\n\n  clearCache: ClearCacheFn<this> = (opts) => {\n    const filter = opts?.filter\n    if (filter !== undefined) {\n      this.__store.setState((s) => {\n        return {\n          ...s,\n          cachedMatches: s.cachedMatches.filter(\n            (m) => !filter(m as MakeRouteMatchUnion<this>),\n          ),\n        }\n      })\n    } else {\n      this.__store.setState((s) => {\n        return {\n          ...s,\n          cachedMatches: [],\n        }\n      })\n    }\n  }\n\n  clearExpiredCache = () => {\n    // This is where all of the garbage collection magic happens\n    const filter = (d: MakeRouteMatch<TRouteTree>) => {\n      const route = this.looseRoutesById[d.routeId]!\n\n      if (!route.options.loader) {\n        return true\n      }\n\n      // If the route was preloaded, use the preloadGcTime\n      // otherwise, use the gcTime\n      const gcTime =\n        (d.preload\n          ? (route.options.preloadGcTime ?? this.options.defaultPreloadGcTime)\n          : (route.options.gcTime ?? this.options.defaultGcTime)) ??\n        5 * 60 * 1000\n\n      return !(d.status !== 'error' && Date.now() - d.updatedAt < gcTime)\n    }\n    this.clearCache({ filter })\n  }\n\n  loadRouteChunk = (route: AnyRoute) => {\n    if (route._lazyPromise === undefined) {\n      if (route.lazyFn) {\n        route._lazyPromise = route.lazyFn().then((lazyRoute) => {\n          // explicitly don't copy over the lazy route's id\n          const { id: _id, ...options } = lazyRoute.options\n          Object.assign(route.options, options)\n        })\n      } else {\n        route._lazyPromise = Promise.resolve()\n      }\n    }\n\n    // If for some reason lazy resolves more lazy components...\n    // We'll wait for that before pre attempt to preload any\n    // components themselves.\n    if (route._componentsPromise === undefined) {\n      route._componentsPromise = route._lazyPromise.then(() =>\n        Promise.all(\n          componentTypes.map(async (type) => {\n            const component = route.options[type]\n            if ((component as any)?.preload) {\n              await (component as any).preload()\n            }\n          }),\n        ),\n      )\n    }\n    return route._componentsPromise\n  }\n\n  preloadRoute: PreloadRouteFn<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory\n  > = async (opts) => {\n    const next = this.buildLocation(opts as any)\n\n    let matches = this.matchRoutes(next, {\n      throwOnError: true,\n      preload: true,\n      dest: opts,\n    })\n\n    const activeMatchIds = new Set(\n      [...this.state.matches, ...(this.state.pendingMatches ?? [])].map(\n        (d) => d.id,\n      ),\n    )\n\n    const loadedMatchIds = new Set([\n      ...activeMatchIds,\n      ...this.state.cachedMatches.map((d) => d.id),\n    ])\n\n    // If the matches are already loaded, we need to add them to the cachedMatches\n    batch(() => {\n      matches.forEach((match) => {\n        if (!loadedMatchIds.has(match.id)) {\n          this.__store.setState((s) => ({\n            ...s,\n            cachedMatches: [...(s.cachedMatches as any), match],\n          }))\n        }\n      })\n    })\n\n    try {\n      matches = await this.loadMatches({\n        matches,\n        location: next,\n        preload: true,\n        updateMatch: (id, updater) => {\n          // Don't update the match if it's currently loaded\n          if (activeMatchIds.has(id)) {\n            matches = matches.map((d) => (d.id === id ? updater(d) : d))\n          } else {\n            this.updateMatch(id, updater)\n          }\n        },\n      })\n\n      return matches\n    } catch (err) {\n      if (isRedirect(err)) {\n        if (err.reloadDocument) {\n          return undefined\n        }\n        return await this.preloadRoute({\n          ...(err as any),\n          _fromLocation: next,\n        })\n      }\n      if (!isNotFound(err)) {\n        // Preload errors are not fatal, but we should still log them\n        console.error(err)\n      }\n      return undefined\n    }\n  }\n\n  matchRoute: MatchRouteFn<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory\n  > = (location, opts) => {\n    const matchLocation = {\n      ...location,\n      to: location.to\n        ? this.resolvePathWithBase(\n            (location.from || '') as string,\n            location.to as string,\n          )\n        : undefined,\n      params: location.params || {},\n      leaveParams: true,\n    }\n    const next = this.buildLocation(matchLocation as any)\n\n    if (opts?.pending && this.state.status !== 'pending') {\n      return false\n    }\n\n    const pending =\n      opts?.pending === undefined ? !this.state.isLoading : opts.pending\n\n    const baseLocation = pending\n      ? this.latestLocation\n      : this.state.resolvedLocation || this.state.location\n\n    const match = matchPathname(this.basepath, baseLocation.pathname, {\n      ...opts,\n      to: next.pathname,\n    }) as any\n\n    if (!match) {\n      return false\n    }\n    if (location.params) {\n      if (!deepEqual(match, location.params, { partial: true })) {\n        return false\n      }\n    }\n\n    if (match && (opts?.includeSearch ?? true)) {\n      return deepEqual(baseLocation.search, next.search, { partial: true })\n        ? match\n        : false\n    }\n\n    return match\n  }\n\n  ssr?: {\n    manifest: Manifest | undefined\n    serializer: StartSerializer\n  }\n\n  serverSsr?: {\n    injectedHtml: Array<InjectedHtmlEntry>\n    injectHtml: (getHtml: () => string | Promise<string>) => Promise<void>\n    injectScript: (\n      getScript: () => string | Promise<string>,\n      opts?: { logScript?: boolean },\n    ) => Promise<void>\n    streamValue: (key: string, value: any) => void\n    streamedKeys: Set<string>\n    onMatchSettled: (opts: { router: AnyRouter; match: AnyRouteMatch }) => any\n  }\n\n  clientSsr?: {\n    getStreamedValue: <T>(key: string) => T | undefined\n  }\n\n  _handleNotFound = (\n    matches: Array<AnyRouteMatch>,\n    err: NotFoundError,\n    {\n      updateMatch = this.updateMatch,\n    }: {\n      updateMatch?: (\n        id: string,\n        updater: (match: AnyRouteMatch) => AnyRouteMatch,\n      ) => void\n    } = {},\n  ) => {\n    // Find the route that should handle the not found error\n    // First check if a specific route is requested to show the error\n    const routeCursor = this.routesById[err.routeId ?? ''] ?? this.routeTree\n    const matchesByRouteId: Record<string, AnyRouteMatch> = {}\n\n    // Setup routesByRouteId object for quick access\n    for (const match of matches) {\n      matchesByRouteId[match.routeId] = match\n    }\n\n    // Ensure a NotFoundComponent exists on the route\n    if (\n      !routeCursor.options.notFoundComponent &&\n      (this.options as any)?.defaultNotFoundComponent\n    ) {\n      routeCursor.options.notFoundComponent = (\n        this.options as any\n      ).defaultNotFoundComponent\n    }\n\n    // Ensure we have a notFoundComponent\n    invariant(\n      routeCursor.options.notFoundComponent,\n      'No notFoundComponent found. Please set a notFoundComponent on your route or provide a defaultNotFoundComponent to the router.',\n    )\n\n    // Find the match for this route\n    const matchForRoute = matchesByRouteId[routeCursor.id]\n\n    invariant(\n      matchForRoute,\n      'Could not find match for route: ' + routeCursor.id,\n    )\n\n    // Assign the error to the match - using non-null assertion since we've checked with invariant\n    updateMatch(matchForRoute.id, (prev) => ({\n      ...prev,\n      status: 'notFound',\n      error: err,\n      isFetching: false,\n    }))\n\n    if ((err as any).routerCode === 'BEFORE_LOAD' && routeCursor.parentRoute) {\n      err.routeId = routeCursor.parentRoute.id\n      this._handleNotFound(matches, err, {\n        updateMatch,\n      })\n    }\n  }\n\n  hasNotFoundMatch = () => {\n    return this.__store.state.matches.some(\n      (d) => d.status === 'notFound' || d.globalNotFound,\n    )\n  }\n}\n\nexport class SearchParamError extends Error {}\n\nexport class PathParamError extends Error {}\n\n// A function that takes an import() argument which is a function and returns a new function that will\n// proxy arguments from the caller to the imported function, retaining all type\n// information along the way\nexport function lazyFn<\n  T extends Record<string, (...args: Array<any>) => any>,\n  TKey extends keyof T = 'default',\n>(fn: () => Promise<T>, key?: TKey) {\n  return async (\n    ...args: Parameters<T[TKey]>\n  ): Promise<Awaited<ReturnType<T[TKey]>>> => {\n    const imported = await fn()\n    return imported[key || 'default'](...args)\n  }\n}\n\nexport function getInitialRouterState(\n  location: ParsedLocation,\n): RouterState<any> {\n  return {\n    loadedAt: 0,\n    isLoading: false,\n    isTransitioning: false,\n    status: 'idle',\n    resolvedLocation: undefined,\n    location,\n    matches: [],\n    pendingMatches: [],\n    cachedMatches: [],\n    statusCode: 200,\n  }\n}\n\nfunction validateSearch(validateSearch: AnyValidator, input: unknown): unknown {\n  if (validateSearch == null) return {}\n\n  if ('~standard' in validateSearch) {\n    const result = validateSearch['~standard'].validate(input)\n\n    if (result instanceof Promise)\n      throw new SearchParamError('Async validation not supported')\n\n    if (result.issues)\n      throw new SearchParamError(JSON.stringify(result.issues, undefined, 2), {\n        cause: result,\n      })\n\n    return result.value\n  }\n\n  if ('parse' in validateSearch) {\n    return validateSearch.parse(input)\n  }\n\n  if (typeof validateSearch === 'function') {\n    return validateSearch(input)\n  }\n\n  return {}\n}\n\nexport const componentTypes = [\n  'component',\n  'errorComponent',\n  'pendingComponent',\n  'notFoundComponent',\n] as const\n\nfunction routeNeedsPreload(route: AnyRoute) {\n  for (const componentType of componentTypes) {\n    if ((route.options[componentType] as any)?.preload) {\n      return true\n    }\n  }\n  return false\n}\n"], "names": ["trimPath", "createMemoryHistory", "createBrowserHistory", "Store", "setupScrollRestoration", "trimPathRight", "trimPathLeft", "parsePathname", "replaceEqualDeep", "path", "<PERSON><PERSON><PERSON>", "cleanPath", "matchPathname", "rootRouteId", "last", "interpolate<PERSON><PERSON>", "pathname", "joinPaths", "functionalUpdate", "_a", "search", "next", "pick", "deepEqual", "createControlledPromise", "parseHref", "redirect", "notFound", "batch", "isResolvedRedirect", "isNotFound", "isRedirect", "_b", "preload", "_d", "_c", "options", "defaultStringifySearch", "defaultParseSearch", "opts", "validateSearch"], "mappings": ";;;;;;;;;;;;AA2qBO,SAAS,sBAAsB,KAAc;AAClD,MAAI,eAAe,OAAO;AACxB,UAAM,MAAM;AAAA,MACV,MAAM,IAAI;AAAA,MACV,SAAS,IAAI;AAAA,IACf;AAEI,QAAA,QAAQ,IAAI,aAAa,eAAe;AACxC,UAAY,QAAQ,IAAI;AAAA,IAAA;AAGrB,WAAA;AAAA,EAAA;AAGF,SAAA;AAAA,IACL,MAAM;AAAA,EACR;AACF;AA2BO,SAAS,sBAAsB,aAGnC;AACD,QAAM,eAAe,YAAY;AACjC,QAAM,aAAa,YAAY;AACzB,QAAA,eAAc,6CAAc,cAAa,WAAW;AACpD,QAAA,eAAc,6CAAc,UAAS,WAAW;AAChD,QAAA,eAAc,6CAAc,UAAS,WAAW;AACtD,SAAO,EAAE,cAAc,YAAY,aAAa,aAAa,YAAY;AAC3E;AA0BO,MAAM,WAMX;AAAA;AAAA;AAAA;AAAA,EAsCA,YACE,SAOA;AA5CF,SAAA,kBAAsC,GAAG,KAAK;AAAA,MAC5C,KAAK,WAAW;AAAA,IAAA,CACjB;AACiB,SAAA,kBAAA;AACuC,SAAA,uBAAA;AACd,SAAA,iCAAA;AAC3C,SAAA,kCAAkB,IAAiC;AAE/B,SAAA,oBAAA;AACO,SAAA,2BAAA;AAwDU,SAAA,kBAAA,CAAC,OAAO,GAAG;AAEhD,SAAA,SAMI,CAAC,eAAe;;AAClB,UAAI,WAAW,eAAe;AACpB,gBAAA;AAAA,UACN;AAAA,QACF;AAAA,MAAA;AAGF,YAAM,kBAAkB,KAAK;AAC7B,WAAK,UAAU;AAAA,QACb,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MACL;AAEA,WAAK,WAAW,KAAK,QAAQ,YAAY,OAAO,aAAa;AAE7D,WAAK,0BAA0B,KAAK,QAAQ,8BACxC,IAAI;AAAA,QACF,KAAK,QAAQ,4BAA4B,IAAI,CAAC,SAAS;AAAA,UACrD,mBAAmB,IAAI;AAAA,UACvB;AAAA,QACD,CAAA;AAAA,MAAA,IAEH;AAGF,UAAA,CAAC,KAAK,YACL,WAAW,YAAY,WAAW,aAAa,gBAAgB,UAChE;AAEE,YAAA,WAAW,aAAa,UACxB,WAAW,aAAa,MACxB,WAAW,aAAa,KACxB;AACA,eAAK,WAAW;AAAA,QAAA,OACX;AACL,eAAK,WAAW,IAAIA,KAAS,SAAA,WAAW,QAAQ,CAAC;AAAA,QAAA;AAAA,MACnD;AAIA,UAAA,CAAC,KAAK,WACL,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,KAAK,SACvD;AACA,aAAK,UACH,KAAK,QAAQ,YACX,KAAK,WACHC,4BAAoB;AAAA,UAClB,gBAAgB,CAAC,KAAK,YAAY,GAAG;AAAA,QAAA,CACtC,IACDC,QAAqB,qBAAA;AACtB,aAAA,iBAAiB,KAAK,cAAc;AAAA,MAAA;AAG3C,UAAI,KAAK,QAAQ,cAAc,KAAK,WAAW;AACxC,aAAA,YAAY,KAAK,QAAQ;AAC9B,aAAK,eAAe;AAAA,MAAA;AAGlB,UAAA,CAAC,KAAK,SAAS;AACjB,aAAK,UAAU,IAAIC,MAAAA,MAAM,sBAAsB,KAAK,cAAc,GAAG;AAAA,UACnE,UAAU,MAAM;AACd,iBAAK,QAAQ,QAAQ;AAAA,cACnB,GAAG,KAAK;AAAA,cACR,eAAe,KAAK,MAAM,cAAc;AAAA,gBACtC,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM;AAAA,cAAA;AAAA,YAE5C;AAAA,UAAA;AAAA,QACF,CACD;AAEDC,0BAAAA,uBAAuB,IAAI;AAAA,MAAA;AAI3B,UAAA,OAAO,WAAW,eAClB,SAAS,UACT,SAAO,YAAO,QAAP,mBAAY,cAAa,YAChC;AACK,aAAA,iCAAiC,OAAO,IAAI;AAAA,UAC/C;AAAA,QACF;AAAA,MAAA;AAAA,IAEJ;AAMA,SAAA,iBAAiB,MAAM;AACrB,WAAK,aAAa,CAAC;AACnB,WAAK,eAAe,CAAC;AAEf,YAAA,gBAAgB,KAAK,QAAQ;AACnC,UAAI,eAAe;AACjB,sBAAc,KAAK;AAAA,UACjB,eAAe;AAAA,UACf,YAAY,KAAK,QAAQ;AAAA,QAAA,CAC1B;AACC,aAAK,WAAmB,cAAc,EAAE,IAAI;AAAA,MAAA;AAG1C,YAAA,gBAAgB,CAAC,gBAAiC;AAC1C,oBAAA,QAAQ,CAAC,YAAY,MAAM;AACrC,qBAAW,KAAK;AAAA,YACd,eAAe;AAAA,YACf,YAAY,KAAK,QAAQ;AAAA,UAAA,CAC1B;AAED,gBAAM,gBAAiB,KAAK,WAAmB,WAAW,EAAE;AAE5D;AAAA,YACE,CAAC;AAAA,YACD,mCAAmC,OAAO,WAAW,EAAE,CAAC;AAAA,UAC1D;AACE,eAAK,WAAmB,WAAW,EAAE,IAAI;AAE3C,cAAI,CAAC,WAAW,UAAU,WAAW,MAAM;AACnC,kBAAA,kBAAkBC,KAAAA,cAAc,WAAW,QAAQ;AAEvD,gBAAA,CAAE,KAAK,aAAqB,eAAe,KAC3C,WAAW,SAAS,SAAS,GAAG,GAChC;AACE,mBAAK,aAAqB,eAAe,IAAI;AAAA,YAAA;AAAA,UACjD;AAGF,gBAAM,WAAW,WAAW;AAE5B,cAAI,qCAAU,QAAQ;AACpB,0BAAc,QAAQ;AAAA,UAAA;AAAA,QACxB,CACD;AAAA,MACH;AAEc,oBAAA,CAAC,KAAK,SAAS,CAAC;AAE9B,YAAM,eAMD,CAAC;AAEN,YAAM,SAA0B,OAAO,OAAO,KAAK,UAAU;AAEtD,aAAA,QAAQ,CAAC,GAAG,MAAM;;AACvB,YAAI,EAAE,UAAU,CAAC,EAAE,MAAM;AACvB;AAAA,QAAA;AAGI,cAAA,UAAUC,KAAAA,aAAa,EAAE,QAAQ;AACjC,cAAA,SAASC,mBAAc,OAAO;AAEpC,eAAO,OAAO,SAAS,OAAK,YAAO,CAAC,MAAR,mBAAW,WAAU,KAAK;AACpD,iBAAO,MAAM;AAAA,QAAA;AAGf,cAAM,SAAS,OAAO,IAAI,CAAC,YAAY;AACjC,cAAA,QAAQ,UAAU,KAAK;AAClB,mBAAA;AAAA,UAAA;AAGL,cAAA,QAAQ,SAAS,SAAS;AACrB,mBAAA;AAAA,UAAA;AAGL,cAAA,QAAQ,SAAS,YAAY;AACxB,mBAAA;AAAA,UAAA;AAGF,iBAAA;AAAA,QAAA,CACR;AAEY,qBAAA,KAAK,EAAE,OAAO,GAAG,SAAS,QAAQ,OAAO,GAAG,QAAQ;AAAA,MAAA,CAClE;AAED,WAAK,aAAa,aACf,KAAK,CAAC,GAAG,MAAM;AACR,cAAA,YAAY,KAAK,IAAI,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AAG3D,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;AAC/B,mBAAO,EAAE,OAAO,CAAC,IAAK,EAAE,OAAO,CAAC;AAAA,UAAA;AAAA,QAClC;AAIF,YAAI,EAAE,OAAO,WAAW,EAAE,OAAO,QAAQ;AACvC,iBAAO,EAAE,OAAO,SAAS,EAAE,OAAO;AAAA,QAAA;AAIpC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,cAAA,EAAE,OAAO,CAAC,EAAG,UAAU,EAAE,OAAO,CAAC,EAAG,OAAO;AACtC,mBAAA,EAAE,OAAO,CAAC,EAAG,QAAQ,EAAE,OAAO,CAAC,EAAG,QAAQ,IAAI;AAAA,UAAA;AAAA,QACvD;AAIK,eAAA,EAAE,QAAQ,EAAE;AAAA,MACpB,CAAA,EACA,IAAI,CAAC,GAAG,MAAM;AACb,UAAE,MAAM,OAAO;AACf,eAAO,EAAE;AAAA,MAAA,CACV;AAAA,IACL;AAEyB,SAAA,YAAA,CAAC,WAAW,OAAO;AAC1C,YAAM,WAAgC;AAAA,QACpC;AAAA,QACA;AAAA,MACF;AAEK,WAAA,YAAY,IAAI,QAAQ;AAE7B,aAAO,MAAM;AACN,aAAA,YAAY,OAAO,QAAQ;AAAA,MAClC;AAAA,IACF;AAEA,SAAA,OAAe,CAAC,gBAAgB;AACzB,WAAA,YAAY,QAAQ,CAAC,aAAa;AACjC,YAAA,SAAS,cAAc,YAAY,MAAM;AAC3C,mBAAS,GAAG,WAAW;AAAA,QAAA;AAAA,MACzB,CACD;AAAA,IACH;AAE6C,SAAA,gBAAA,CAC3C,kBACA,oBACG;AACH,YAAM,QAAQ,CAAC;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,MACmE;AACnE,cAAM,eAAe,KAAK,QAAQ,YAAY,MAAM;AACpD,cAAM,YAAY,KAAK,QAAQ,gBAAgB,YAAY;AAEpD,eAAA;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQC,MAAA,iBAAiB,qDAAkB,QAAQ,YAAY;AAAA,UAC/D,MAAM,KAAK,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC,KAAK;AAAA,UACtC,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI;AAAA,UACpC,OAAOA,MAAA,iBAAiB,qDAAkB,OAAO,KAAK;AAAA,QACxD;AAAA,MACF;AAEA,YAAM,WAAW,MAAM,mBAAmB,KAAK,QAAQ,QAAQ;AAE/D,YAAM,EAAE,gBAAgB,UAAU,IAAI,SAAS;AAE/C,UAAI,mBAAmB,CAAC,aAAa,cAAc,KAAK,kBAAkB;AAElE,cAAA,qBAAqB,MAAM,cAAc;AAC5B,2BAAA,MAAM,MAAM,SAAS,MAAM;AAE9C,eAAO,mBAAmB,MAAM;AAEzB,eAAA;AAAA,UACL,GAAG;AAAA,UACH,gBAAgB;AAAA,QAClB;AAAA,MAAA;AAGK,aAAA;AAAA,IACT;AAEsB,SAAA,sBAAA,CAAC,MAAcC,WAAiB;AACpD,YAAM,eAAeC,KAAAA,YAAY;AAAA,QAC/B,UAAU,KAAK;AAAA,QACf,MAAM;AAAA,QACN,IAAIC,eAAUF,MAAI;AAAA,QAClB,eAAe,KAAK,QAAQ;AAAA,QAC5B,eAAe,KAAK,QAAQ;AAAA,MAAA,CAC7B;AACM,aAAA;AAAA,IACT;AAe6B,SAAA,cAAA,CAC3B,gBACA,sBACA,SACG;AACC,UAAA,OAAO,mBAAmB,UAAU;AACtC,eAAO,KAAK;AAAA,UACV;AAAA,YACE,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,QACF;AAAA,MAAA,OACK;AACE,eAAA,KAAK,oBAAoB,gBAAgB,oBAAoB;AAAA,MAAA;AAAA,IAExE;AAsSqC,SAAA,mBAAA,CAAC,MAAM,SAAS;AACnD,UAAI,cAAsC,CAAC;AACrC,YAAA,cAAcJ,KAAAA,cAAc,KAAK,QAAQ;AACzC,YAAA,mBAAmB,CAAC,UAAoB;AAC5C,cAAM,SAASO,KAAA,cAAc,KAAK,UAAU,aAAa;AAAA,UACvD,IAAI,MAAM;AAAA,UACV,eACE,MAAM,QAAQ,iBAAiB,KAAK,QAAQ;AAAA,UAC9C,OAAO;AAAA,QAAA,CACR;AACM,eAAA;AAAA,MACT;AAEI,UAAA,cACF,6BAAM,QAAO,SAAY,KAAK,aAAa,KAAK,EAAG,IAAI;AACzD,UAAI,YAAY;AACd,sBAAc,iBAAiB,UAAU;AAAA,MAAA,OACpC;AACL,qBAAa,KAAK,WAAW,KAAK,CAAC,UAAU;AACrC,gBAAA,gBAAgB,iBAAiB,KAAK;AAE5C,cAAI,eAAe;AACH,0BAAA;AACP,mBAAA;AAAA,UAAA;AAGF,iBAAA;AAAA,QAAA,CACR;AAAA,MAAA;AAGH,UAAI,cACF,cAAe,KAAK,WAAmBC,KAAAA,WAAW;AAE9C,YAAA,gBAAiC,CAAC,WAAW;AAEnD,aAAO,YAAY,aAAa;AAC9B,sBAAc,YAAY;AAC1B,sBAAc,QAAQ,WAAW;AAAA,MAAA;AAG5B,aAAA,EAAE,eAAe,aAAa,WAAW;AAAA,IAClD;AAEA,SAAA,cAAc,CAAC,OAAe;AACtB,YAAA,QAAQ,KAAK,SAAS,EAAE;AAE9B,UAAI,CAAC,MAAO;AAEZ,YAAM,gBAAgB,MAAM;AAC5B,mBAAa,MAAM,cAAc;AAAA,IACnC;AAEA,SAAA,gBAAgB,MAAM;;AACpB,iBAAK,MAAM,mBAAX,mBAA2B,QAAQ,CAAC,UAAU;AACvC,aAAA,YAAY,MAAM,EAAE;AAAA,MAAA;AAAA,IAE7B;AAEA,SAAA,gBAAiC,CAAC,SAAS;AACzC,YAAM,QAAQ,CACZ,OAEI,CAAA,GACJ,wBACmB;;AACnB,cAAM,cAAc,KAAK,gBACrB,KAAK,YAAY,KAAK,eAAe,EAAE,gBAAgB,KAAA,CAAM,IAC7D,KAAK,MAAM;AAEf,cAAM,YACJ,KAAK,QAAQ,OACT,YAAY;AAAA,UAAK,CAAC,MAChBD,KAAAA,cAAc,KAAK,UAAUP,mBAAc,EAAE,QAAQ,GAAG;AAAA,YACtD,IAAI,KAAK;AAAA,YACT,eAAe;AAAA,YACf,OAAO;AAAA,UACR,CAAA;AAAA,QAAA,IAEH;AAEN,cAAM,YAAW,uCAAW,aAAY,KAAK,eAAe;AAE5D;AAAA,UACE,KAAK,QAAQ,QAAQ,aAAa;AAAA,UAClC,oCAAoC,KAAK;AAAA,QAC3C;AAEA,cAAM,eAAa,UAAK,MAAM,mBAAX,mBAA2B,WAC1CS,gBAAK,KAAK,MAAM,cAAc,MAA9BA,mBAAiC,WACjCA,gBAAK,WAAW,MAAhBA,mBAAmB,WAAU,KAAK,eAAe;AAE/C,cAAA,iBAAiB,2DAAqB,cAAc;AAAA,UAAO,CAAC,MAChE,YAAY,KAAK,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE;AAAA;AAExC,YAAA;AACJ,YAAI,KAAK,IAAI;AACL,gBAAA,iBACJ,uCAAW,eACXA,WAAA,KAAK,WAAW,MAAhBA,mBAAmB,aACnB,KAAK,eAAe;AACtB,qBAAW,KAAK,oBAAoB,eAAe,GAAG,KAAK,EAAE,EAAE;AAAA,QAAA,OAC1D;AACL,gBAAM,6BACJ,KAAK,YACH,sDAAgB,KAAK,CAAC,UAAU;AAC9B,kBAAM,mBAAmBC,KAAAA,gBAAgB;AAAA,cACvC,MAAM,MAAM;AAAA,cACZ,SAAQ,2DAAqB,gBAAe,CAAC;AAAA,cAC7C,eAAe,KAAK;AAAA,YACrB,CAAA,EAAE;AACH,kBAAMC,YAAWC,KAAAA,UAAU,CAAC,KAAK,UAAU,gBAAgB,CAAC;AAC5D,mBAAOD,cAAa;AAAA,UACrB,OARD,mBAQI,EACN;AACF,qBAAW,KAAK;AAAA,YACd;AAAA,aACA,yEAA4B,OAAM;AAAA,UACpC;AAAA,QAAA;AAGF,cAAM,aAAa,EAAE,IAAGF,WAAK,KAAA,WAAW,MAAhBA,mBAAmB,OAAO;AAElD,YAAI,cACD,KAAK,UAAU,UAAU,OACtB,aACA;AAAA,UACE,GAAG;AAAA,UACH,GAAGI,MAAA,iBAAiB,KAAK,QAAe,UAAU;AAAA,QACpD;AAEN,YAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AACjB,qEAAA,cAClB,IAAI,CAAC,UAAU;;AACd,qBACEC,MAAA,MAAM,QAAQ,WAAd,gBAAAA,IAAsB,cAAa,MAAM,QAAQ;AAAA,UAEpD,GACA,OAAO,SACP,QAAQ,CAAC,OAAO;AACf,yBAAa,EAAE,GAAG,YAAa,GAAG,GAAI,UAAU,EAAE;AAAA,UAAA;AAAA,QACnD;AAGL,mBAAWJ,KAAAA,gBAAgB;AAAA,UACzB,MAAM;AAAA,UACN,QAAQ,cAAc,CAAC;AAAA,UACvB,gBAAgB;AAAA,UAChB,aAAa,KAAK;AAAA,UAClB,eAAe,KAAK;AAAA,QACrB,CAAA,EAAE;AAEH,YAAI,SAAS;AACb,YAAI,KAAK,4BAA0B,UAAK,QAAQ,WAAb,mBAAqB,SAAQ;AAC9D,cAAI,kBAAkB,CAAC;AACF,qEAAA,cAAc,QAAQ,CAAC,UAAU;AAChD,gBAAA;AACE,kBAAA,MAAM,QAAQ,gBAAgB;AACd,kCAAA;AAAA,kBAChB,GAAG;AAAA,kBACH,GAAI,eAAe,MAAM,QAAQ,gBAAgB;AAAA,oBAC/C,GAAG;AAAA,oBACH,GAAG;AAAA,kBAAA,CACJ,KAAK,CAAA;AAAA,gBACR;AAAA,cAAA;AAAA,YACF,QACM;AAAA,YAAA;AAAA,UAER;AAEO,mBAAA;AAAA,QAAA;AAGL,cAAA,mBAAmB,CAACK,YAAgB;AAClC,gBAAA,kBACJ,2DAAqB,cAAc;AAAA,YACjC,CAAC,KAAK,UAAU;;AACd,oBAAM,cAA4C,CAAC;AAC/C,kBAAA,YAAY,MAAM,SAAS;AACzB,qBAAAD,MAAA,MAAM,QAAQ,WAAd,gBAAAA,IAAsB,aAAa;AACrC,8BAAY,KAAK,GAAG,MAAM,QAAQ,OAAO,WAAW;AAAA,gBAAA;AAAA,cACtD,WAIA,MAAM,QAAQ,oBACd,MAAM,QAAQ,mBACd;AACA,sBAAM,mBAA0C,CAAC;AAAA,kBAC/C,QAAAC;AAAAA,kBACA;AAAA,gBAAA,MACI;AACJ,sBAAI,aAAaA;AACjB,sBACE,sBAAsB,MAAM,WAC5B,MAAM,QAAQ,kBACd;AACa,iCAAA,MAAM,QAAQ,iBAAiB;AAAA,sBAC1C,CAAC,MAAMC,UAASA,MAAK,IAAI;AAAA,sBACzBD;AAAAA,oBACF;AAAA,kBAAA;AAEI,wBAAA,SAAS,KAAK,UAAU;AAC9B,sBACE,uBAAuB,MAAM,WAC7B,MAAM,QAAQ,mBACd;AACO,2BAAA,MAAM,QAAQ,kBAAkB;AAAA,sBACrC,CAAC,MAAMC,UAASA,MAAK,IAAI;AAAA,sBACzB;AAAA,oBACF;AAAA,kBAAA;AAEK,yBAAA;AAAA,gBACT;AACA,4BAAY,KAAK,gBAAgB;AAAA,cAAA;AAEnC,kBAAI,KAAK,0BAA0B,MAAM,QAAQ,gBAAgB;AAC/D,sBAAM,WAAkC,CAAC,EAAE,QAAAD,SAAQ,WAAW;AACtD,wBAAA,SAAS,KAAKA,OAAM;AACtB,sBAAA;AACF,0BAAM,kBAAkB;AAAA,sBACtB,GAAG;AAAA,sBACH,GAAI;AAAA,wBACF,MAAM,QAAQ;AAAA,wBACd;AAAA,sBAAA,KACG,CAAA;AAAA,oBACP;AACO,2BAAA;AAAA,kBAAA,QACD;AAEC,2BAAA;AAAA,kBAAA;AAAA,gBAEX;AACA,4BAAY,KAAK,QAAQ;AAAA,cAAA;AAEpB,qBAAA,IAAI,OAAO,WAAW;AAAA,YAC/B;AAAA,YACA,CAAA;AAAA,gBACG,CAAC;AAGR,gBAAM,QAA+B,CAAC,EAAE,QAAAA,cAAa;AAC/C,gBAAA,CAAC,KAAK,QAAQ;AAChB,qBAAO,CAAC;AAAA,YAAA;AAEN,gBAAA,KAAK,WAAW,MAAM;AACjBA,qBAAAA;AAAAA,YAAA;AAEF,mBAAAF,uBAAiB,KAAK,QAAQE,OAAM;AAAA,UAC7C;AACA,yBAAe,KAAK,KAAK;AAEnB,gBAAA,YAAY,CAAC,OAAe,kBAA4B;AAExD,gBAAA,SAAS,eAAe,QAAQ;AAC3B,qBAAA;AAAA,YAAA;AAGH,kBAAA,aAAa,eAAe,KAAK;AAEjC,kBAAA,OAAO,CAAC,cAAwB;AAC7B,qBAAA,UAAU,QAAQ,GAAG,SAAS;AAAA,YACvC;AAEA,mBAAO,WAAW,EAAE,QAAQ,eAAe,MAAM;AAAA,UACnD;AAGO,iBAAA,UAAU,GAAGA,OAAM;AAAA,QAC5B;AAEA,iBAAS,iBAAiB,MAAM;AAEvB,iBAAAZ,MAAAA,iBAAiB,YAAY,MAAM;AAC5C,cAAM,YAAY,KAAK,QAAQ,gBAAgB,MAAM;AAErD,cAAM,OACJ,KAAK,SAAS,OACV,KAAK,eAAe,OACpB,KAAK,OACHU,MAAAA,iBAAiB,KAAK,MAAM,KAAK,eAAe,IAAI,IACpD;AAER,cAAM,UAAU,OAAO,IAAI,IAAI,KAAK;AAEpC,YAAI,YACF,KAAK,UAAU,OACX,KAAK,eAAe,QACpB,KAAK,QACHA,MAAAA,iBAAiB,KAAK,OAAO,KAAK,eAAe,KAAK,IACtD,CAAC;AAET,oBAAYV,MAAAA,iBAAiB,KAAK,eAAe,OAAO,SAAS;AAE1D,eAAA;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO;AAAA,UACP,MAAM,QAAQ;AAAA,UACd,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,OAAO;AAAA,UACvC,gBAAgB,KAAK;AAAA,QACvB;AAAA,MACF;AAEA,YAAM,mBAAmB,CACvB,OAAyB,CAAA,GACzB,eACG;;AACG,cAAA,OAAO,MAAM,IAAI;AACvB,YAAI,aAAa,aAAa,MAAM,UAAU,IAAI;AAElD,YAAI,CAAC,YAAY;AACf,cAAI,SAAS,CAAC;AAEd,gBAAM,aAAY,UAAK,QAAQ,eAAb,mBAAyB,KAAK,CAAC,MAAM;AACrD,kBAAM,QAAQI,KAAAA,cAAc,KAAK,UAAU,KAAK,UAAU;AAAA,cACxD,IAAI,EAAE;AAAA,cACN,eAAe;AAAA,cACf,OAAO;AAAA,YAAA,CACR;AAED,gBAAI,OAAO;AACA,uBAAA;AACF,qBAAA;AAAA,YAAA;AAGF,mBAAA;AAAA,UAAA;AAGT,cAAI,WAAW;AACb,kBAAM,EAAE,MAAM,OAAO,GAAG,UAAc,IAAA;AACzB,yBAAA;AAAA,cACX,GAAGU,WAAK,MAAM,CAAC,MAAM,CAAC;AAAA,cACtB,GAAG;AAAA,cACH;AAAA,YACF;AACA,yBAAa,MAAM,UAAU;AAAA,UAAA;AAAA,QAC/B;AAGF,cAAM,cAAc,KAAK,iBAAiB,MAAM,IAAI;AAC9C,cAAA,QAAQ,MAAM,MAAM,WAAW;AAErC,YAAI,YAAY;AACd,gBAAM,gBAAgB,KAAK,iBAAiB,YAAY,UAAU;AAC5D,gBAAA,cAAc,MAAM,YAAY,aAAa;AACnD,gBAAM,iBAAiB;AAAA,QAAA;AAGlB,eAAA;AAAA,MACT;AAEA,UAAI,KAAK,MAAM;AACb,eAAO,iBAAiB,MAAM;AAAA,UAC5B,GAAGA,WAAK,MAAM,CAAC,MAAM,CAAC;AAAA,UACtB,GAAG,KAAK;AAAA,QAAA,CACT;AAAA,MAAA;AAGH,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAIA,SAAA,iBAAmC,CAAC;AAAA,MAClC;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IAAA,MACC;AACJ,YAAM,cAAc,MAAM;AAIxB,cAAM,eAAe;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACa,qBAAA,QAAQ,CAAC,SAAS;AAC3B,eAAK,MAAc,IAAI,IAAI,KAAK,eAAe,MAAM,IAAI;AAAA,QAAA,CAC5D;AACD,cAAM,UAAUC,MAAAA,UAAU,KAAK,OAAO,KAAK,eAAe,KAAK;AAClD,qBAAA,QAAQ,CAAC,SAAS;AACtB,iBAAA,KAAK,MAAM,IAAI;AAAA,QAAA,CACvB;AACM,eAAA;AAAA,MACT;AAEA,YAAM,YAAY,KAAK,eAAe,SAAS,KAAK;AAEpD,YAAM,wBAAwB,KAAK;AAC9B,WAAA,wBAAwBC,MAAAA,wBAA8B,MAAM;AAC/D,uEAAuB;AAAA,MAAQ,CAChC;AAGG,UAAA,aAAa,eAAe;AAC9B,aAAK,KAAK;AAAA,MAAA,OACL;AAEL,YAAI,EAAE,gBAAgB,oBAAoB,GAAG,YAAgB,IAAA;AAE7D,YAAI,gBAAgB;AACJ,wBAAA;AAAA,YACZ,GAAG;AAAA,YACH,OAAO;AAAA,cACL,GAAG,eAAe;AAAA,cAClB,WAAW;AAAA,cACX,gBAAgB;AAAA,gBACd,GAAG;AAAA,gBACH,QAAQ,YAAY;AAAA,gBACpB,OAAO;AAAA,kBACL,GAAG,YAAY;AAAA,kBACf,WAAW;AAAA,kBACX,gBAAgB;AAAA,kBAChB,KAAK;AAAA,gBAAA;AAAA,cACP;AAAA,YACF;AAAA,UAEJ;AAEA,cACE,YAAY,kBACZ,KAAK,QAAQ,kBACb,OACA;AACY,wBAAA,MAAM,YAAY,KAAK;AAAA,UAAA;AAAA,QACrC;AAGF,oBAAY,MAAM,8BAChB,sBAAsB,KAAK,QAAQ,6BAA6B;AAElE,aAAK,uBAAuB;AAE5B,aAAK,QAAQ,KAAK,UAAU,YAAY,MAAM;AAAA,UAC5C,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,EAAE,cAAc;AAAA,QAClB;AAAA,MAAA;AAGG,WAAA,kBAAkB,KAAK,eAAe;AAE3C,UAAI,CAAC,KAAK,QAAQ,YAAY,MAAM;AAClC,aAAK,KAAK;AAAA,MAAA;AAGZ,aAAO,KAAK;AAAA,IACd;AAEA,SAAA,yBAAyB,CAAC;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAA8C,OAAO;AACnD,UAAI,MAAM;AACR,cAAM,eAAe,KAAK,QAAQ,SAAS,MAAM;AAC3C,cAAA,SAASC,kBAAU,MAAM;AAAA,UAC7B,aAAa,UAAU,eAAe,eAAe;AAAA,QAAA,CACtD;AACD,aAAK,KAAK,OAAO;AACjB,aAAK,SAAS,KAAK,QAAQ,YAAY,OAAO,MAAM;AAEpD,aAAK,OAAO,OAAO,KAAK,MAAM,CAAC;AAAA,MAAA;AAG3B,YAAA,WAAW,KAAK,cAAc;AAAA,QAClC,GAAI;AAAA,QACJ,wBAAwB;AAAA,MAAA,CACzB;AACD,aAAO,KAAK,eAAe;AAAA,QACzB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACD;AAAA,IACH;AAEA,SAAA,WAAuB,CAAC,EAAE,IAAI,gBAAgB,MAAM,GAAG,WAAW;AAChE,UAAI,gBAAgB;AAClB,YAAI,CAAC,MAAM;AACT,gBAAM,WAAW,KAAK,cAAc,EAAE,IAAI,GAAG,MAAa;AAC1D,iBAAO,KAAK,QAAQ,WAAW,SAAS,IAAI;AAAA,QAAA;AAE9C,YAAI,KAAK,SAAS;AACT,iBAAA,SAAS,QAAQ,IAAI;AAAA,QAAA,OACvB;AACL,iBAAO,SAAS,OAAO;AAAA,QAAA;AAEzB;AAAA,MAAA;AAGF,aAAO,KAAK,uBAAuB;AAAA,QACjC,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MAAA,CACD;AAAA,IACH;AAIA,SAAA,OAAe,OAAO,SAA6C;AACjE,WAAK,iBAAiB,KAAK,cAAc,KAAK,cAAc;AAExD,UAAAC;AACA,UAAAC;AAEA,UAAA;AAGU,oBAAA,IAAI,QAAc,CAAC,YAAY;AAC3C,aAAK,gBAAgB,YAAY;;AAC3B,cAAA;AACF,kBAAM,OAAO,KAAK;AACZ,kBAAA,eAAe,KAAK,MAAM;AAGhC,iBAAK,cAAc;AAEf,gBAAA;AAEJC,kBAAAA,MAAM,MAAM;AAMO,+BAAA,KAAK,YAAY,IAAI;AAGjC,mBAAA,QAAQ,SAAS,CAAC,OAAO;AAAA,gBAC5B,GAAG;AAAA,gBACH,QAAQ;AAAA,gBACR,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV;AAAA;AAAA,gBAEA,eAAe,EAAE,cAAc,OAAO,CAAC,MAAM;AACpC,yBAAA,CAAC,eAAe,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;AAAA,gBACjD,CAAA;AAAA,cAAA,EACD;AAAA,YAAA,CACH;AAEG,gBAAA,CAAC,KAAK,MAAM,UAAU;AACxB,mBAAK,KAAK;AAAA,gBACR,MAAM;AAAA,gBACN,GAAG,sBAAsB;AAAA,kBACvB,kBAAkB;AAAA,kBAClB,UAAU;AAAA,gBACX,CAAA;AAAA,cAAA,CACF;AAAA,YAAA;AAGH,iBAAK,KAAK;AAAA,cACR,MAAM;AAAA,cACN,GAAG,sBAAsB;AAAA,gBACvB,kBAAkB;AAAA,gBAClB,UAAU;AAAA,cACX,CAAA;AAAA,YAAA,CACF;AAED,kBAAM,KAAK,YAAY;AAAA,cACrB,MAAM,6BAAM;AAAA,cACZ,SAAS;AAAA,cACT,UAAU;AAAA;AAAA,cAEV,SAAS,YAAY;AAEnB,qBAAK,oBAAoB,YAAY;AAK/B,sBAAA;AACA,sBAAA;AACA,sBAAA;AAEJA,wBAAAA,MAAM,MAAM;AACL,yBAAA,QAAQ,SAAS,CAAC,MAAM;AAC3B,4BAAM,kBAAkB,EAAE;AACpB,4BAAA,aAAa,EAAE,kBAAkB,EAAE;AAEzC,uCAAiB,gBAAgB;AAAA,wBAC/B,CAAC,UAAU,CAAC,WAAW,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AAAA,sBACtD;AACA,wCAAkB,WAAW;AAAA,wBAC3B,CAAC,UACC,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AAAA,sBAClD;AACA,uCAAiB,gBAAgB;AAAA,wBAAO,CAAC,UACvC,WAAW,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AAAA,sBAC1C;AAEO,6BAAA;AAAA,wBACL,GAAG;AAAA,wBACH,WAAW;AAAA,wBACX,UAAU,KAAK,IAAI;AAAA,wBACnB,SAAS;AAAA,wBACT,gBAAgB;AAAA,wBAChB,eAAe;AAAA,0BACb,GAAG,EAAE;AAAA,0BACL,GAAG,eAAe,OAAO,CAAC,MAAM,EAAE,WAAW,OAAO;AAAA,wBAAA;AAAA,sBAExD;AAAA,oBAAA,CACD;AACD,yBAAK,kBAAkB;AAAA,kBAAA,CACxB;AAIC;AAAA,oBACE,CAAC,gBAAgB,SAAS;AAAA,oBAC1B,CAAC,iBAAiB,SAAS;AAAA,oBAC3B,CAAC,gBAAgB,QAAQ;AAAA,oBAE3B,QAAQ,CAAC,CAAC,SAAS,IAAI,MAAM;AACrB,4BAAA,QAAQ,CAAC,UAAU;;AACzB,6BAAAT,MAAA,KAAK,gBAAgB,MAAM,OAAO,EAAG,SAAQ,UAA7C,wBAAAA,KAAqD;AAAA,oBAAK,CAC3D;AAAA,kBAAA,CACF;AAAA,gBAAA,CACF;AAAA,cAAA;AAAA,YACH,CACD;AAAA,mBACM,KAAK;AACR,gBAAAU,SAAAA,mBAAmB,GAAG,GAAG;AAChBH,2BAAA;AACP,kBAAA,CAAC,KAAK,UAAU;AAClB,qBAAK,SAAS;AAAA,kBACZ,GAAGA;AAAAA,kBACH,SAAS;AAAA,kBACT,eAAe;AAAA,gBAAA,CAChB;AAAA,cAAA;AAAA,YACH,WACSI,SAAAA,WAAW,GAAG,GAAG;AACfH,2BAAA;AAAA,YAAA;AAGR,iBAAA,QAAQ,SAAS,CAAC,OAAO;AAAA,cAC5B,GAAG;AAAA,cACH,YAAYD,aACRA,WAAS,aACTC,aACE,MACA,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,OAAO,IACxC,MACA;AAAA,cACRD,UAAAA;AAAAA,YAAA,EACA;AAAA,UAAA;AAGA,cAAA,KAAK,sBAAsB,aAAa;AAC1C,uBAAK,0BAAL,mBAA4B;AAC5B,iBAAK,oBAAoB;AACzB,iBAAK,wBAAwB;AAAA,UAAA;AAEvB,kBAAA;AAAA,QAAA,CACT;AAAA,MAAA,CACF;AAED,WAAK,oBAAoB;AAEnB,YAAA;AAEN,aACG,KAAK,qBACN,gBAAgB,KAAK,mBACrB;AACA,cAAM,KAAK;AAAA,MAAA;AAGT,UAAA,KAAK,oBAAoB;AACtB,aAAA,QAAQ,SAAS,CAAC,OAAO;AAAA,UAC5B,GAAG;AAAA,UACH,YAAY;AAAA,QAAA,EACZ;AAAA,MAAA;AAAA,IAEN;AAEA,SAAA,sBAAsB,CAAC,OAA4B;AAGjD,YAAM,uBACJ,KAAK,wBAAwB,KAAK,QAAQ;AAG5C,aAAO,KAAK;AAGV,UAAA,wBACA,OAAO,aAAa,eACpB,yBAAyB,YACzB,OAAO,SAAS,wBAAwB,YACxC;AAGI,YAAA;AAEJ,YACE,OAAO,yBAAyB,YAChC,KAAK,gCACL;AACA,gBAAM,OAAO,KAAK;AACZ,gBAAA,eAAe,KAAK,MAAM;AAEhC,gBAAM,8BACJ,OAAO,qBAAqB,UAAU,aAClC,qBAAqB;AAAA,YACnB,sBAAsB;AAAA,cACpB,kBAAkB;AAAA,cAClB,UAAU;AAAA,YACX,CAAA;AAAA,cAEH,qBAAqB;AAEC,sCAAA;AAAA,YAC1B,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QAAA,OACK;AACuB,sCAAA;AAAA,QAAA;AAG9B,iBAAS,oBAAoB,yBAAyB;AAAA,MAAA,OACjD;AACF,WAAA;AAAA,MAAA;AAAA,IAEP;AAE6B,SAAA,cAAA,CAAC,IAAI,YAAY;;AACxC,UAAA;AACE,YAAA,aAAY,UAAK,MAAM,mBAAX,mBAA2B,KAAK,CAAC,MAAM,EAAE,OAAO;AAC5D,YAAA,YAAY,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AACtD,YAAA,WAAW,KAAK,MAAM,cAAc,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAEjE,YAAM,aAAa,YACf,mBACA,YACE,YACA,WACE,kBACA;AAER,UAAI,YAAY;AACT,aAAA,QAAQ,SAAS,CAAC,MAAO;;AAAA;AAAA,YAC5B,GAAG;AAAA,YACH,CAAC,UAAU,IAAGP,MAAA,EAAE,UAAU,MAAZ,gBAAAA,IAAe;AAAA,cAAI,CAAC,MAChC,EAAE,OAAO,KAAM,UAAU,QAAQ,CAAC,IAAK;AAAA;AAAA,UACzC;AAAA,SACA;AAAA,MAAA;AAGG,aAAA;AAAA,IACT;AAEA,SAAA,WAAuB,CAAC,YAAoB;AACnC,aAAA;AAAA,QACL,GAAG,KAAK,MAAM;AAAA,QACd,GAAI,KAAK,MAAM,kBAAkB,CAAC;AAAA,QAClC,GAAG,KAAK,MAAM;AAAA,QACd,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAAA,IAChC;AAEA,SAAA,cAAc,OAAO;AAAA,MACnB;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,cAAc,KAAK;AAAA,MACnB;AAAA,IAAA,MAYoC;AAChC,UAAA;AACJ,UAAI,WAAW;AAEf,YAAM,iBAAiB,YAAY;AACjC,YAAI,CAAC,UAAU;AACF,qBAAA;AACX,iBAAM;AAAA,QAAU;AAAA,MAEpB;AAEM,YAAA,iBAAiB,CAAC,YAAoB;AAC1C,eAAO,CAAC,EAAE,cAAc,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAAA,MAC1E;AAEM,YAAA,4BAA4B,CAAC,OAAsB,QAAa;;AAChE,YAAAU,SAAAA,mBAAmB,GAAG,GAAG;AACvB,cAAA,CAAC,IAAI,gBAAgB;AACjB,kBAAA;AAAA,UAAA;AAAA,QACR;AAGF,YAAIE,SAAW,WAAA,GAAG,KAAKD,SAAA,WAAW,GAAG,GAAG;AAC1B,sBAAA,MAAM,IAAI,CAAC,UAAU;AAAA,YAC/B,GAAG;AAAA,YACH,QAAQC,SAAAA,WAAW,GAAG,IAClB,eACAD,SAAAA,WAAW,GAAG,IACZ,aACA;AAAA,YACN,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,mBAAmB;AAAA,YACnB,eAAe;AAAA,UAAA,EACf;AAEE,cAAA,CAAE,IAAY,SAAS;AACvB,gBAAY,UAAU,MAAM;AAAA,UAAA;AAGhC,sBAAM,sBAAN,mBAAyB;AACzB,sBAAM,kBAAN,mBAAqB;AACrB,sBAAM,gBAAN,mBAAmB;AAEf,cAAAC,SAAAA,WAAW,GAAG,GAAG;AACR,uBAAA;AACX,kBAAM,KAAK,gBAAgB,EAAE,GAAG,KAAK,eAAe,UAAU;AACxD,kBAAA;AAAA,UAAA,WACGD,SAAAA,WAAW,GAAG,GAAG;AACrB,iBAAA,gBAAgB,SAAS,KAAK;AAAA,cACjC;AAAA,YAAA,CACD;AACD,uBAAK,cAAL,mBAAgB,eAAe;AAAA,cAC7B,QAAQ;AAAA,cACR,OAAO,KAAK,SAAS,MAAM,EAAE;AAAA,YAAA;AAEzB,kBAAA;AAAA,UAAA;AAAA,QACR;AAAA,MAEJ;AAEI,UAAA;AACF,cAAM,IAAI,QAAc,CAAC,YAAY,cAAc;AACjD;AAAC,WAAC,YAAY;;AACR,gBAAA;AACF,oBAAM,oBAAoB,CACxB,OACA,KACA,eACG;;AACH,sBAAM,EAAE,IAAI,SAAS,QAAQ,IAAI,QAAQ,KAAK;AACxC,sBAAA,QAAQ,KAAK,gBAAgB,OAAO;AAK1C,oBAAI,eAAe,SAAS;AACpB,wBAAA;AAAA,gBAAA;AAGR,oBAAI,aAAa;AACjB,qCAAqB,sBAAsB;AAC3C,0CAA0B,KAAK,SAAS,OAAO,GAAI,GAAG;AAElD,oBAAA;AACI,mBAAAE,OAAAb,MAAA,MAAA,SAAQ,YAAR,gBAAAa,IAAA,KAAAb,KAAkB;AAAA,yBACjB,iBAAiB;AAClB,wBAAA;AACN,4CAA0B,KAAK,SAAS,OAAO,GAAI,GAAG;AAAA,gBAAA;AAG5C,4BAAA,SAAS,CAAC,SAAS;;AAC7B,mBAAAA,MAAA,KAAK,sBAAL,gBAAAA,IAAwB;AACxB,mBAAAa,MAAA,KAAK,gBAAL,gBAAAA,IAAkB;AAEX,yBAAA;AAAA,oBACL,GAAG;AAAA,oBACH,OAAO;AAAA,oBACP,QAAQ;AAAA,oBACR,YAAY;AAAA,oBACZ,WAAW,KAAK,IAAI;AAAA,oBACpB,iBAAiB,IAAI,gBAAgB;AAAA,oBACrC,mBAAmB;AAAA,kBACrB;AAAA,gBAAA,CACD;AAAA,cACH;AAEW,yBAAA,CAAC,OAAO,EAAE,IAAI,SAAS,SAAS,KAAK,QAAQ,WAAW;AAC3D,sBAAA,gBAAgB,KAAK,SAAS,OAAO;AAC3C,sBAAM,iBAAgB,aAAQ,QAAQ,CAAC,MAAjB,mBAAoB;AAEpC,sBAAA,QAAQ,KAAK,gBAAgB,OAAO;AAE1C,sBAAM,YACJ,MAAM,QAAQ,aAAa,KAAK,QAAQ;AAE1C,sBAAM,gBAAgB,CAAC,EACrB,WACA,CAAC,KAAK,YACN,CAAC,eAAe,OAAO,MACtB,MAAM,QAAQ,UACb,MAAM,QAAQ,cACd,kBAAkB,KAAK,MACzB,OAAO,cAAc,YACrB,cAAc,aACb,MAAM,QAAQ,sBACZ,UAAK,YAAL,mBAAsB;AAG3B,oBAAI,oBAAoB;AACxB;AAAA;AAAA;AAAA,kBAGE,cAAc,qBACd,cAAc;AAAA,kBACd;AACA,sBAAI,eAAe;AACjB,+BAAW,MAAM;AACX,0BAAA;AAGa,uCAAA;AAAA,sBAAA,QACT;AAAA,sBAAA;AAAA,uBACP,SAAS;AAAA,kBAAA;AAId,wBAAM,cAAc;AACpB,sCAAoB,KAAK,SAAS,OAAO,EAAG,WAAW;AAAA,gBAAA;AAEzD,oBAAI,mBAAmB;AAEjB,sBAAA;AACU,gCAAA,SAAS,CAAC,SAAS;AAE7B,4BAAM,kBAAkB,KAAK;AACtB,6BAAA;AAAA,wBACL,GAAG;AAAA,wBACH,aAAaR,8BAA8B,MAAM;AAC/C,6EAAiB;AAAA,wBAAQ,CAC1B;AAAA,wBACD,mBAAmBA,MAA8B,wBAAA;AAAA,sBACnD;AAAA,oBAAA,CACD;AACK,0BAAA,kBAAkB,IAAI,gBAAgB;AAExC,wBAAA;AAEJ,wBAAI,eAAe;AAGjB,uCAAiB,WAAW,MAAM;AAC5B,4BAAA;AAGa,yCAAA;AAAA,wBAAA,QACT;AAAA,wBAAA;AAAA,yBACP,SAAS;AAAA,oBAAA;AAGd,0BAAM,EAAE,aAAa,YAAA,IAAgB,KAAK,SAAS,OAAO;AAE1D,wBAAI,aAAa;AACG,wCAAA,OAAO,aAAa,cAAc;AAAA,oBAAA;AAGtD,wBAAI,aAAa;AACG,wCAAA,OAAO,aAAa,iBAAiB;AAAA,oBAAA;AAGnD,0BAAA,wBAAwB,MAC5B,gBACI,KAAK,SAAS,aAAa,EAAG,UAC7B,KAAK,QAAQ,WAAW,CAAC;AAEpB,gCAAA,SAAS,CAAC,UAAU;AAAA,sBAC9B,GAAG;AAAA,sBACH,YAAY;AAAA,sBACZ,YAAY,KAAK,aAAa;AAAA,sBAC9B;AAAA,sBACA;AAAA,sBACA,SAAS;AAAA,wBACP,GAAG,sBAAsB;AAAA,wBACzB,GAAG,KAAK;AAAA,sBAAA;AAAA,oBACV,EACA;AAEI,0BAAA,EAAE,QAAQ,QAAQ,SAAS,UAC/B,KAAK,SAAS,OAAO;AAEjB,0BAAA,UAAU,eAAe,OAAO;AAEtC,0BAAM,sBAMF;AAAA,sBACF;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA,UAAU,CAAC,SACT,KAAK,SAAS,EAAE,GAAG,MAAM,eAAe,UAAU;AAAA,sBACpD,eAAe,KAAK;AAAA,sBACpB,OAAO,UAAU,YAAY;AAAA,sBAC7B;AAAA,oBACF;AAEA,0BAAM,oBACH,QAAM,iBAAM,SAAQ,eAAd,4BAA2B,yBAClC,CAAC;AAEH,wBACEO,SAAW,WAAA,iBAAiB,KAC5BD,SAAA,WAAW,iBAAiB,GAC5B;AACkB,wCAAA,OAAO,mBAAmB,aAAa;AAAA,oBAAA;AAG/C,gCAAA,SAAS,CAAC,SAAS;AACtB,6BAAA;AAAA,wBACL,GAAG;AAAA,wBACH,qBAAqB;AAAA,wBACrB,SAAS;AAAA,0BACP,GAAG,sBAAsB;AAAA,0BACzB,GAAG,KAAK;AAAA,0BACR,GAAG;AAAA,wBACL;AAAA,wBACA;AAAA,sBACF;AAAA,oBAAA,CACD;AAAA,2BACM,KAAK;AACM,sCAAA,OAAO,KAAK,aAAa;AAAA,kBAAA;AAGjC,8BAAA,SAAS,CAAC,SAAS;;AAC7B,qBAAAX,MAAA,KAAK,sBAAL,gBAAAA,IAAwB;AAEjB,2BAAA;AAAA,sBACL,GAAG;AAAA,sBACH,mBAAmB;AAAA,sBACnB,YAAY;AAAA,oBACd;AAAA,kBAAA,CACD;AAAA,gBAAA;AAAA,cACH;AAGF,oBAAM,uBAAuB,QAAQ,MAAM,GAAG,kBAAkB;AAChE,oBAAM,gBAA+C,CAAC;AAEtD,mCAAqB,QAAQ,CAAC,EAAE,IAAI,SAAS,WAAW,UAAU;AAClD,8BAAA;AAAA,mBACX,YAAY;AACX,0BAAM,EAAE,eAAe,kBAAA,IACrB,KAAK,SAAS,OAAO;AAEvB,wBAAI,uBAAuB;AAC3B,wBAAI,uBAAuB;AAE3B,wBAAI,mBAAmB;AACf,4BAAA;AACA,4BAAA,QAAQ,KAAK,SAAS,OAAO;AACnC,0BAAI,MAAM,OAAO;AACW,kDAAA,OAAO,MAAM,KAAK;AAAA,sBAAA;AAAA,oBAC9C,OACK;AACC,4BAAA,qBAAqB,cAAc,QAAQ,CAAC;AAC5C,4BAAA,QAAQ,KAAK,gBAAgB,OAAO;AAE1C,4BAAM,mBAAmB,MAAuB;AACxC,8BAAA;AAAA,0BACJ;AAAA,0BACA;AAAA,0BACA;AAAA,0BACA;AAAA,0BACA;AAAA,wBAAA,IACE,KAAK,SAAS,OAAO;AAEnBc,8BAAAA,WAAU,eAAe,OAAO;AAE/B,+BAAA;AAAA,0BACL;AAAA,0BACA,MAAM;AAAA,0BACN,SAAS,CAAC,CAACA;AAAAA,0BACX;AAAA,0BACA;AAAA,0BACA;AAAA,0BACA;AAAA,0BACA,UAAU,CAAC,SACT,KAAK,SAAS,EAAE,GAAG,MAAM,eAAe,UAAU;AAAA,0BACpD,OAAOA,WAAU,YAAY;AAAA,0BAC7B;AAAA,wBACF;AAAA,sBACF;AAGA,4BAAM,MAAM,KAAK,IAAA,IAAQ,KAAK,SAAS,OAAO,EAAG;AAE3C,4BAAA,UAAU,eAAe,OAAO;AAEtC,4BAAM,WAAW,UACZ,MAAM,QAAQ,oBACf,KAAK,QAAQ,2BACb,MACC,MAAM,QAAQ,aACf,KAAK,QAAQ,oBACb;AAEE,4BAAA,qBAAqB,MAAM,QAAQ;AAKzC,4BAAM,eACJ,OAAO,uBAAuB,aAC1B,mBAAmB,iBAAkB,CAAA,IACrC;AAEM,kCAAA,SAAS,CAAC,UAAU;AAAA,wBAC9B,GAAG;AAAA,wBACH,eAAeT,MAAAA,wBAA8B;AAAA,wBAC7C,SACE,CAAC,CAAC,WACF,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAAA,sBAAA,EAClD;AAEF,4BAAM,cAAc,MAAM;;AAClB,8BAAA,QAAQ,KAAK,SAAS,OAAO;AAEnC,4BAAI,CAAC,OAAO;AACV;AAAA,wBAAA;AAEF,8BAAM,eAAe;AAAA,0BACnB;AAAA,0BACA;AAAA,0BACA,QAAQ,MAAM;AAAA,0BACd,YAAY,MAAM;AAAA,wBACpB;AACA,8BAAM,iBAAgBQ,OAAAb,MAAA,MAAM,SAAQ,SAAd,gBAAAa,IAAA,KAAAb,KAAqB;AAC3C,8BAAM,OAAO,+CAAe;AAC5B,8BAAM,QAAQ,+CAAe;AAC7B,8BAAM,cAAc,+CAAe;AAEnC,8BAAM,WAAUe,OAAAC,MAAA,MAAM,SAAQ,YAAd,gBAAAD,IAAA,KAAAC,KAAwB;AACxC,8BAAM,WAAU,iBAAM,SAAQ,YAAd,4BAAwB;AAC5B,oCAAA,SAAS,CAAC,UAAU;AAAA,0BAC9B,GAAG;AAAA,0BACH;AAAA,0BACA;AAAA,0BACA;AAAA,0BACA;AAAA,0BACA;AAAA,wBAAA,EACA;AAAA,sBACJ;AAEA,4BAAM,YAAY,YAAY;;AACxB,4BAAA;AAMF,gCAAM,6BAA6B,YAAY;AACvC,kCAAA,cAAc,KAAK,SAAS,OAAO;AAEzC,gCAAI,YAAY,mBAAmB;AACjC,oCAAM,YAAY;AAAA,4BAAA;AAAA,0BAEtB;AAGI,8BAAA;AACF,iCAAK,eAAe,KAAK;AAEb,wCAAA,SAAS,CAAC,UAAU;AAAA,8BAC9B,GAAG;AAAA,8BACH,YAAY;AAAA,4BAAA,EACZ;AAGF,kCAAM,aACJ,QAAMH,OAAAb,MAAA,MAAM,SAAQ,WAAd,gBAAAa,IAAA,KAAAb,KAAuB;AAE/B;AAAA,8BACE,KAAK,SAAS,OAAO;AAAA,8BACrB;AAAA,4BACF;AAKA,kCAAM,MAAM;AAEZ,kCAAM,2BAA2B;AAIjC,kCAAM,MAAM;AAEZS,kCAAAA,MAAM,MAAM;AACE,0CAAA,SAAS,CAAC,UAAU;AAAA,gCAC9B,GAAG;AAAA,gCACH,OAAO;AAAA,gCACP,QAAQ;AAAA,gCACR,YAAY;AAAA,gCACZ,WAAW,KAAK,IAAI;AAAA,gCACpB;AAAA,8BAAA,EACA;AACU,0CAAA;AAAA,4BAAA,CACb;AAAA,mCACM,GAAG;AACV,gCAAI,QAAQ;AAEZ,kCAAM,2BAA2B;AAEjC,sDAA0B,KAAK,SAAS,OAAO,GAAI,CAAC;AAEhD,gCAAA;AACI,+BAAAM,OAAAC,MAAA,MAAA,SAAQ,YAAR,gBAAAD,IAAA,KAAAC,KAAkB;AAAA,qCACjB,cAAc;AACb,sCAAA;AACR;AAAA,gCACE,KAAK,SAAS,OAAO;AAAA,gCACrB;AAAA,8BACF;AAAA,4BAAA;AAGFP,kCAAAA,MAAM,MAAM;AACE,0CAAA,SAAS,CAAC,UAAU;AAAA,gCAC9B,GAAG;AAAA,gCACH;AAAA,gCACA,QAAQ;AAAA,gCACR,YAAY;AAAA,8BAAA,EACZ;AACU,0CAAA;AAAA,4BAAA,CACb;AAAA,0BAAA;AAGH,qCAAK,cAAL,mBAAgB,eAAe;AAAA,4BAC7B,QAAQ;AAAA,4BACR,OAAO,KAAK,SAAS,OAAO;AAAA,0BAAA;AAAA,iCAEvB,KAAK;AACZA,gCAAAA,MAAM,MAAM;AACE,wCAAA,SAAS,CAAC,UAAU;AAAA,8BAC9B,GAAG;AAAA,8BACH,eAAe;AAAA,4BAAA,EACf;AACU,wCAAA;AAAA,0BAAA,CACb;AACD,oDAA0B,KAAK,SAAS,OAAO,GAAI,GAAG;AAAA,wBAAA;AAAA,sBAE1D;AAGA,4BAAM,EAAE,QAAQ,QAAA,IAAY,KAAK,SAAS,OAAO;AACjD,6CACE,WAAW,cACV,YAAY,gBAAgB,MAAM;AACrC,0BAAI,WAAW,MAAM,QAAQ,YAAY,OAAO;AAAA,sBAAA,WAErC,wBAAwB,CAAC,MAAM;AACjB,+CAAA;AACtB,yBAAC,YAAY;AACR,8BAAA;AACF,kCAAM,UAAU;AAChB,kCAAM,EAAE,eAAe,YAAA,IACrB,KAAK,SAAS,OAAO;AACvB,2EAAe;AACf,uEAAa;AACD,wCAAA,SAAS,CAAC,UAAU;AAAA,8BAC9B,GAAG;AAAA,8BACH,eAAe;AAAA,4BAAA,EACf;AAAA,mCACK,KAAK;AACR,gCAAAC,SAAAA,mBAAmB,GAAG,GAAG;AACrB,oCAAA,KAAK,SAAS,GAAG;AAAA,4BAAA;AAAA,0BACzB;AAAA,wBACF,GACC;AAAA,sBAEH,WAAA,WAAW,aACV,wBAAwB,MACzB;AACA,8BAAM,UAAU;AAAA,sBAAA,OACX;AAIO,oCAAA;AAAA,sBAAA;AAAA,oBACd;AAEF,wBAAI,CAAC,sBAAsB;AACzB,4BAAM,EAAE,eAAe,YAAA,IACrB,KAAK,SAAS,OAAO;AACvB,qEAAe;AACf,iEAAa;AAAA,oBAAQ;AAGX,gCAAA,SAAS,CAAC,UAAU;AAAA,sBAC9B,GAAG;AAAA,sBACH,YAAY,uBAAuB,KAAK,aAAa;AAAA,sBACrD,eAAe,uBACX,KAAK,gBACL;AAAA,sBACJ,SAAS;AAAA,oBAAA,EACT;AACK,2BAAA,KAAK,SAAS,OAAO;AAAA,kBAC3B,GAAA;AAAA,gBACL;AAAA,cAAA,CACD;AAEK,oBAAA,QAAQ,IAAI,aAAa;AAEpB,yBAAA;AAAA,qBACJ,KAAK;AACZ,wBAAU,GAAG;AAAA,YAAA;AAAA,UACf,GACC;AAAA,QAAA,CACJ;AACD,cAAM,eAAe;AAAA,eACd,KAAK;AACZ,YAAIE,SAAW,WAAA,GAAG,KAAKD,SAAA,WAAW,GAAG,GAAG;AACtC,cAAIA,oBAAW,GAAG,KAAK,CAAC,YAAY;AAClC,kBAAM,eAAe;AAAA,UAAA;AAGjB,gBAAA;AAAA,QAAA;AAAA,MACR;AAGK,aAAA;AAAA,IACT;AAEA,SAAA,aAQI,CAAC,SAAS;AACN,YAAA,aAAa,CAAC,MAAkC;;AACpD,cAAI,kCAAM,WAAN,8BAAe,OAAmC,MAAM;AACnD,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,SAAS;AAAA,YACT,GAAI,EAAE,WAAW,UACZ,EAAE,QAAQ,WAAW,OAAO,WAC7B,CAAA;AAAA,UACN;AAAA,QAAA;AAEK,eAAA;AAAA,MACT;AAEK,WAAA,QAAQ,SAAS,CAAC,MAAO;;AAAA;AAAA,UAC5B,GAAG;AAAA,UACH,SAAS,EAAE,QAAQ,IAAI,UAAU;AAAA,UACjC,eAAe,EAAE,cAAc,IAAI,UAAU;AAAA,UAC7C,iBAAgB,OAAE,mBAAF,mBAAkB,IAAI;AAAA,QAAU;AAAA,OAChD;AAEF,aAAO,KAAK,KAAK,EAAE,MAAM,6BAAM,MAAM;AAAA,IACvC;AAEA,SAAA,kBAAkB,CAAC,QAAuC;AACxD,YAAMJ,YAAW;AAEb,UAAA,CAACA,UAAS,MAAM;AAClB,QAAAA,UAAS,OAAO,KAAK,cAAcA,SAAe,EAAE;AAAA,MAAA;AAG/C,aAAAA;AAAA,IACT;AAEA,SAAA,aAAiC,CAAC,SAAS;AACzC,YAAM,SAAS,6BAAM;AACrB,UAAI,WAAW,QAAW;AACnB,aAAA,QAAQ,SAAS,CAAC,MAAM;AACpB,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,eAAe,EAAE,cAAc;AAAA,cAC7B,CAAC,MAAM,CAAC,OAAO,CAA8B;AAAA,YAAA;AAAA,UAEjD;AAAA,QAAA,CACD;AAAA,MAAA,OACI;AACA,aAAA,QAAQ,SAAS,CAAC,MAAM;AACpB,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,eAAe,CAAA;AAAA,UACjB;AAAA,QAAA,CACD;AAAA,MAAA;AAAA,IAEL;AAEA,SAAA,oBAAoB,MAAM;AAElB,YAAA,SAAS,CAAC,MAAkC;AAChD,cAAM,QAAQ,KAAK,gBAAgB,EAAE,OAAO;AAExC,YAAA,CAAC,MAAM,QAAQ,QAAQ;AAClB,iBAAA;AAAA,QAAA;AAKT,cAAM,UACH,EAAE,UACE,MAAM,QAAQ,iBAAiB,KAAK,QAAQ,uBAC5C,MAAM,QAAQ,UAAU,KAAK,QAAQ,kBAC1C,IAAI,KAAK;AAEJ,eAAA,EAAE,EAAE,WAAW,WAAW,KAAK,QAAQ,EAAE,YAAY;AAAA,MAC9D;AACK,WAAA,WAAW,EAAE,QAAQ;AAAA,IAC5B;AAEA,SAAA,iBAAiB,CAAC,UAAoB;AAChC,UAAA,MAAM,iBAAiB,QAAW;AACpC,YAAI,MAAM,QAAQ;AAChB,gBAAM,eAAe,MAAM,OAAA,EAAS,KAAK,CAAC,cAAc;AAEtD,kBAAM,EAAE,IAAI,KAAK,GAAGU,aAAY,UAAU;AACnC,mBAAA,OAAO,MAAM,SAASA,QAAO;AAAA,UAAA,CACrC;AAAA,QAAA,OACI;AACC,gBAAA,eAAe,QAAQ,QAAQ;AAAA,QAAA;AAAA,MACvC;AAME,UAAA,MAAM,uBAAuB,QAAW;AACpC,cAAA,qBAAqB,MAAM,aAAa;AAAA,UAAK,MACjD,QAAQ;AAAA,YACN,eAAe,IAAI,OAAO,SAAS;AAC3B,oBAAA,YAAY,MAAM,QAAQ,IAAI;AACpC,kBAAK,uCAAmB,SAAS;AAC/B,sBAAO,UAAkB,QAAQ;AAAA,cAAA;AAAA,YAEpC,CAAA;AAAA,UAAA;AAAA,QAEL;AAAA,MAAA;AAEF,aAAO,MAAM;AAAA,IACf;AAEA,SAAA,eAKI,OAAO,SAAS;AACZ,YAAA,OAAO,KAAK,cAAc,IAAW;AAEvC,UAAA,UAAU,KAAK,YAAY,MAAM;AAAA,QACnC,cAAc;AAAA,QACd,SAAS;AAAA,QACT,MAAM;AAAA,MAAA,CACP;AAED,YAAM,iBAAiB,IAAI;AAAA,QACzB,CAAC,GAAG,KAAK,MAAM,SAAS,GAAI,KAAK,MAAM,kBAAkB,CAAG,CAAA,EAAE;AAAA,UAC5D,CAAC,MAAM,EAAE;AAAA,QAAA;AAAA,MAEb;AAEM,YAAA,qCAAqB,IAAI;AAAA,QAC7B,GAAG;AAAA,QACH,GAAG,KAAK,MAAM,cAAc,IAAI,CAAC,MAAM,EAAE,EAAE;AAAA,MAAA,CAC5C;AAGDR,YAAAA,MAAM,MAAM;AACF,gBAAA,QAAQ,CAAC,UAAU;AACzB,cAAI,CAAC,eAAe,IAAI,MAAM,EAAE,GAAG;AAC5B,iBAAA,QAAQ,SAAS,CAAC,OAAO;AAAA,cAC5B,GAAG;AAAA,cACH,eAAe,CAAC,GAAI,EAAE,eAAuB,KAAK;AAAA,YAAA,EAClD;AAAA,UAAA;AAAA,QACJ,CACD;AAAA,MAAA,CACF;AAEG,UAAA;AACQ,kBAAA,MAAM,KAAK,YAAY;AAAA,UAC/B;AAAA,UACA,UAAU;AAAA,UACV,SAAS;AAAA,UACT,aAAa,CAAC,IAAI,YAAY;AAExB,gBAAA,eAAe,IAAI,EAAE,GAAG;AAChB,wBAAA,QAAQ,IAAI,CAAC,MAAO,EAAE,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAE;AAAA,YAAA,OACtD;AACA,mBAAA,YAAY,IAAI,OAAO;AAAA,YAAA;AAAA,UAC9B;AAAA,QACF,CACD;AAEM,eAAA;AAAA,eACA,KAAK;AACR,YAAAG,SAAAA,WAAW,GAAG,GAAG;AACnB,cAAI,IAAI,gBAAgB;AACf,mBAAA;AAAA,UAAA;AAEF,iBAAA,MAAM,KAAK,aAAa;AAAA,YAC7B,GAAI;AAAA,YACJ,eAAe;AAAA,UAAA,CAChB;AAAA,QAAA;AAEC,YAAA,CAACD,SAAAA,WAAW,GAAG,GAAG;AAEpB,kBAAQ,MAAM,GAAG;AAAA,QAAA;AAEZ,eAAA;AAAA,MAAA;AAAA,IAEX;AAOI,SAAA,aAAA,CAAC,UAAU,SAAS;AACtB,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,IAAI,SAAS,KACT,KAAK;AAAA,UACF,SAAS,QAAQ;AAAA,UAClB,SAAS;AAAA,QAAA,IAEX;AAAA,QACJ,QAAQ,SAAS,UAAU,CAAC;AAAA,QAC5B,aAAa;AAAA,MACf;AACM,YAAA,OAAO,KAAK,cAAc,aAAoB;AAEpD,WAAI,6BAAM,YAAW,KAAK,MAAM,WAAW,WAAW;AAC7C,eAAA;AAAA,MAAA;AAGH,YAAA,WACJ,6BAAM,aAAY,SAAY,CAAC,KAAK,MAAM,YAAY,KAAK;AAEvD,YAAA,eAAe,UACjB,KAAK,iBACL,KAAK,MAAM,oBAAoB,KAAK,MAAM;AAE9C,YAAM,QAAQlB,KAAAA,cAAc,KAAK,UAAU,aAAa,UAAU;AAAA,QAChE,GAAG;AAAA,QACH,IAAI,KAAK;AAAA,MAAA,CACV;AAED,UAAI,CAAC,OAAO;AACH,eAAA;AAAA,MAAA;AAET,UAAI,SAAS,QAAQ;AACf,YAAA,CAACW,MAAAA,UAAU,OAAO,SAAS,QAAQ,EAAE,SAAS,KAAK,CAAC,GAAG;AAClD,iBAAA;AAAA,QAAA;AAAA,MACT;AAGE,UAAA,WAAU,6BAAM,kBAAiB,OAAO;AACnC,eAAAA,gBAAU,aAAa,QAAQ,KAAK,QAAQ,EAAE,SAAS,KAAA,CAAM,IAChE,QACA;AAAA,MAAA;AAGC,aAAA;AAAA,IACT;AAuBkB,SAAA,kBAAA,CAChB,SACA,KACA;AAAA,MACE,cAAc,KAAK;AAAA,IACrB,IAKI,OACD;;AAGH,YAAM,cAAc,KAAK,WAAW,IAAI,WAAW,EAAE,KAAK,KAAK;AAC/D,YAAM,mBAAkD,CAAC;AAGzD,iBAAW,SAAS,SAAS;AACV,yBAAA,MAAM,OAAO,IAAI;AAAA,MAAA;AAIpC,UACE,CAAC,YAAY,QAAQ,uBACpB,UAAK,YAAL,mBAAsB,2BACvB;AACY,oBAAA,QAAQ,oBAClB,KAAK,QACL;AAAA,MAAA;AAIJ;AAAA,QACE,YAAY,QAAQ;AAAA,QACpB;AAAA,MACF;AAGM,YAAA,gBAAgB,iBAAiB,YAAY,EAAE;AAErD;AAAA,QACE;AAAA,QACA,qCAAqC,YAAY;AAAA,MACnD;AAGY,kBAAA,cAAc,IAAI,CAAC,UAAU;AAAA,QACvC,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY;AAAA,MAAA,EACZ;AAEF,UAAK,IAAY,eAAe,iBAAiB,YAAY,aAAa;AACpE,YAAA,UAAU,YAAY,YAAY;AACjC,aAAA,gBAAgB,SAAS,KAAK;AAAA,UACjC;AAAA,QAAA,CACD;AAAA,MAAA;AAAA,IAEL;AAEA,SAAA,mBAAmB,MAAM;AAChB,aAAA,KAAK,QAAQ,MAAM,QAAQ;AAAA,QAChC,CAAC,MAAM,EAAE,WAAW,cAAc,EAAE;AAAA,MACtC;AAAA,IACF;AAnwEE,SAAK,OAAO;AAAA,MACV,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,SAAS;AAAA,MACT,GAAG;AAAA,MACH,eAAe,QAAQ,iBAAiB;AAAA,MACxC,cAAc,QAAQ,gBAAgB;AAAA,MACtC,iBAAiB,QAAQ,mBAAmBc,aAAA;AAAA,MAC5C,aAAa,QAAQ,eAAeC,aAAAA;AAAAA,IAAA,CACrC;AAEG,QAAA,OAAO,aAAa,aAAa;AACjC,aAAe,iBAAiB;AAAA,IAAA;AAAA,EACpC;AAAA,EAkGF,IAAI,QAAQ;AACV,WAAO,KAAK,QAAQ;AAAA,EAAA;AAAA,EAuMtB,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EAAA;AAAA,EA8BN,oBACN,MACA,MACsB;AACtB,UAAM,EAAE,YAAY,eAAe,gBAAgB,KAAK;AAAA,MACtD;AAAA,MACA,6BAAM;AAAA,IACR;AACA,QAAI,mBAAmB;AAGvB;AAAA;AAAA,MAEE,aACI,WAAW,SAAS,OAAO,YAAY,IAAI;AAAA;AAAA,QAE3CjC,KAAA,cAAc,KAAK,QAAQ;AAAA;AAAA,MAC/B;AAEI,UAAA,KAAK,QAAQ,eAAe;AAChB,sBAAA,KAAK,KAAK,QAAQ,aAAa;AAAA,MAAA,OACxC;AAEc,2BAAA;AAAA,MAAA;AAAA,IACrB;AAGF,UAAM,yBAAyB,MAAM;AACnC,UAAI,CAAC,kBAAkB;AACd,eAAA;AAAA,MAAA;AAGL,UAAA,KAAK,QAAQ,iBAAiB,QAAQ;AACxC,iBAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,gBAAA,QAAQ,cAAc,CAAC;AAC7B,cAAI,MAAM,UAAU;AAClB,mBAAO,MAAM;AAAA,UAAA;AAAA,QACf;AAAA,MACF;AAGK,aAAAQ,KAAA;AAAA,IAAA,GACN;AAEH,UAAM,cAAc,cAAc,IAAI,CAAC,UAAU;;AAC3C,UAAA;AAEJ,YAAM,gBACJ,WAAM,QAAQ,WAAd,mBAAsB,UAAS,MAAM,QAAQ;AAE/C,UAAI,aAAa;AACX,YAAA;AACI,gBAAA,eAAe,YAAY,WAAW;AAErC,iBAAA,OAAO,aAAa,YAAY;AAAA,iBAChC,KAAU;AACG,8BAAA,IAAI,eAAe,IAAI,SAAS;AAAA,YAClD,OAAO;AAAA,UAAA,CACR;AAED,cAAI,6BAAM,cAAc;AAChB,kBAAA;AAAA,UAAA;AAGD,iBAAA;AAAA,QAAA;AAAA,MACT;AAGF;AAAA,IAAA,CACD;AAED,UAAM,UAAgC,CAAC;AAEjC,UAAA,mBAAmB,CAAC,gBAAgC;AACxD,YAAM,gBAAgB,2CAAa;AAEnC,YAAM,gBAAgB,CAAC,gBACjB,KAAK,QAAQ,WAAmB,KACjC,YAAY,WAAW,KAAK,QAAQ,WAAW,CAAC;AAE9C,aAAA;AAAA,IACT;AAEc,kBAAA,QAAQ,CAAC,OAAO,UAAU;;AAQhC,YAAA,cAAc,QAAQ,QAAQ,CAAC;AAErC,YAAM,CAAC,gBAAgB,mBAAmB,WAAW,KAIhD,MAAM;AAEH,cAAA,gBAAe,2CAAa,WAAU,KAAK;AAC3C,cAAA,sBAAqB,2CAAa,kBAAiB,CAAC;AAEtD,YAAA;AACI,gBAAA,eACJ,eAAe,MAAM,QAAQ,gBAAgB,EAAE,GAAG,aAAc,CAAA,KAChE,CAAC;AAEI,iBAAA;AAAA,YACL;AAAA,cACE,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,YACA,EAAE,GAAG,oBAAoB,GAAG,aAAa;AAAA,YACzC;AAAA,UACF;AAAA,iBACO,KAAU;AACjB,cAAI,mBAAmB;AACnB,cAAA,EAAE,eAAe,mBAAmB;AACnB,+BAAA,IAAI,iBAAiB,IAAI,SAAS;AAAA,cACnD,OAAO;AAAA,YAAA,CACR;AAAA,UAAA;AAGH,cAAI,6BAAM,cAAc;AAChB,kBAAA;AAAA,UAAA;AAGR,iBAAO,CAAC,cAAc,CAAC,GAAG,gBAAgB;AAAA,QAAA;AAAA,MAC5C,GACC;AAOG,YAAA,eACJ,iBAAM,SAAQ,eAAd,4BAA2B;AAAA,QACzB,QAAQ;AAAA,MACT,OAAK;AAER,YAAM,iBAAiB,aAAa,KAAK,UAAU,UAAU,IAAI;AAEjE,YAAM,EAAE,YAAY,iBAAiB,IAAIE,qBAAgB;AAAA,QACvD,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,eAAe,KAAK;AAAA,MAAA,CACrB;AAED,YAAM,UACJA,KAAAA,gBAAgB;AAAA,QACd,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,eAAe,KAAK;AAAA,MAAA,CACrB,EAAE,mBAAmB;AAQlB,YAAA,gBAAgB,KAAK,SAAS,OAAO;AAErC,YAAA,gBAAgB,KAAK,MAAM,QAAQ;AAAA,QACvC,CAAC,MAAM,EAAE,YAAY,MAAM;AAAA,MAC7B;AAEM,YAAA,QAAQ,gBAAgB,SAAS;AAEnC,UAAA;AAEJ,UAAI,eAAe;AACT,gBAAA;AAAA,UACN,GAAG;AAAA,UACH;AAAA,UACA,QAAQ,gBACJP,MAAA,iBAAiB,cAAc,QAAQ,WAAW,IAClD;AAAA,UACJ,eAAe;AAAA,UACf,QAAQ,gBACJA,uBAAiB,cAAc,QAAQ,cAAc,IACrDA,uBAAiB,cAAc,QAAQ,cAAc;AAAA,UACzD,eAAe;AAAA,QACjB;AAAA,MAAA,OACK;AACL,cAAM,SACJ,MAAM,QAAQ,UACd,MAAM,QAAQ,cACd,MAAM,UACN,kBAAkB,KAAK,IACnB,YACA;AAEE,gBAAA;AAAA,UACN,IAAI;AAAA,UACJ;AAAA,UACA,SAAS,MAAM;AAAA,UACf,QAAQ,gBACJA,MAAA,iBAAiB,cAAc,QAAQ,WAAW,IAClD;AAAA,UACJ,eAAe;AAAA,UACf,UAAUS,KAAAA,UAAU,CAAC,KAAK,UAAU,gBAAgB,CAAC;AAAA,UACrD,WAAW,KAAK,IAAI;AAAA,UACpB,QAAQ,gBACJT,MAAA,iBAAiB,cAAc,QAAQ,cAAc,IACrD;AAAA,UACJ,eAAe;AAAA,UACf,aAAa;AAAA,UACb;AAAA,UACA,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,aAAa,YAAY,KAAK;AAAA,UAC9B,gBAAgB,CAAC;AAAA,UACjB,qBAAqB,CAAC;AAAA,UACtB,SAAS,CAAC;AAAA,UACV,iBAAiB,IAAI,gBAAgB;AAAA,UACrC,YAAY;AAAA,UACZ;AAAA,UACA,YAAY,gBACRA,MAAA,iBAAiB,cAAc,YAAY,UAAU,IACrD;AAAA,UACJ,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,MAAM;AAAA,UACN,YAAY,MAAM,QAAQ,cAAc,CAAC;AAAA,UACzC,aAAagB,MAAAA,wBAAwB;AAAA,UACrC,UAAU,MAAM;AAAA,QAClB;AAAA,MAAA;AAGE,UAAA,EAAC,6BAAM,UAAS;AAEZ,cAAA,iBAAiB,0BAA0B,MAAM;AAAA,MAAA;AAIzD,YAAM,cAAc;AAEd,YAAA,gBAAgB,iBAAiB,WAAW;AAElD,YAAM,UAAU;AAAA,QACd,GAAG;AAAA,QACH,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACX;AAEA,cAAQ,KAAK,KAAK;AAAA,IAAA,CACnB;AAEO,YAAA,QAAQ,CAAC,OAAO,UAAU;;AAChC,YAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAChD,YAAM,gBAAgB,KAAK,SAAS,MAAM,EAAE;AAG5C,UAAI,CAAC,kBAAiB,6BAAM,oBAAmB,MAAM;AAC7C,cAAA,cAAc,QAAQ,QAAQ,CAAC;AAC/B,cAAA,gBAAgB,iBAAiB,WAAW;AAGlD,cAAM,mBAA4D;AAAA,UAChE,MAAM,MAAM;AAAA,UACZ,QAAQ,MAAM;AAAA,UACd,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU,CAACe,UACT,KAAK,SAAS,EAAE,GAAGA,OAAM,eAAe,MAAM;AAAA,UAChD,eAAe,KAAK;AAAA,UACpB,OAAO,MAAM;AAAA,UACb,iBAAiB,MAAM;AAAA,UACvB,SAAS,CAAC,CAAC,MAAM;AAAA,UACjB;AAAA,QACF;AAGA,cAAM,mBAAiB,iBAAM,SAAQ,YAAd,4BAAwB,sBAAqB,CAAC;AAErE,cAAM,UAAU;AAAA,UACd,GAAG;AAAA,UACH,GAAG,MAAM;AAAA,UACT,GAAG,MAAM;AAAA,QACX;AAAA,MAAA;AAAA,IACF,CACD;AAEM,WAAA;AAAA,EAAA;AA4oDX;AAEO,MAAM,yBAAyB,MAAM;AAAC;AAEtC,MAAM,uBAAuB,MAAM;AAAC;AAK3B,SAAA,OAGd,IAAsB,KAAY;AAClC,SAAO,UACF,SACuC;AACpC,UAAA,WAAW,MAAM,GAAG;AAC1B,WAAO,SAAS,OAAO,SAAS,EAAE,GAAG,IAAI;AAAA,EAC3C;AACF;AAEO,SAAS,sBACd,UACkB;AACX,SAAA;AAAA,IACL,UAAU;AAAA,IACV,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB;AAAA,IACA,SAAS,CAAC;AAAA,IACV,gBAAgB,CAAC;AAAA,IACjB,eAAe,CAAC;AAAA,IAChB,YAAY;AAAA,EACd;AACF;AAEA,SAAS,eAAeC,iBAA8B,OAAyB;AACzEA,MAAAA,mBAAkB,KAAM,QAAO,CAAC;AAEpC,MAAI,eAAeA,iBAAgB;AACjC,UAAM,SAASA,gBAAe,WAAW,EAAE,SAAS,KAAK;AAEzD,QAAI,kBAAkB;AACd,YAAA,IAAI,iBAAiB,gCAAgC;AAE7D,QAAI,OAAO;AACH,YAAA,IAAI,iBAAiB,KAAK,UAAU,OAAO,QAAQ,QAAW,CAAC,GAAG;AAAA,QACtE,OAAO;AAAA,MAAA,CACR;AAEH,WAAO,OAAO;AAAA,EAAA;AAGhB,MAAI,WAAWA,iBAAgB;AACtBA,WAAAA,gBAAe,MAAM,KAAK;AAAA,EAAA;AAG/B,MAAA,OAAOA,oBAAmB,YAAY;AACxC,WAAOA,gBAAe,KAAK;AAAA,EAAA;AAG7B,SAAO,CAAC;AACV;AAEO,MAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,kBAAkB,OAAiB;;AAC1C,aAAW,iBAAiB,gBAAgB;AAC1C,SAAK,WAAM,QAAQ,aAAa,MAA3B,mBAAsC,SAAS;AAC3C,aAAA;AAAA,IAAA;AAAA,EACT;AAEK,SAAA;AACT;;;;;;;;;"}