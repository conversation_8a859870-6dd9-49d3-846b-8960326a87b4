import { generateNanoId } from "../utils/helper.js";
import urlSchema from "../models/short_url.model.js";
import { saveShortUrl } from "../dao/short_url.js";
export const createShortUrlServicesWithoutUser = async (url) => {
  const shortUrl = generateNanoId(7); // Remove await - generateNanoId is synchronous
  if (!shortUrl) {
    throw new Error("Failed to generate short url");
  }
  await saveShortUrl(shortUrl, url);

  return shortUrl;
};

export const createShortUrlServicesWithUser = async (url, userId) => {
  const shortUrl = generateNanoId(7); // Remove await - generateNanoId is synchronous
  await saveShortUrl(shortUrl, url, userId); // Fix parameter order

  return shortUrl;
};
