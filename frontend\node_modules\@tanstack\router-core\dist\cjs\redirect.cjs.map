{"version": 3, "file": "redirect.cjs", "sources": ["../../src/redirect.ts"], "sourcesContent": ["import type { NavigateOptions } from './link'\nimport type { <PERSON><PERSON>out<PERSON>, RegisteredRouter } from './router'\nimport type { PickAsRequired } from './utils'\n\nexport type AnyRedirect = Redirect<any, any, any, any, any>\n\n/**\n * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType)\n */\nexport type Redirect<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = {\n  href?: string\n  /**\n   * @deprecated Use `statusCode` instead\n   **/\n  code?: number\n  /**\n   * The HTTP status code to use when redirecting.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType#statuscode-property)\n   */\n  statusCode?: number\n  /**\n   * If provided, will throw the redirect object instead of returning it. This can be useful in places where `throwing` in a function might cause it to have a return type of `never`. In that case, you can use `redirect({ throw: true })` to throw the redirect object instead of returning it.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType#throw-property)\n   */\n  throw?: any\n  /**\n   * The HTTP headers to use when redirecting.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType#headers-property)\n   */\n  headers?: HeadersInit\n} & NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>\n\nexport type ResolvedRedirect<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string = '',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = PickAsRequired<\n  Redirect<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n  'code' | 'statusCode' | 'headers'\n> & {\n  href: string\n}\n\nexport function redirect<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TTo extends string | undefined = '.',\n  const TFrom extends string = string,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  opts: Redirect<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n): Redirect<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> {\n  ;(opts as any).isRedirect = true\n  opts.statusCode = opts.statusCode || opts.code || 307\n  opts.headers = opts.headers || {}\n  if (!opts.reloadDocument) {\n    opts.reloadDocument = false\n    try {\n      new URL(`${opts.href}`)\n      opts.reloadDocument = true\n    } catch {}\n  }\n\n  if (opts.throw) {\n    throw opts\n  }\n\n  return opts\n}\n\nexport function isRedirect(obj: any): obj is AnyRedirect {\n  return !!obj?.isRedirect\n}\n\nexport function isResolvedRedirect(obj: any): obj is ResolvedRedirect {\n  return !!obj?.isRedirect && obj.href\n}\n"], "names": [], "mappings": ";;AAmDO,SAAS,SAOd,MACmD;AACjD,OAAa,aAAa;AAC5B,OAAK,aAAa,KAAK,cAAc,KAAK,QAAQ;AAC7C,OAAA,UAAU,KAAK,WAAW,CAAC;AAC5B,MAAA,CAAC,KAAK,gBAAgB;AACxB,SAAK,iBAAiB;AAClB,QAAA;AACF,UAAI,IAAI,GAAG,KAAK,IAAI,EAAE;AACtB,WAAK,iBAAiB;AAAA,IAAA,QAChB;AAAA,IAAA;AAAA,EAAC;AAGX,MAAI,KAAK,OAAO;AACR,UAAA;AAAA,EAAA;AAGD,SAAA;AACT;AAEO,SAAS,WAAW,KAA8B;AAChD,SAAA,CAAC,EAAC,2BAAK;AAChB;AAEO,SAAS,mBAAmB,KAAmC;AACpE,SAAO,CAAC,EAAC,2BAAK,eAAc,IAAI;AAClC;;;;"}