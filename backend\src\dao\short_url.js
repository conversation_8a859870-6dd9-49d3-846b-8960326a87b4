import urlSchema from "../models/short_url.model.js";
import { ConflictError } from "../utils/errorHandler.js";

export const saveShortUrl = async (shortUrl, longUrl, userId) => {
  try {
    const newUrl = new urlSchema({
      short_url: shortUrl,
      full_url: longUrl,
    });

    if (userId) {
      newUrl.user = userId;
    }

    return await newUrl.save(); // Return the saved document
  } catch (err) {
    console.log(err);
    throw new ConflictError(err.message || "Failed to save URL");
  }
};

export const findUrlFromShortUrl = async (shorturl) => {
  return await urlSchema.findOneAndUpdate(
    { short_url: shorturl },
    { $inc: { clicks: 1 } },
    { new: true } // Return the updated document
  );
};
