import { NavigateOptions, ToOptions } from './link.cjs';
import { ParsedLocation } from './location.cjs';
import { RoutePaths } from './routeInfo.cjs';
import { RegisteredRouter, ViewTransitionOptions } from './router.cjs';
export interface MatchLocation {
    to?: string | number | null;
    fuzzy?: boolean;
    caseSensitive?: boolean;
    from?: string;
}
export interface CommitLocationOptions {
    replace?: boolean;
    resetScroll?: boolean;
    hashScrollIntoView?: boolean | ScrollIntoViewOptions;
    viewTransition?: boolean | ViewTransitionOptions;
    /**
     * @deprecated All navigations use React transitions under the hood now
     **/
    startTransition?: boolean;
    ignoreBlocker?: boolean;
}
export type NavigateFn = <TRouter extends RegisteredRouter, TTo extends string | undefined, TFrom extends RoutePaths<TRouter['routeTree']> | string = string, TMaskFrom extends RoutePaths<TRouter['routeTree']> | string = TFrom, TMaskTo extends string = ''>(opts: NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>) => Promise<void> | void;
export type BuildLocationFn = <TRouter extends RegisteredRouter, TTo extends string | undefined, TFrom extends RoutePaths<TRouter['routeTree']> | string = string, TMaskFrom extends RoutePaths<TRouter['routeTree']> | string = TFrom, TMaskTo extends string = ''>(opts: ToOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & {
    leaveParams?: boolean;
    _includeValidateSearch?: boolean;
}) => ParsedLocation;
