{"version": 3, "file": "Transitioner.js", "sources": ["../../src/Transitioner.tsx"], "sourcesContent": ["import * as React from 'react'\nimport {\n  getLocationChangeInfo,\n  handleHashScroll,\n  trimPathRight,\n} from '@tanstack/router-core'\nimport { useLayoutEffect, usePrevious } from './utils'\nimport { useRouter } from './useRouter'\nimport { useRouterState } from './useRouterState'\n\nexport function Transitioner() {\n  const router = useRouter()\n  const mountLoadForRouter = React.useRef({ router, mounted: false })\n  const isLoading = useRouterState({\n    select: ({ isLoading }) => isLoading,\n  })\n\n  const [isTransitioning, setIsTransitioning] = React.useState(false)\n  // Track pending state changes\n  const hasPendingMatches = useRouterState({\n    select: (s) => s.matches.some((d) => d.status === 'pending'),\n    structuralSharing: true,\n  })\n\n  const previousIsLoading = usePrevious(isLoading)\n\n  const isAnyPending = isLoading || isTransitioning || hasPendingMatches\n  const previousIsAnyPending = usePrevious(isAnyPending)\n\n  const isPagePending = isLoading || hasPendingMatches\n  const previousIsPagePending = usePrevious(isPagePending)\n\n  if (!router.isServer) {\n    router.startTransition = (fn: () => void) => {\n      setIsTransitioning(true)\n      React.startTransition(() => {\n        fn()\n        setIsTransitioning(false)\n      })\n    }\n  }\n\n  // Subscribe to location changes\n  // and try to load the new location\n  React.useEffect(() => {\n    const unsub = router.history.subscribe(router.load)\n\n    const nextLocation = router.buildLocation({\n      to: router.latestLocation.pathname,\n      search: true,\n      params: true,\n      hash: true,\n      state: true,\n      _includeValidateSearch: true,\n    })\n\n    if (\n      trimPathRight(router.latestLocation.href) !==\n      trimPathRight(nextLocation.href)\n    ) {\n      router.commitLocation({ ...nextLocation, replace: true })\n    }\n\n    return () => {\n      unsub()\n    }\n  }, [router, router.history])\n\n  // Try to load the initial location\n  useLayoutEffect(() => {\n    if (\n      (typeof window !== 'undefined' && router.clientSsr) ||\n      (mountLoadForRouter.current.router === router &&\n        mountLoadForRouter.current.mounted)\n    ) {\n      return\n    }\n    mountLoadForRouter.current = { router, mounted: true }\n\n    const tryLoad = async () => {\n      try {\n        await router.load()\n      } catch (err) {\n        console.error(err)\n      }\n    }\n\n    tryLoad()\n  }, [router])\n\n  useLayoutEffect(() => {\n    // The router was loading and now it's not\n    if (previousIsLoading && !isLoading) {\n      router.emit({\n        type: 'onLoad', // When the new URL has committed, when the new matches have been loaded into state.matches\n        ...getLocationChangeInfo(router.state),\n      })\n    }\n  }, [previousIsLoading, router, isLoading])\n\n  useLayoutEffect(() => {\n    // emit onBeforeRouteMount\n    if (previousIsPagePending && !isPagePending) {\n      router.emit({\n        type: 'onBeforeRouteMount',\n        ...getLocationChangeInfo(router.state),\n      })\n    }\n  }, [isPagePending, previousIsPagePending, router])\n\n  useLayoutEffect(() => {\n    // The router was pending and now it's not\n    if (previousIsAnyPending && !isAnyPending) {\n      router.emit({\n        type: 'onResolved',\n        ...getLocationChangeInfo(router.state),\n      })\n\n      router.__store.setState((s) => ({\n        ...s,\n        status: 'idle',\n        resolvedLocation: s.location,\n      }))\n\n      handleHashScroll(router)\n    }\n  }, [isAnyPending, previousIsAnyPending, router])\n\n  return null\n}\n"], "names": ["isLoading"], "mappings": ";;;;;AAUO,SAAS,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,qBAAqB,MAAM,OAAO,EAAE,QAAQ,SAAS,OAAO;AAClE,QAAM,YAAY,eAAe;AAAA,IAC/B,QAAQ,CAAC,EAAE,WAAAA,iBAAgBA;AAAAA,EAAA,CAC5B;AAED,QAAM,CAAC,iBAAiB,kBAAkB,IAAI,MAAM,SAAS,KAAK;AAElE,QAAM,oBAAoB,eAAe;AAAA,IACvC,QAAQ,CAAC,MAAM,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,SAAS;AAAA,IAC3D,mBAAmB;AAAA,EAAA,CACpB;AAEK,QAAA,oBAAoB,YAAY,SAAS;AAEzC,QAAA,eAAe,aAAa,mBAAmB;AAC/C,QAAA,uBAAuB,YAAY,YAAY;AAErD,QAAM,gBAAgB,aAAa;AAC7B,QAAA,wBAAwB,YAAY,aAAa;AAEnD,MAAA,CAAC,OAAO,UAAU;AACb,WAAA,kBAAkB,CAAC,OAAmB;AAC3C,yBAAmB,IAAI;AACvB,YAAM,gBAAgB,MAAM;AACvB,WAAA;AACH,2BAAmB,KAAK;AAAA,MAAA,CACzB;AAAA,IACH;AAAA,EAAA;AAKF,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,QAAQ,UAAU,OAAO,IAAI;AAE5C,UAAA,eAAe,OAAO,cAAc;AAAA,MACxC,IAAI,OAAO,eAAe;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,wBAAwB;AAAA,IAAA,CACzB;AAGC,QAAA,cAAc,OAAO,eAAe,IAAI,MACxC,cAAc,aAAa,IAAI,GAC/B;AACA,aAAO,eAAe,EAAE,GAAG,cAAc,SAAS,MAAM;AAAA,IAAA;AAG1D,WAAO,MAAM;AACL,YAAA;AAAA,IACR;AAAA,EACC,GAAA,CAAC,QAAQ,OAAO,OAAO,CAAC;AAG3B,kBAAgB,MAAM;AAEjB,QAAA,OAAO,WAAW,eAAe,OAAO,aACxC,mBAAmB,QAAQ,WAAW,UACrC,mBAAmB,QAAQ,SAC7B;AACA;AAAA,IAAA;AAEF,uBAAmB,UAAU,EAAE,QAAQ,SAAS,KAAK;AAErD,UAAM,UAAU,YAAY;AACtB,UAAA;AACF,cAAM,OAAO,KAAK;AAAA,eACX,KAAK;AACZ,gBAAQ,MAAM,GAAG;AAAA,MAAA;AAAA,IAErB;AAEQ,YAAA;AAAA,EAAA,GACP,CAAC,MAAM,CAAC;AAEX,kBAAgB,MAAM;AAEhB,QAAA,qBAAqB,CAAC,WAAW;AACnC,aAAO,KAAK;AAAA,QACV,MAAM;AAAA;AAAA,QACN,GAAG,sBAAsB,OAAO,KAAK;AAAA,MAAA,CACtC;AAAA,IAAA;AAAA,EAEF,GAAA,CAAC,mBAAmB,QAAQ,SAAS,CAAC;AAEzC,kBAAgB,MAAM;AAEhB,QAAA,yBAAyB,CAAC,eAAe;AAC3C,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,GAAG,sBAAsB,OAAO,KAAK;AAAA,MAAA,CACtC;AAAA,IAAA;AAAA,EAEF,GAAA,CAAC,eAAe,uBAAuB,MAAM,CAAC;AAEjD,kBAAgB,MAAM;AAEhB,QAAA,wBAAwB,CAAC,cAAc;AACzC,aAAO,KAAK;AAAA,QACV,MAAM;AAAA,QACN,GAAG,sBAAsB,OAAO,KAAK;AAAA,MAAA,CACtC;AAEM,aAAA,QAAQ,SAAS,CAAC,OAAO;AAAA,QAC9B,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,kBAAkB,EAAE;AAAA,MAAA,EACpB;AAEF,uBAAiB,MAAM;AAAA,IAAA;AAAA,EAExB,GAAA,CAAC,cAAc,sBAAsB,MAAM,CAAC;AAExC,SAAA;AACT;"}