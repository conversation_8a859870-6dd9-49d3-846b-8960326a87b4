{"version": 3, "file": "ScriptOnce.js", "sources": ["../../src/ScriptOnce.tsx"], "sourcesContent": ["import jsesc from 'jsesc'\n\nexport function ScriptOnce({\n  children,\n  log,\n}: {\n  children: string\n  log?: boolean\n  sync?: boolean\n}) {\n  if (typeof document !== 'undefined') {\n    return null\n  }\n\n  return (\n    <script\n      className=\"tsr-once\"\n      dangerouslySetInnerHTML={{\n        __html: [\n          children,\n          (log ?? true) && process.env.NODE_ENV === 'development'\n            ? `console.info(\\`Injected From Server:\n${jsesc(children.toString(), { quotes: 'backtick' })}\\`)`\n            : '',\n          'if (typeof __TSR_SSR__ !== \"undefined\") __TSR_SSR__.cleanScripts()',\n        ]\n          .filter(Boolean)\n          .join('\\n'),\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;AAEO,SAAS,WAAW;AAAA,EACzB;AAAA,EACA;AACF,GAIG;AACG,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;AAAA,EAAA;AAIP,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAU;AAAA,MACV,yBAAyB;AAAA,QACvB,QAAQ;AAAA,UACN;AAAA,WACC,OAAO,SAAS,QAAQ,IAAI,aAAa,gBACtC;AAAA,EACZ,MAAM,SAAS,YAAY,EAAE,QAAQ,WAAY,CAAA,CAAC,QACtC;AAAA,UACJ;AAAA,QAEC,EAAA,OAAO,OAAO,EACd,KAAK,IAAI;AAAA,MAAA;AAAA,IACd;AAAA,EACF;AAEJ;"}