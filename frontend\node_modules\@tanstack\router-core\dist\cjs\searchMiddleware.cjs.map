{"version": 3, "file": "searchMiddleware.cjs", "sources": ["../../src/searchMiddleware.ts"], "sourcesContent": ["import { deepEqual } from './utils'\nimport type { NoInfer, PickOptional } from './utils'\nimport type { SearchMiddleware } from './route'\nimport type { IsRequiredParams } from './link'\n\nexport function retainSearchParams<TSearchSchema extends object>(\n  keys: Array<keyof TSearchSchema> | true,\n): SearchMiddleware<TSearchSchema> {\n  return ({ search, next }) => {\n    const result = next(search)\n    if (keys === true) {\n      return { ...search, ...result }\n    }\n    // add missing keys from search to result\n    keys.forEach((key) => {\n      if (!(key in result)) {\n        result[key] = search[key]\n      }\n    })\n    return result\n  }\n}\n\nexport function stripSearchParams<\n  TSearchSchema,\n  TOptionalProps = PickOptional<NoInfer<TSearchSchema>>,\n  const TValues =\n    | Partial<NoInfer<TOptionalProps>>\n    | Array<keyof TOptionalProps>,\n  const TInput = IsRequiredParams<TSearchSchema> extends never\n    ? TValues | true\n    : TValues,\n>(input: NoInfer<TInput>): SearchMiddleware<TSearchSchema> {\n  return ({ search, next }) => {\n    if (input === true) {\n      return {}\n    }\n    const result = next(search) as Record<string, unknown>\n    if (Array.isArray(input)) {\n      input.forEach((key) => {\n        delete result[key]\n      })\n    } else {\n      Object.entries(input as Record<string, unknown>).forEach(\n        ([key, value]) => {\n          if (deepEqual(result[key], value)) {\n            delete result[key]\n          }\n        },\n      )\n    }\n    return result as any\n  }\n}\n"], "names": ["deepEqual"], "mappings": ";;;AAKO,SAAS,mBACd,MACiC;AACjC,SAAO,CAAC,EAAE,QAAQ,WAAW;AACrB,UAAA,SAAS,KAAK,MAAM;AAC1B,QAAI,SAAS,MAAM;AACjB,aAAO,EAAE,GAAG,QAAQ,GAAG,OAAO;AAAA,IAAA;AAG3B,SAAA,QAAQ,CAAC,QAAQ;AAChB,UAAA,EAAE,OAAO,SAAS;AACb,eAAA,GAAG,IAAI,OAAO,GAAG;AAAA,MAAA;AAAA,IAC1B,CACD;AACM,WAAA;AAAA,EACT;AACF;AAEO,SAAS,kBASd,OAAyD;AACzD,SAAO,CAAC,EAAE,QAAQ,WAAW;AAC3B,QAAI,UAAU,MAAM;AAClB,aAAO,CAAC;AAAA,IAAA;AAEJ,UAAA,SAAS,KAAK,MAAM;AACtB,QAAA,MAAM,QAAQ,KAAK,GAAG;AAClB,YAAA,QAAQ,CAAC,QAAQ;AACrB,eAAO,OAAO,GAAG;AAAA,MAAA,CAClB;AAAA,IAAA,OACI;AACE,aAAA,QAAQ,KAAgC,EAAE;AAAA,QAC/C,CAAC,CAAC,KAAK,KAAK,MAAM;AAChB,cAAIA,MAAU,UAAA,OAAO,GAAG,GAAG,KAAK,GAAG;AACjC,mBAAO,OAAO,GAAG;AAAA,UAAA;AAAA,QACnB;AAAA,MAEJ;AAAA,IAAA;AAEK,WAAA;AAAA,EACT;AACF;;;"}